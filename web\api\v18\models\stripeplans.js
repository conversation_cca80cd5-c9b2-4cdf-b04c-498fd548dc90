module.exports = (sequelize, DataTypes) => {
  const StripePlan = sequelize.define('StripePlan', {
    stripePlanName: DataTypes.STRING,
    stripePlanId: DataTypes.STRING,
    isDeleted: DataTypes.BOOLEAN,
    stripeProductId: DataTypes.STRING,
    stripeProductName: DataTypes.STRING,
    stripeAmount: DataTypes.INTEGER,
    PlanId: {
      type: DataTypes.INTEGER,
    },
    stripeCurrency: DataTypes.STRING,
    isActive: DataTypes.BOOLEAN,
  });
  StripePlan.associate = (models) => {
    StripePlan.belongsTo(models.Plan);
  };
  StripePlan.getAll = async (attr) => {
    const stripePlans = await StripePlan.findAll({
      include: ['Plan'],
      where: { ...attr },
    });
    return stripePlans;
  };
  StripePlan.associate = (models) => {
    StripePlan.belongsTo(models.Plan);
    return StripePlan;
  };
  StripePlan.getBy = async (attr) => {
    const stripePlan = await StripePlan.findOne({
      include: ['Plan'],
      where: { ...attr },
    });

    return stripePlan;
  };

  StripePlan.createPlan = async (attributes) => {
    const newPlan = await StripePlan.create(attributes);
    return newPlan;
  };
  return StripePlan;
};
