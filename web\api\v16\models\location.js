const { Sequelize } = require('sequelize');

const { Op } = Sequelize;
module.exports = (sequelize, DataTypes) => {
  const Locations = sequelize.define(
    'Locations',
    {
      locationName: DataTypes.STRING,
      notes: DataTypes.TEXT,
      ProjectId: DataTypes.INTEGER,
      LocationId: DataTypes.INTEGER,
      MemberId: DataTypes.INTEGER,
      ParentCompanyId: DataTypes.INTEGER,
      createdBy: {
        type: DataTypes.INTEGER,
        references: {
          model: 'Users', // name of Target model
          key: 'id', // key in Target model that we're referencing
        },
      },
      platform: DataTypes.STRING, // web Or mobile
      isDeleted: DataTypes.BOOLEAN,
      isActive: DataTypes.BOOLEAN,
      locationPath: DataTypes.STRING,
      isDefault: DataTypes.BOOLEAN,
    },
    {},
  );
  Locations.associate = (models) => {
    Locations.belongsTo(models.User, {
      as: 'userDetails',
      foreignKey: 'createdBy',
    });
    Locations.belongsTo(models.Project);
    Locations.hasMany(models.LocationNotificationPreferences, { foreignKey: 'LocationId' });
    Locations.hasMany(models.Locations, { as: 'paths', foreignKey: 'LocationId' });
    Locations.hasMany(models.Locations, { as: 'tier', foreignKey: 'LocationId' });
    return Locations;
  };

  Locations.get = async (attr, sort, sortByField, search) => {
    const sortByFieldName = sortByField || 'id';
    const sortByColumnType = sort || 'DESC';
    let commonSearch = {
      isActive: true,
      isDeleted: false,
      isDefault: false,
      ...attr,
    };
    if (search) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              { notes: { [Sequelize.Op.iLike]: `%${search}%` } },
              { locationName: { [Sequelize.Op.iLike]: `%${search}%` } },
              {
                '$paths.locationName$': {
                  [Sequelize.Op.iLike]: `%${search}%`,
                },
              },
              {
                '$paths.tier.locationName$': {
                  [Sequelize.Op.iLike]: `%${search}%`,
                },
              },
            ],
          },
        ],
      };
    }
    const getLocations = await Locations.findAll({
      subQuery: false,
      where: commonSearch,
      include: [
        {
          required: false,
          where: { isActive: true, isDeleted: false },
          association: 'paths',
          include: [
            {
              required: false,
              where: { isActive: true, isDeleted: false },
              association: 'tier',
            },
          ],
        },
      ],
      group: ['Locations.id', 'paths.id', 'paths.tier.id'],
      order: [[`${sortByFieldName}`, `${sortByColumnType}`]],
    });

    const defaultLocation = await Locations.findOne({
      where: {
        isActive: true,
        isDefault: true,
        ...attr,
      },
      include: [
        {
          required: false,
          where: { isActive: true, isDeleted: false },
          association: 'paths',
          include: [
            {
              required: false,
              where: { isActive: true, isDeleted: false },
              association: 'tier',
            },
          ],
        },
      ],
    });
    return { getLocations, defaultLocation };
  };

  Locations.getOne = async (attr) => {
    const commonSearch = {
      isActive: true,
      isDeleted: false,
      ...attr,
    };
    const getLocation = await Locations.findOne({
      subQuery: false,
      where: commonSearch,
      include: [
        {
          required: false,
          where: { isActive: true, isDeleted: false },
          association: 'paths',
          include: [
            {
              required: false,
              where: { isActive: true, isDeleted: false },
              association: 'tier',
            },
          ],
        },
      ],
      group: ['Locations.id', 'paths.id', 'paths.tier.id'],
    });
    return getLocation;
  };

  Locations.getLocations = async (attr) => {
    const sortByFieldName = 'id';
    const sortByColumnType = 'ASC';
    const getLocation = await Locations.findAll({
      where: attr,
      attributes: ['id', 'locationPath', 'isDefault'],
      order: [[`${sortByFieldName}`, `${sortByColumnType}`]],
    });
    return getLocation;
  };

  Locations.getLocationList = async (
    member,
    ProjectId,
    attr,
    sort,
    sortByField,
    search,
    // limit,
    // offset,
  ) => {
    const sortByFieldName = sortByField || 'id';
    const sortByColumnType = sort || 'DESC';
    let commonSearch = {
      isActive: true,
      isDeleted: false,
      isDefault: false,
      ...attr,
    };
    if (search) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              { notes: { [Sequelize.Op.iLike]: `%${search}%` } },
              { locationName: { [Sequelize.Op.iLike]: `%${search}%` } },
              {
                '$paths.locationName$': {
                  [Sequelize.Op.iLike]: `%${search}%`,
                },
              },
              {
                '$paths.tier.locationName$': {
                  [Sequelize.Op.iLike]: `%${search}%`,
                },
              },
            ],
          },
        ],
      };
    }
    const getLocations = await Locations.findAll({
      subQuery: false,
      where: commonSearch,
      include: [
        {
          required: false,
          where: { isDeleted: false, ProjectId, MemberId: member },
          association: 'LocationNotificationPreferences',
          attributes: ['id', 'follow', 'ProjectId', 'ParentCompanyId', 'MemberId', 'LocationId'],
        },
        {
          required: false,
          where: { isActive: true, isDeleted: false },
          association: 'paths',
          include: [
            {
              required: false,
              where: { isActive: true, isDeleted: false },
              association: 'tier',
              include: [
                {
                  required: false,
                  where: { isDeleted: false, ProjectId, MemberId: member },
                  association: 'LocationNotificationPreferences',
                  attributes: [
                    'id',
                    'follow',
                    'ProjectId',
                    'ParentCompanyId',
                    'MemberId',
                    'LocationId',
                  ],
                },
              ],
            },
            {
              required: false,
              where: { isDeleted: false, ProjectId, MemberId: member },
              association: 'LocationNotificationPreferences',
              attributes: [
                'id',
                'follow',
                'ProjectId',
                'ParentCompanyId',
                'MemberId',
                'LocationId',
              ],
            },
          ],
        },
      ],
      group: [
        'Locations.id',
        'LocationNotificationPreferences.id',
        'paths.id',
        'paths.LocationNotificationPreferences.id',
        'paths.tier.id',
        'paths.tier.LocationNotificationPreferences.id',
      ],
      order: [[`${sortByFieldName}`, `${sortByColumnType}`]],
    });
    const defaultLocation = await Locations.findOne({
      where: {
        isActive: true,
        isDefault: true,
        ...attr,
      },
      include: [
        {
          required: false,
          where: { isDeleted: false, ProjectId, MemberId: member },
          association: 'LocationNotificationPreferences',
          attributes: ['id', 'follow', 'ProjectId', 'ParentCompanyId', 'MemberId', 'LocationId'],
        },
        {
          required: false,
          where: { isActive: true, isDeleted: false },
          association: 'paths',
          include: [
            {
              required: false,
              where: { isActive: true, isDeleted: false },
              association: 'tier',
            },
          ],
        },
      ],
    });
    return { getLocations, defaultLocation };
  };

  Locations.getDefaultLocation = async (attr) => {
    const defaultLocation = await Locations.findOne({
      where: {
        isActive: true,
        isDefault: true,
        ...attr,
      },
    });
    return defaultLocation;
  };

  return Locations;
};
