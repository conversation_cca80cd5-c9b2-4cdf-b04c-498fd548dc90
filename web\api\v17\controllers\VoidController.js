const status = require('http-status');
const { voidService } = require('../services');

const VoidController = {
  async createVoidList(req, res, next) {
    try {
      await voidService.createVoidList(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Delivery Booking Marked as Void Successfully.',
            data: response,
            status: 201,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async removeVoidList(req, res, next) {
    try {
      await voidService.removeVoidList(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Booking Restored Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async createCraneRequestVoid(req, res, next) {
    try {
      await voidService.createCraneRequestVoid(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Crane Booking Marked as Void Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async createConcreteRequestVoid(req, res, next) {
    try {
      await voidService.createConcreteRequestVoid(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Concrete Booking Marked as Void Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getVoidList(req, res, next) {
    try {
      await voidService.getVoidList(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Void list displayed successfully',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async createMultipleVoidList(req, res, next) {
    try {
      await voidService.createMultipleVoidList(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Multiple Delivery Booking Marked as Void Successfully.',
            data: response,
            status: 201,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
};

module.exports = VoidController;
