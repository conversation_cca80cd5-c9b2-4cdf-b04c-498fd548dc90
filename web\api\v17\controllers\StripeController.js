const status = require('http-status');
const stripe = require('stripe')(process.env.STRIPE_API_KEY, {
  maxNetworkRetries: 3,
});
const moment = require('moment');
const ApiError = require('../helpers/apiError');
const { Plan, StripePlan, Sequelize, User, StripeSubscription, Project } = require('../models');
const { stripeService } = require('../services');

const StripeController = {
  /**
   * @params {header: JWT 'token'}
   * @params req.body.product.name - required
   * @params req.body.product.type
   *
   * @params req.body.plan.nickName
   * @params req.body.plan.amount
   * @params req.body.plan.currency must be supported format e.g ('inr', 'usd')
   * @params req.body.plan.interval  Either day, week, month or year.
   * @params req.body.plan.usage_type [licensed, metered]
   *
   * @returns {StripePlan}
   */
  async createPlan(req, res, next) {
    try {
      let product;
      let index = -1;
      const stripeDetails = await StripePlan.findOne({
        where: Sequelize.and(
          { stripePlanName: req.body.plan.nickName },
          { stripeProductName: req.body.product.name },
        ),
      });
      if (stripeDetails) {
        const error = new ApiError('Product and plan name already exist.', status.BAD_REQUEST);
        next(error);
      } else {
        const productDetails = await stripe.products.list();
        if (productDetails.data.length > 0) {
          index = productDetails.data.findIndex(
            (item) =>
              item.name.toString().toLowerCase() === req.body.product.name.toString().toLowerCase(),
          );
          if (index !== -1) {
            product = productDetails.data[index];
            req.body.product.id = product.id;
            stripeService.stripeAddProduct(req.body, (prodError, planResponse) => {
              if (!prodError) {
                res.status(status.OK).json(planResponse);
              } else {
                next(prodError);
              }
            });
          } else {
            stripeService.stripeProductcreate(req.body.product, (err, response) => {
              if (!err) {
                product = response;
                req.body.product.id = product.id;
                stripeService.stripeAddProduct(req.body, (prodError, planResponse) => {
                  if (!prodError) {
                    res.status(status.OK).json(planResponse);
                  } else {
                    next(prodError);
                  }
                });
              } else {
                next(err);
              }
            });
          }
        } else {
          stripeService.stripeProductcreate(req.body.product, (err, response) => {
            if (!err) {
              product = response;
              req.body.product.id = product;
              stripeService.stripeAddProduct(req.body, (prodError, planResponse) => {
                if (!prodError) {
                  res.status(status.OK).json(planResponse);
                } else {
                  next(prodError);
                }
              });
            } else {
              next(err);
            }
          });
        }
      }
    } catch (err) {
      next(err);
    }
  },

  async listAllPlans(req, res, next) {
    try {
      await stripeService.planList(req.params, (response, error) => {
        if (!error) {
          res.status(status.OK).json({ response });
        } else {
          next(error);
        }
      });

      // if (stripePlans.length > 0) {
      //   stripePlans = stripePlans.map((plan) => StripeSerializer.serialize(plan));
      // }
    } catch (err) {
      next(err);
    }
  },
  async upgradePlanList(req, res, next) {
    try {
      await stripeService.upgradePlanList(req.params, (response, error) => {
        if (!error) {
          res.status(status.OK).json({ response });
        } else {
          next(error);
        }
      });

      // if (stripePlans.length > 0) {
      //   stripePlans = stripePlans.map((plan) => StripeSerializer.serialize(plan));
      // }
    } catch (err) {
      next(err);
    }
  },
  /**
   * @params {header: JWT 'token'}
   * @params req.body.card.number - required
   * @params req.body.card.exp_month
   * @params req.body.card.exp_year
   * @params req.body.card.cvc
   *
   * @returns {StripePlan}
   */
  async addCard(req, res, next) {
    try {
      await stripeService.addCard(req, async (cardDetail, error) => {
        if (error) {
          next(error);
        } else {
          stripeService.subscribe(req, async (subDetail, subError) => {
            if (subError) {
              next(subError);
            } else {
              res.status(status.CREATED).json({ data: subDetail });
            }
          });
        }
      });
    } catch (error) {
      next(error);
    }
  },
  async subscribe(req, res, next) {
    try {
      stripeService.subscription(req, async (subDetail, subError) => {
        if (subError) {
          next(subError);
        } else {
          res.status(status.CREATED).json({ data: subDetail });
        }
      });
    } catch (err) {
      next(err);
    }
  },
  async cancelSubscription(req, res, next) {
    try {
      await stripeService.cancelSubscription(req, async (subDetail, subError) => {
        if (subError) {
          next(subError);
        } else {
          res.status(status.OK).json({ data: subDetail });
        }
      });
    } catch (error) {
      next(error);
    }
  },
  async holdSubscription(req, res, next) {
    try {
      await stripeService.holdSubscription(req, async (subDetail, subError) => {
        if (subError) {
          next(subError);
        } else {
          res.status(status.OK).json({ data: subDetail });
        }
      });
    } catch (error) {
      next(error);
    }
  },
  async listPlans(req, res, next) {
    try {
      const getPlanList = await stripeService.listPlans();
      if (getPlanList) {
        res.status(status.OK).json({
          status: 200,
          message: 'Plans listed Successfully.',
          data: getPlanList,
        });
      } else {
        res.status(status.UNPROCESSABLE_ENTITY).json({
          status: 422,
          message: 'Cannot able to list',
        });
      }
    } catch (error) {
      next(error);
    }
  },
  async updatePlanDetail(req, res, next) {
    try {
      const updatedPlanDetail = await stripeService.updatePlanDetail(req);
      if (updatedPlanDetail) {
        res.status(status.OK).json({
          status: 200,
          message: 'Plan Updated Successfully.',
        });
      } else {
        res.status(status.UNPROCESSABLE_ENTITY).json({
          status: 422,
          message: 'Cannot able to update plan.',
        });
      }
    } catch (error) {
      next(error);
    }
  },
  /**
   * @params {header: JWT 'token'}
   * @params req.body.id  -  Plan id
   * @params req.body.planName
   * @params req.body.amount
   * @params req.body.currency  *
   * @returns {getstripePlan}
   */
  async editPlan(req, res, next) {
    try {
      const { id } = req.body;
      await Plan.updatePlan(id, { isPublished: false });
      const planData = await stripeService.getStripePlans(req);
      await stripeService.getOldSubscribersList(req, id, planData);
      if (planData) {
        res.status(status.OK).json({ message: 'Plan updated' });
      } else {
        const error = new ApiError("Can't update plan.", status.BAD_REQUEST);
        next(error);
      }
    } catch (err) {
      next(err);
    }
  },
  async stripePortalSession(req, res, next) {
    try {
      const { stripeCustomerId } = req.user;
      const session = await stripe.billingPortal.sessions.create({
        customer: stripeCustomerId,
        return_url: `${process.env.BASE_URL}/profile`,
      });
      if (session) {
        res.status(status.OK).json({
          status: 200,
          message: 'stripe listed successfully.',
          data: session,
        });
      }
    } catch (error) {
      next(error);
    }
  },
  async webhook(req, res, next) {
    try {
      const endpointSecret = process.env.STRIPE_WEBHOOK_ENDPOINT_SECRET;
      const sig = req.headers['stripe-signature'];
      const event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
      let subscriptionId;
      let changedPlanId;
      let customerId;
      let nickname;
      if (event.type === 'customer.subscription.updated') {
        const subscription = event.data.object;
        subscriptionId = subscription.id;
        customerId = subscription.customer;
        changedPlanId = subscription.plan.id;
        nickname = subscription.plan.nickname;
        const user = await User.findOne({
          where: {
            stripeCustomerId: customerId,
          },
        });
        const getChangedPlan = await StripePlan.findOne({
          where: {
            stripePlanId: changedPlanId,
            stripePlanName: nickname,
          },
        });
        const getStripeSubscription = await StripeSubscription.findOne({
          where: {
            subscriptionId,
          },
        });
        if (getStripeSubscription) {
          await StripeSubscription.update(
            {
              UserId: user.id,
              status: 'active',
            },
            { where: { id: getStripeSubscription.id } },
          );
          const project = await Project.findOne({
            where: {
              StripeSubscriptionId: getStripeSubscription.id,
            },
          });
          let startDate;
          let endDate;
          if (getChangedPlan.stripeProductName.toLowerCase() === 'trial plan') {
            startDate = moment().format('YYYY-MM-DD');
            endDate = moment().add(14, 'days').format('YYYY-MM-DD');
          } else if (
            getChangedPlan.stripeProductName.toLowerCase() === 'project plan' &&
            getChangedPlan.stripePlanName === 'monthly'
          ) {
            startDate = moment().format('YYYY-MM-DD');
            endDate = moment(startDate, 'YYYY-MM-DD').add(1, 'M').format('YYYY-MM-DD');
          } else if (
            getChangedPlan.stripeProductName.toLowerCase() === 'project plan' &&
            getChangedPlan.stripePlanName === 'yearly'
          ) {
            startDate = moment().format('YYYY-MM-DD');
            endDate = moment(startDate, 'YYYY-MM-DD').add(12, 'M').format('YYYY-MM-DD');
          }
          const projectUpdated = await Project.update(
            {
              PlanId: getChangedPlan.id,
              StripeSubscriptionId: getStripeSubscription.id,
              status: '',
              subscribedOn: new Date(),
              startDate,
              endDate,
            },
            { where: { id: project.id } },
          );
          if (projectUpdated) {
            global.io.emit('planChanged', projectUpdated);
          }
        }
        res.status(200).json({ received: true });
      } else {
        res.status(422).json({ error: 'Unhandled event type', eventType: event.type });
      }
      res.status(200).send('webhook processed successfully');
    } catch (error) {
      console.log(error);
      res.status(400).send(`Webhook Error: ${error.message}`);
    }
  },
  async checkout(req, res, next) {
    try {
      const { stripeCustomerId } = req.user;
      const paymentMethods = await stripe.paymentMethods.list({
        customer: stripeCustomerId,
        type: 'card',
      });
      const session = await stripe.checkout.sessions.create({
        payment_method_types: [paymentMethods.data.card],
        mode: 'subscription',
        line_items: [
          {
            price: req.body.planData.stripePlanId,
            quantity: 1,
          },
        ],
        customer: stripeCustomerId,
        success_url: `${process.env.BASE_URL}${req.body.url}?customerId=${stripeCustomerId}`,
        cancel_url: `${process.env.BASE_URL}${req.body.url}`,
      });
      res.send({
        status: 200,
        data: session,
      });
    } catch (error) {
      console.log(error);
      res.status(400).send(`checkout Error: ${error.message}`);
    }
  },
  async createCheckoutSession(req, res, next) {
    try {
      const customer = await stripe.customers.create({
        email: req.body.email,
        name: req.body.name,
        phone: req.body.phone,
      });
      const session = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        mode: 'subscription',
        line_items: [
          {
            price: req.body.planData.stripePlanId,
            quantity: 1,
          },
        ],
        customer: customer.id,
        success_url: `${process.env.BASE_URL}${req.body.url}?customerId=${customer.id}`,
        cancel_url: `${process.env.BASE_URL}${req.body.url}`,
      });
      res.send({
        status: 200,
        data: session,
      });
    } catch (error) {
      console.log(error);
      res.status(400).send(`checkout Error: ${error.message}`);
    }
  },
};

module.exports = StripeController;
