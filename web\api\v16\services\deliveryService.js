/* eslint-disable no-restricted-syntax */
/* eslint-disable no-loop-func */
/* eslint-disable no-await-in-loop */
/* eslint-disable no-new */
const moment = require('moment');
const Moment = require('moment');
const momenttz = require('moment-timezone');
const MomentRange = require('moment-range');
const httpStatus = require('http-status');

const momentRange = MomentRange.extendMoment(Moment);
const ExcelJS = require('exceljs');
const { Worker } = require('worker_threads');
const { stringify } = require('flatted');
const path = require('path');
const Cryptr = require('cryptr');
// const mixpanelService = require('./mixpanelService');
const MAILER = require('../mailer');

const bulkNdrProcess = path.join(__dirname, './bulkNdrProcess.js');
const ApiError = require('../helpers/apiError');
const {
  Sequelize,
  Enterprise,
  NotificationPreference,
  NotificationPreferenceItem,
  DigestNotification,
  TimeZone,
  CraneRequest,
  ProjectSettings,
  RequestRecurrenceSeries,
  CalendarSetting,
  LocationNotificationPreferences,
  Locations,
} = require('../models');
let {
  DeliveryRequest,
  Member,
  DeliveryPerson,
  DeliverGate,
  DeliverEquipment,
  DeliverCompany,
  Role,
  Gates,
  Equipments,
  DeliverDefineWork,
  Company,
  Project,
  DeliverDefine,
  DeliverHistory,
  DeliveryPersonNotification,
  VoidList,
  User,
  Notification,
} = require('../models');
const helper = require('../helpers/domainHelper');
const notificationHelper = require('../helpers/notificationHelper');
const pushNotification = require('../config/fcm');
const calendarSettingsService = require('./calendarSettingsService');
const voidService = require('./voidService');
const concreteRequestService = require('./concreteRequestService');

let publicUser;
let publicMember;
const { Op } = Sequelize;

const deliveryService = {
  async newRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const eventTimeZone = await TimeZone.findOne({
        where: {
          isDeleted: false,
          id: +inputData.body.TimeZoneId,
        },
        attributes: [
          'id',
          'location',
          'isDayLightSavingEnabled',
          'timeZoneOffsetInMinutes',
          'dayLightSavingTimeInMinutes',
          'timezone',
        ],
      });
      if (!eventTimeZone) {
        return done(null, { message: 'Provide a valid timezone' });
      }
      const deliveryData = inputData.body;
      const loginUser = inputData.user;
      const projectDetails = await Project.getProjectAndSettings({
        isDeleted: false,
        id: +deliveryData.ProjectId,
      });
      let startDate;
      let endDate;
      if (deliveryData.recurrence) {
        startDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          deliveryData.deliveryStart,
          deliveryData.startPicker,
          eventTimeZone.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
        endDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          deliveryData.deliveryEnd,
          deliveryData.endPicker,
          eventTimeZone.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
      }
      if (deliveryData.startPicker === deliveryData.endPicker) {
        return done(null, { message: 'Delivery Start time and End time should not be the same' });
      }
      if (deliveryData.startPicker > deliveryData.endPicker) {
        return done(null, { message: 'Please enter From Time lesser than To Time' });
      }
      if (startDate || endDate) {
        if (projectDetails.ProjectSettings.deliveryWindowTime === 0) {
          if (deliveryData.recurrence === 'Does Not Repeat') {
            return done(null, { message: 'Please enter Future Date/Time' });
          }
          return done(null, { message: 'Please enter Future Start or End Date/Time' });
        }
        return done(null, {
          message: `Bookings can not be submitted within ${projectDetails.ProjectSettings.deliveryWindowTime} ${projectDetails.ProjectSettings.deliveryWindowTimeUnit} prior to the event`,
        });
      }

      if (projectDetails && projectDetails.ProjectSettings) {
        this.checkInputDatas(inputData, async (checkResponse, checkError) => {
          if (checkError) {
            return done(null, checkError);
          }
          const memberDetails = await Member.getBy({
            UserId: loginUser.id,
            ProjectId: deliveryData.ProjectId,
            isActive: true,
            isDeleted: false,
          });
          if (memberDetails) {
            const range = momentRange.range(
              moment(deliveryData.deliveryStart),
              moment(deliveryData.deliveryEnd),
            );
            let totalDays = Array.from(range.by('day'));
            const eventsArray = [];
            let DeliverParam = {};
            const lastIdValue = await DeliveryRequest.findOne({
              where: { ProjectId: memberDetails.ProjectId, isDeleted: false },
              order: [['DeliveryId', 'DESC']],
            });
            let id = 0;
            const newValue = JSON.parse(JSON.stringify(lastIdValue));
            if (newValue && newValue.DeliveryId !== null && newValue.DeliveryId !== undefined) {
              id = newValue.DeliveryId;
            }
            let lastData = {};
            lastData = await CraneRequest.findOne({
              where: { ProjectId: +memberDetails.ProjectId, isDeleted: false },
              order: [['CraneRequestId', 'DESC']],
            });
            const deliveryRequestList = await DeliveryRequest.findOne({
              where: {
                ProjectId: +memberDetails.ProjectId,
                isDeleted: false,
                isAssociatedWithCraneRequest: true,
              },
              order: [['CraneRequestId', 'DESC']],
            });
            if (deliveryRequestList) {
              if (lastData) {
                if (deliveryRequestList.CraneRequestId > lastData.CraneRequestId) {
                  lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
                }
              } else {
                lastData = {};
                lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
              }
            }
            if (lastData) {
              const data = lastData.CraneRequestId;
              lastData.CraneRequestId = 0;
              lastData.CraneRequestId = data + 1;
            } else {
              lastData = {};
              lastData.CraneRequestId = 1;
            }
            let craneId = 0;
            const newId = JSON.parse(JSON.stringify(lastData));
            if (newId && newId.CraneRequestId !== null && newId.CraneRequestId !== undefined) {
              craneId = newId.CraneRequestId;
            }
            const roleDetails = await Role.getBy('Project Admin');
            const accountRoleDetails = await Role.getBy('Account Admin');
            if (deliveryData.recurrence === 'Daily') {
              const startTime = deliveryData.startPicker;
              const endTime = deliveryData.endPicker;
              let dailyIndex = 0;
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                deliveryData,
                inputData.user,
                deliveryData.requestType,
                eventTimeZone.timezone,
              );

              while (dailyIndex < totalDays.length) {
                const data = totalDays[dailyIndex];
                if (
                  moment(data).isBetween(
                    moment(deliveryData.deliveryStart),
                    moment(deliveryData.deliveryEnd),
                    null,
                    '[]',
                  ) ||
                  moment(data).isSame(deliveryData.deliveryStart) ||
                  moment(data).isSame(deliveryData.deliveryEnd)
                ) {
                  id += 1;
                  craneId += 1;
                  const date = moment(`${data}`).format('MM/DD/YYYY');
                  const chosenTimezoneDeliveryStart = moment.tz(
                    `${date} ${startTime}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  const chosenTimezoneDeliveryEnd = moment.tz(
                    `${date} ${endTime}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  const deliveryStart = chosenTimezoneDeliveryStart
                    .clone()
                    .tz('UTC')
                    .format('YYYY-MM-DD HH:mm:ssZ');
                  const deliveryEnd = chosenTimezoneDeliveryEnd
                    .clone()
                    .tz('UTC')
                    .format('YYYY-MM-DD HH:mm:ssZ');
                  DeliverParam = {
                    description: deliveryData.description,
                    escort: deliveryData.escort,
                    vehicleDetails: deliveryData.vehicleDetails,
                    notes: deliveryData.notes,
                    DeliveryId: id,
                    deliveryStart,
                    deliveryEnd,
                    ProjectId: deliveryData.ProjectId,
                    createdBy: memberDetails.id,
                    isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
                    requestType: deliveryData.requestType,
                    cranePickUpLocation: deliveryData.cranePickUpLocation,
                    craneDropOffLocation: deliveryData.craneDropOffLocation,
                    CraneRequestId:
                      deliveryData.requestType === 'deliveryRequestWithCrane' ? craneId : null,
                    recurrenceId,
                    LocationId: deliveryData.LocationId,
                  };
                  if (
                    memberDetails.RoleId === roleDetails.id ||
                    memberDetails.RoleId === accountRoleDetails.id ||
                    memberDetails.isAutoApproveEnabled ||
                    projectDetails.ProjectSettings.isAutoApprovalEnabled
                  ) {
                    DeliverParam.status = 'Approved';
                    DeliverParam.approvedBy = memberDetails.id;
                    DeliverParam.approved_at = new Date();
                  }
                  eventsArray.push(DeliverParam);
                  // eslint-disable-next-line no-const-assign
                  dailyIndex += +deliveryData.repeatEveryCount;
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (deliveryData.recurrence === 'Weekly') {
              const startTime = deliveryData.startPicker;
              const endTime = deliveryData.endPicker;
              const startDayWeek = moment(deliveryData.deliveryStart).startOf('week');
              const endDayWeek = moment(deliveryData.deliveryEnd).endOf('week');
              const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
              const totalDaysOfRecurrence = Array.from(range1.by('day'));
              totalDays = totalDaysOfRecurrence;
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                deliveryData,
                inputData.user,
                deliveryData.requestType,
                eventTimeZone.timezone,
              );
              let count;
              let weekIncrement;
              if (+deliveryData.repeatEveryCount > 1) {
                count = +deliveryData.repeatEveryCount - 1;
                weekIncrement = 7;
              } else {
                count = 1;
                weekIncrement = 0;
              }
              for (
                let indexba = 0;
                indexba < totalDaysOfRecurrence.length;
                indexba += weekIncrement * count
              ) {
                const totalLength = indexba + 6;
                for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
                  const data = totalDaysOfRecurrence[indexb];
                  indexba += 1;
                  if (
                    data &&
                    !moment(data).isBefore(deliveryData.deliveryStart) &&
                    !moment(data).isAfter(deliveryData.deliveryEnd)
                  ) {
                    const day = moment(data).format('dddd');
                    const indexVal = deliveryData.days.includes(day);
                    if (indexVal) {
                      id += 1;
                      craneId += 1;
                      const date = moment(`${data}`).format('MM/DD/YYYY');
                      const chosenTimezoneDeliveryStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneDeliveryEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const deliveryStart = chosenTimezoneDeliveryStart
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      const deliveryEnd = chosenTimezoneDeliveryEnd
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      DeliverParam = {
                        description: deliveryData.description,
                        escort: deliveryData.escort,
                        vehicleDetails: deliveryData.vehicleDetails,
                        notes: deliveryData.notes,
                        DeliveryId: id,
                        deliveryStart,
                        deliveryEnd,
                        ProjectId: deliveryData.ProjectId,
                        createdBy: memberDetails.id,
                        isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
                        requestType: deliveryData.requestType,
                        cranePickUpLocation: deliveryData.cranePickUpLocation,
                        craneDropOffLocation: deliveryData.craneDropOffLocation,
                        CraneRequestId:
                          deliveryData.requestType === 'deliveryRequestWithCrane' ? craneId : null,
                        recurrenceId,
                        LocationId: deliveryData.LocationId,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        DeliverParam.status = 'Approved';
                        DeliverParam.approvedBy = memberDetails.id;
                        DeliverParam.approved_at = new Date();
                      }
                      eventsArray.push(DeliverParam);
                    }
                  }
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (deliveryData.recurrence === 'Monthly') {
              const startTime = deliveryData.startPicker;
              const endTime = deliveryData.endPicker;
              const startMonth = moment(deliveryData.deliveryStart).startOf('month');
              const startMonthNumber = moment(startMonth).format('MM');
              const endMonth = moment(deliveryData.deliveryEnd).endOf('month');
              const endMonthNumber = moment(endMonth).format('MM');
              let startDate1 = moment(deliveryData.deliveryStart);
              const endDate1 = moment(deliveryData.deliveryEnd).endOf('month');
              const allMonthsInPeriod = [];
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                deliveryData,
                inputData.user,
                deliveryData.requestType,
                eventTimeZone.timezone,
              );
              while (startDate1.isBefore(endDate1)) {
                allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
                startDate1 = startDate1.add(1, 'month');
              }
              let currentMonthDates = [];
              let totalNumberOfMonths = endMonthNumber - startMonthNumber;
              if (totalNumberOfMonths < 0) {
                totalNumberOfMonths *= -1;
              }
              let k = 0;
              while (k < allMonthsInPeriod.length + 1) {
                currentMonthDates = Array.from(
                  { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                  (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                );

                if (deliveryData.chosenDateOfMonth) {
                  const getDate = currentMonthDates.filter(
                    (value) => moment(value).format('DD') === deliveryData.dateOfMonth,
                  );
                  if (getDate.length === 1) {
                    if (
                      moment(getDate[0]).isBetween(
                        moment(deliveryData.deliveryStart),
                        moment(deliveryData.deliveryEnd),
                        null,
                        '[]',
                      ) ||
                      moment(getDate[0]).isSame(deliveryData.deliveryStart) ||
                      moment(getDate[0]).isSame(deliveryData.deliveryEnd)
                    ) {
                      id += 1;
                      craneId += 1;
                      const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                      const chosenTimezoneDeliveryStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneDeliveryEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const deliveryStart = chosenTimezoneDeliveryStart
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      const deliveryEnd = chosenTimezoneDeliveryEnd
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      DeliverParam = {
                        description: deliveryData.description,
                        escort: deliveryData.escort,
                        vehicleDetails: deliveryData.vehicleDetails,
                        notes: deliveryData.notes,
                        DeliveryId: id,
                        deliveryStart,
                        deliveryEnd,
                        ProjectId: deliveryData.ProjectId,
                        createdBy: memberDetails.id,
                        isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
                        requestType: deliveryData.requestType,
                        cranePickUpLocation: deliveryData.cranePickUpLocation,
                        craneDropOffLocation: deliveryData.craneDropOffLocation,
                        CraneRequestId:
                          deliveryData.requestType === 'deliveryRequestWithCrane' ? craneId : null,
                        recurrenceId,
                        LocationId: deliveryData.LocationId,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        DeliverParam.status = 'Approved';
                        DeliverParam.approvedBy = memberDetails.id;
                        DeliverParam.approved_at = new Date();
                      }
                      eventsArray.push(DeliverParam);
                    }
                  }
                } else if (allMonthsInPeriod[k]) {
                  const dayOfMonth = deliveryData.monthlyRepeatType;
                  const week = dayOfMonth.split(' ')[0].toLowerCase();
                  const day = dayOfMonth.split(' ')[1].toLowerCase();
                  const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM')
                    .startOf('month')
                    .day(day);
                  const getAllDays = [];
                  if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                  const month = chosenDay.month();
                  while (month === chosenDay.month()) {
                    getAllDays.push(chosenDay.toString());
                    chosenDay.add(7, 'd');
                  }
                  let i = 0;
                  if (week === 'second') {
                    i += 1;
                  } else if (week === 'third') {
                    i += 2;
                  } else if (week === 'fourth') {
                    i += 3;
                  } else if (week === 'last') {
                    i = getAllDays.length - 1;
                  }
                  const finalDay = getAllDays[i];
                  if (
                    moment(finalDay).isBetween(
                      moment(deliveryData.deliveryStart),
                      moment(deliveryData.deliveryEnd),
                      null,
                      '[]',
                    ) ||
                    moment(finalDay).isSame(deliveryData.deliveryStart) ||
                    moment(finalDay).isSame(deliveryData.deliveryEnd)
                  ) {
                    id += 1;
                    craneId += 1;
                    const date = moment(finalDay).format('MM/DD/YYYY');
                    const chosenTimezoneDeliveryStart = moment.tz(
                      `${date} ${startTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezoneDeliveryEnd = moment.tz(
                      `${date} ${endTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const deliveryStart = chosenTimezoneDeliveryStart
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ');
                    const deliveryEnd = chosenTimezoneDeliveryEnd
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ');
                    DeliverParam = {
                      description: deliveryData.description,
                      escort: deliveryData.escort,
                      vehicleDetails: deliveryData.vehicleDetails,
                      notes: deliveryData.notes,
                      DeliveryId: id,
                      deliveryStart,
                      deliveryEnd,
                      ProjectId: deliveryData.ProjectId,
                      createdBy: memberDetails.id,
                      isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
                      requestType: deliveryData.requestType,
                      cranePickUpLocation: deliveryData.cranePickUpLocation,
                      craneDropOffLocation: deliveryData.craneDropOffLocation,
                      CraneRequestId:
                        deliveryData.requestType === 'deliveryRequestWithCrane' ? craneId : null,
                      recurrenceId,
                      LocationId: deliveryData.LocationId,
                    };
                    if (
                      memberDetails.RoleId === roleDetails.id ||
                      memberDetails.RoleId === accountRoleDetails.id ||
                      memberDetails.isAutoApproveEnabled ||
                      projectDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      DeliverParam.status = 'Approved';
                      DeliverParam.approvedBy = memberDetails.id;
                      DeliverParam.approved_at = new Date();
                    }
                    eventsArray.push(DeliverParam);
                  }
                }
                k += +deliveryData.repeatEveryCount;
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (deliveryData.recurrence === 'Yearly') {
              const startTime = deliveryData.startPicker;
              const endTime = deliveryData.endPicker;
              const startMonth = moment(deliveryData.deliveryStart).startOf('month');
              const startMonthNumber = moment(startMonth).format('MM');
              const endMonth = moment(deliveryData.deliveryEnd).endOf('month');
              const endMonthNumber = moment(endMonth).format('MM');
              let startDate1 = moment(deliveryData.deliveryStart);
              const endDate1 = moment(deliveryData.deliveryEnd).endOf('month');
              const allMonthsInPeriod = [];
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                deliveryData,
                inputData.user,
                deliveryData.requestType,
                eventTimeZone.timezone,
              );
              while (startDate1.isBefore(endDate1)) {
                allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
                startDate1 = startDate1.add(12, 'month');
              }
              let currentMonthDates = [];
              let totalNumberOfMonths = endMonthNumber - startMonthNumber;
              if (totalNumberOfMonths < 0) {
                totalNumberOfMonths *= -1;
              }
              for (let k = 0; k < allMonthsInPeriod.length + 1; k += 1) {
                currentMonthDates = Array.from(
                  { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                  (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                );
                if (deliveryData.chosenDateOfMonth) {
                  const getDate = currentMonthDates.filter(
                    (value) => moment(value).format('DD') === deliveryData.dateOfMonth,
                  );
                  if (getDate.length === 1) {
                    if (
                      moment(getDate[0]).isBetween(
                        moment(deliveryData.deliveryStart),
                        moment(deliveryData.deliveryEnd),
                        null,
                        '[]',
                      ) ||
                      moment(getDate[0]).isSame(deliveryData.deliveryStart) ||
                      moment(getDate[0]).isSame(deliveryData.deliveryEnd)
                    ) {
                      id += 1;
                      craneId += 1;
                      const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                      const chosenTimezoneDeliveryStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneDeliveryEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const deliveryStart = chosenTimezoneDeliveryStart
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      const deliveryEnd = chosenTimezoneDeliveryEnd
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      DeliverParam = {
                        description: deliveryData.description,
                        escort: deliveryData.escort,
                        vehicleDetails: deliveryData.vehicleDetails,
                        notes: deliveryData.notes,
                        DeliveryId: id,
                        deliveryStart,
                        deliveryEnd,
                        ProjectId: deliveryData.ProjectId,
                        createdBy: memberDetails.id,
                        isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
                        requestType: deliveryData.requestType,
                        cranePickUpLocation: deliveryData.cranePickUpLocation,
                        craneDropOffLocation: deliveryData.craneDropOffLocation,
                        CraneRequestId:
                          deliveryData.requestType === 'deliveryRequestWithCrane' ? craneId : null,
                        recurrenceId,
                        LocationId: deliveryData.LocationId,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        DeliverParam.status = 'Approved';
                        DeliverParam.approvedBy = memberDetails.id;
                        DeliverParam.approved_at = new Date();
                      }
                      eventsArray.push(DeliverParam);
                    }
                  }
                } else if (allMonthsInPeriod[k]) {
                  const dayOfMonth = deliveryData.monthlyRepeatType;
                  const week = dayOfMonth.split(' ')[0].toLowerCase();
                  const day = dayOfMonth.split(' ')[1].toLowerCase();
                  const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM')
                    .startOf('month')
                    .day(day);
                  const getAllDays = [];
                  if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                  const month = chosenDay.month();
                  while (month === chosenDay.month()) {
                    getAllDays.push(chosenDay.toString());
                    chosenDay.add(7, 'd');
                  }
                  let i = 0;
                  if (week === 'second') {
                    i += 1;
                  } else if (week === 'third') {
                    i += 2;
                  } else if (week === 'fourth') {
                    i += 3;
                  } else if (week === 'last') {
                    i = getAllDays.length - 1;
                  }
                  const finalDay = getAllDays[i];
                  if (
                    moment(finalDay).isBetween(
                      moment(deliveryData.deliveryStart),
                      moment(deliveryData.deliveryEnd),
                      null,
                      '[]',
                    ) ||
                    moment(finalDay).isSame(deliveryData.deliveryStart) ||
                    moment(finalDay).isSame(deliveryData.deliveryEnd)
                  ) {
                    id += 1;
                    craneId += 1;
                    const date = moment(finalDay).format('MM/DD/YYYY');
                    const chosenTimezoneDeliveryStart = moment.tz(
                      `${date} ${startTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezoneDeliveryEnd = moment.tz(
                      `${date} ${endTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const deliveryStart = chosenTimezoneDeliveryStart
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ');
                    const deliveryEnd = chosenTimezoneDeliveryEnd
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ');
                    DeliverParam = {
                      description: deliveryData.description,
                      escort: deliveryData.escort,
                      vehicleDetails: deliveryData.vehicleDetails,
                      notes: deliveryData.notes,
                      DeliveryId: id,
                      deliveryStart,
                      deliveryEnd,
                      ProjectId: deliveryData.ProjectId,
                      createdBy: memberDetails.id,
                      isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
                      requestType: deliveryData.requestType,
                      cranePickUpLocation: deliveryData.cranePickUpLocation,
                      craneDropOffLocation: deliveryData.craneDropOffLocation,
                      CraneRequestId:
                        deliveryData.requestType === 'deliveryRequestWithCrane' ? craneId : null,
                      recurrenceId,
                      LocationId: deliveryData.LocationId,
                    };
                    if (
                      memberDetails.RoleId === roleDetails.id ||
                      memberDetails.RoleId === accountRoleDetails.id ||
                      memberDetails.isAutoApproveEnabled ||
                      projectDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      DeliverParam.status = 'Approved';
                      DeliverParam.approvedBy = memberDetails.id;
                      DeliverParam.approved_at = new Date();
                    }
                    eventsArray.push(DeliverParam);
                  }
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (deliveryData.recurrence === 'Does Not Repeat') {
              id += 1;
              craneId += 1;
              const chosenTimezoneDeliveryStart = moment.tz(
                `${deliveryData.deliveryStart} ${deliveryData.startPicker}`,
                'YYYY MM DD 00:00:00 HH:mm',
                eventTimeZone.timezone,
              );
              const chosenTimezoneDeliveryEnd = moment.tz(
                `${deliveryData.deliveryEnd} ${deliveryData.endPicker}`,
                'YYYY MM DD 00:00:00 HH:mm',
                eventTimeZone.timezone,
              );
              const deliveryStart = chosenTimezoneDeliveryStart
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');
              const deliveryEnd = chosenTimezoneDeliveryEnd
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                deliveryData,
                inputData.user,
                deliveryData.requestType,
                eventTimeZone.timezone,
              );
              DeliverParam = {
                description: deliveryData.description,
                escort: deliveryData.escort,
                vehicleDetails: deliveryData.vehicleDetails,
                notes: deliveryData.notes,
                DeliveryId: id,
                deliveryStart,
                deliveryEnd,
                ProjectId: deliveryData.ProjectId,
                createdBy: memberDetails.id,
                isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
                requestType: deliveryData.requestType,
                cranePickUpLocation: deliveryData.cranePickUpLocation,
                craneDropOffLocation: deliveryData.craneDropOffLocation,
                CraneRequestId:
                  deliveryData.requestType === 'deliveryRequestWithCrane' ? craneId : null,
                recurrenceId,
                LocationId: deliveryData.LocationId,
              };
              if (
                memberDetails.RoleId === roleDetails.id ||
                memberDetails.RoleId === accountRoleDetails.id ||
                memberDetails.isAutoApproveEnabled ||
                projectDetails.ProjectSettings.isAutoApprovalEnabled
              ) {
                DeliverParam.status = 'Approved';
                DeliverParam.approvedBy = memberDetails.id;
                DeliverParam.approved_at = new Date();
              }
              eventsArray.push(DeliverParam);
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            let newDeliverData = {};
            if (eventsArray.length > 0) {
              for (let i = 0; i < eventsArray.length; i += 1) {
                newDeliverData = await DeliveryRequest.createInstance(eventsArray[i]);
                const { companies, persons, define } = deliveryData;
                const gates = [deliveryData.GateId];
                const equipments = [deliveryData.EquipmentId];
                const updateParam = {
                  DeliveryId: newDeliverData.id,
                  DeliveryCode: newDeliverData.DeliveryId,
                  ProjectId: deliveryData.ProjectId,
                };
                companies.forEach(async (element) => {
                  const companyParam = updateParam;
                  companyParam.CompanyId = element;
                  await DeliverCompany.createInstance(companyParam);
                });
                gates.forEach(async (element) => {
                  const gateParam = updateParam;
                  gateParam.GateId = element;
                  await DeliverGate.createInstance(gateParam);
                });
                equipments.forEach(async (element) => {
                  const equipmentParam = updateParam;
                  equipmentParam.EquipmentId = element;
                  await DeliverEquipment.createInstance(equipmentParam);
                });
                persons.forEach(async (element) => {
                  const memberParam = updateParam;
                  memberParam.MemberId = element;
                  await DeliveryPerson.createInstance(memberParam);
                });
                define.forEach(async (element) => {
                  const defineParam = updateParam;
                  defineParam.DeliverDefineWorkId = element;
                  await DeliverDefine.createInstance(defineParam);
                });
                const history = {
                  DeliveryRequestId: newDeliverData.id,
                  DeliveryId: newDeliverData.DeliveryId,
                  MemberId: memberDetails.id,
                  type: 'create',
                  description: `${loginUser.firstName} ${loginUser.lastName} Created Delivery Booking, ${deliveryData.description}.`,
                };
                const notification = history;
                notification.ProjectId = eventsArray[i].ProjectId;
                notification.title = 'Delivery Booking Creation';
                await DeliverHistory.createInstance(history);
                if (newDeliverData.status === 'Approved') {
                  const object = {
                    ProjectId: deliveryData.ProjectId,
                    MemberId: memberDetails.id,
                    DeliveryRequestId: newDeliverData.id,
                    isDeleted: false,
                    type: 'approved',
                    description: `${loginUser.firstName} ${loginUser.lastName} Approved Delivery Booking, ${deliveryData.description}.`,
                  };
                  await DeliverHistory.createInstance(object);
                }
              }
            }
            if (Object.keys(newDeliverData).length > 0 && typeof newDeliverData === 'object') {
              const { persons } = deliveryData;
              const locationChosen = await Locations.findOne({
                where: {
                  ProjectId: deliveryData.ProjectId,
                  id: deliveryData.LocationId,
                },
              });

              const history = {
                DeliveryRequestId: newDeliverData.id,
                DeliveryId: newDeliverData.DeliveryId,
                MemberId: memberDetails.id,
                type: 'create',
                description: `${loginUser.firstName} ${loginUser.lastName} Created Delivery Booking, ${deliveryData.description}.`,
                locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Created Delivery Booking, ${deliveryData.description}. Location: ${locationChosen.locationPath}.`,
                memberData: [],
              };
              const notification = history;
              notification.ProjectId = deliveryData.ProjectId;
              notification.LocationId = deliveryData.LocationId;
              notification.title = 'Delivery Booking Creation';
              notification.recurrenceType = `${deliveryData.recurrence} From ${moment(
                deliveryData.deliveryStart,
              ).format('MM/DD/YYYY')} to ${moment(deliveryData.deliveryEnd).format('MM/DD/YYYY')}`;
              notification.requestType = 'deliveryRequest';
              const newNotification = await Notification.createInstance(notification);
              const memberLocationPreference = await LocationNotificationPreferences.findAll({
                where: {
                  ProjectId: deliveryData.ProjectId,
                  LocationId: deliveryData.LocationId,
                  follow: true,
                },
                include: [
                  {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                      [Op.and]: [
                        {
                          id: { [Op.ne]: memberDetails.id },
                        },
                      ],
                    },
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                  },
                ],
              });
              const locationFollowMembers = [];
              memberLocationPreference.forEach(async (element) => {
                locationFollowMembers.push(element.Member.id);
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    { ProjectId: deliveryData.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.in]: persons } },
                    { id: { [Op.ne]: newNotification.MemberId } },
                    { id: { [Op.notIn]: locationFollowMembers } },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                  },
                ],
                attributes: ['id', 'RoleId'],
              });
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                await pushNotification.sendMemberLocationPreferencePushNotification(
                  memberLocationPreference,
                  newDeliverData.DeliveryRequestId,
                  history.locationFollowDescription,
                  newDeliverData.requestType,
                  newDeliverData.ProjectId,
                  newDeliverData.id,
                  3,
                );
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  deliveryData.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  3,
                );
              }
              history.adminData = adminData;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = deliveryData.ProjectId;
              history.projectName = projectDetails.projectName;
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                [],
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberDetails,
                loginUser,
                3,
                'created a',
                'Delivery Request',
                `delivery Booking (${newDeliverData.DeliveryId} - ${newDeliverData.description})`,
                newDeliverData.id,
              );
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: deliveryData.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 3,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              await pushNotification.sendDeviceToken(history, 3, deliveryData.ProjectId);
              await this.sendEmailNotificationToUser(
                history,
                memberDetails,
                loginUser,
                newDeliverData,
                deliveryData,
                memberLocationPreference,
              );
              const memberLocationPreferenceNotify = await LocationNotificationPreferences.findAll({
                where: {
                  ProjectId: +deliveryData.ProjectId,
                  LocationId: +deliveryData.LocationId,
                  follow: true,
                },
                include: [
                  {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                      [Op.and]: [
                        {
                          id: { [Op.ne]: memberDetails.id },
                        }
                      ],
                    },
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                  },
                ],
              });
              if (+memberDetails.RoleId === 4 || +memberDetails.RoleId === 3) {
                const userEmails = await this.getMemberDetailData(
                  history,
                  memberLocationPreference,
                );
                if (userEmails.length > 0) {
                  userEmails.forEach(async (element) => {
                    if (+element.RoleId === 2 && +element.MemberId !== +memberDetails.id) {
                      let name;
                      if (!element.firstName) {
                        name = 'user';
                      } else {
                        name = `${element.firstName} ${element.lastName}`;
                      }
                      const memberRole = await Role.findOne({
                        where: {
                          id: memberDetails.RoleId,
                          isDeleted: false,
                        },
                      });
                      const mailPayload = {
                        name,
                        email: element.email,
                        content: `We would like to inform you that 
                          ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has created a delivery booking ${newDeliverData.DeliveryId} and waiting for your approval.Kindly review the booking and update the status.`,
                      };
                      const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
                        where: {
                          MemberId: +element.MemberId,
                          ProjectId: +deliveryData.ProjectId,
                          LocationId: +deliveryData.LocationId,
                          isDeleted: false,
                          // follow: true,
                        },
                      });
                      if (isMemberFollowLocation) {
                        const memberNotification = await NotificationPreference.findOne({
                          where: {
                            MemberId: +element.MemberId,
                            ProjectId: +deliveryData.ProjectId,
                            isDeleted: false,
                          },
                          include: [
                            {
                              association: 'NotificationPreferenceItem',
                              where: {
                                id: 8,
                                isDeleted: false,
                              },
                            },
                          ],
                        });
                        if (memberNotification && memberNotification.instant) {
                          await MAILER.sendMail(
                            mailPayload,
                            'notifyPAForApproval',
                            `Delivery Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                            `Delivery Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                            async (info, err) => {
                              console.log(info, err);
                            },
                          );
                        }
                        if (memberNotification && memberNotification.dailyDigest) {
                          await this.createDailyDigestDataApproval(
                            +memberDetails.RoleId,
                            +element.MemberId,
                            +deliveryData.ProjectId,
                            +deliveryData.ParentCompanyId,
                            loginUser,
                            'created a',
                            'Delivery Request',
                            `delivery Booking (${newDeliverData.DeliveryId} - ${newDeliverData.description})`,
                            'and waiting for your approval',
                            newDeliverData.id,
                          );
                        }
                      }
                    }
                  });
                  if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                    history.memberData = [];
                    history.memberData.push(...memberLocationPreferenceNotify);
                  }
                  return done(history, false);
                }
                if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                  history.memberData = [];
                  history.memberData.push(...memberLocationPreferenceNotify);
                }
                return done(history, false);
              } else {
                if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                  history.memberData = [];
                  history.memberData.push(...memberLocationPreferenceNotify);
                }
                return done(history, false);
              }
            }
            return done(null, {
              message: 'Bookings will not be created for the scheduled date/time',
            });
          }
          return done(null, {
            message: 'You are not allowed create Delivery Booking for this project.',
          });
        });
      } else {
        return done(null, { message: 'Project does not exist.' });
      }
    } catch (e) {
      return done(null, e);
    }
  },
  async compareDeliveryDateWithDeliveryWindowDate(
    dateStr,
    timeStr,
    timezoneStr,
    deliveryWindowTime,
    deliveryWindowTimeUnit,
  ) {
    const datetimeStr = `${moment(dateStr).format('YYYY-MM-DD')}T${timeStr}`;
    const datetime = moment.tz(datetimeStr, timezoneStr);
    const currentDatetime = moment
      .tz(timezoneStr)
      .add(deliveryWindowTime, deliveryWindowTimeUnit)
      .startOf('minute');
    return datetime.isSameOrBefore(currentDatetime);
  },
  async ReadAllnotification(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const loginUser = inputData.user;
      const memberDetails = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          isActive: true,
          isDeleted: false,
          ProjectId: inputData.query.ProjectId,
        }),
      });
      const newNot = await DeliveryPersonNotification.update(
        { seen: true },
        {
          where: {
            ProjectId: inputData.query.ProjectId,
            MemberId: memberDetails.id,
            isDeleted: false,
          },
        },
      );
      const condition = {
        seen: false,
        ProjectId: inputData.query.ProjectId,
      };
      const count = await DeliveryPersonNotification.getUnSeenCount(condition, loginUser);
      done(count.length, false);
    } catch (e) {
      return done(null, e);
    }
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const incomeData = inputData;
    let enterpriseValue;
    let ProjectId;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    DeliveryRequest = modelObj.DeliveryRequest;
    Member = modelObj.Member;
    DeliveryPerson = modelObj.DeliveryPerson;
    DeliverGate = modelObj.DeliverGate;
    DeliverEquipment = modelObj.DeliverEquipment;
    DeliverCompany = modelObj.DeliverCompany;
    Role = modelObj.Role;
    Gates = modelObj.Gates;
    Equipments = modelObj.Equipments;
    DeliverDefineWork = modelObj.DeliverDefineWork;
    Company = modelObj.Company;
    Project = modelObj.Project;
    User = modelObj.User;
    DeliverDefine = modelObj.DeliverDefine;
    DeliverHistory = modelObj.DeliverHistory;
    VoidList = modelObj.VoidList;
    DeliveryPersonNotification = modelObj.DeliveryPersonNotification;
    Notification = modelObj.Notification;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return ProjectId;
  },

  async Markallnotification(inputData) {
    try {
      await this.getDynamicModel(inputData);
      const loginUser = inputData.user;
      const { params } = inputData;

      const memberDetails = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          isActive: true,
          isDeleted: false,
          ProjectId: params.ProjectId,
        }),
      });
      const newNot = await DeliveryPersonNotification.update(
        { seen: true },
        {
          where: {
            ProjectId: params.ProjectId,
            MemberId: memberDetails.id,
            isDeleted: false,
          },
        },
      );
      const condition = {
        seen: false,
        ProjectId: params.ProjectId,
      };
      const count = await DeliveryPersonNotification.getUnSeenCount(condition, loginUser);
      return { status: 200, data: count.length };
    } catch (e) {
      console.log(e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async checkInputDatas(inputData, done) {
    await this.getDynamicModel(inputData);
    const deliveryData = inputData.body;
    const { companies, persons, define } = deliveryData;
    const gates = [deliveryData.GateId];
    const equipments = [deliveryData.EquipmentId];
    const inputProjectId = deliveryData.ProjectId;
    const memberList = await Member.count({
      where: { id: { [Op.in]: persons }, ProjectId: inputProjectId, isDeleted: false },
    });
    const gateList = await Gates.count({
      where: { id: { [Op.in]: gates }, ProjectId: inputProjectId, isDeleted: false },
    });
    const equipmentList = await Equipments.count({
      where: { id: { [Op.in]: equipments }, ProjectId: inputProjectId, isDeleted: false },
    });
    const defineList = await DeliverDefineWork.count({
      where: { id: { [Op.in]: define }, ProjectId: inputProjectId, isDeleted: false },
    });
    const companyList = await Company.count({
      where: {
        [Op.or]: [
          {
            id: { [Op.in]: companies },
            ProjectId: +inputProjectId,
            isDeleted: false,
          },
          {
            id: {
              [Op.in]: companies,
            },
            isParent: true,
            ParentCompanyId: +deliveryData.ParentCompanyId,
            isDeleted: false,
          },
        ],
      },
    });
    if (deliveryData.persons && deliveryData.persons.length > 0 && memberList !== persons.length) {
      return done(null, { message: 'Some Member is not in the project' });
    }
    if (deliveryData.GateId && gateList !== gates.length) {
      return done(null, { message: 'Mentioned Gate is not in the project' });
    }
    if (deliveryData.EquipmentId && equipmentList !== equipments.length) {
      return done(null, { message: 'Mentioned Equipment is not in this project' });
    }
    if (
      deliveryData.companies &&
      deliveryData.companies.length > 0 &&
      companyList !== companies.length
    ) {
      return done(null, { message: 'Some Company is not in the project' });
    }
    if (deliveryData.define && deliveryData.define.length > 0 && defineList !== define.length) {
      return done(null, { message: 'Some Definable Feature of Work is not in the project' });
    }
    return done(true, false);
  },
  async editRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const deliveryData = inputData.body;
      const loginUser = inputData.user;
      const { recurrenceId } = deliveryData;
      let editSeriesRequests;
      let newRecurrenceId;
      let previousRecordInSeries;
      const projectSettingDetails = await Project.getProjectAndSettings({
        isDeleted: false,
        id: +deliveryData.ProjectId,
      });
      if (projectSettingDetails) {
        const requestData = await DeliveryRequest.getSingleDeliveryRequestData({
          id: deliveryData.id,
        });
        if (deliveryData.seriesOption === 1) {
          const requestArray = [];
          requestArray.push({
            ProjectId: deliveryData.ProjectId,
            deliveryStart: deliveryData.deliveryStart,
            deliveryEnd: deliveryData.deliveryEnd,
            id: deliveryData.id,
          });
          const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
            requestArray,
            projectSettingDetails,
            'edit',
          );
          if (isOverlapping && isOverlapping.error) {
            return done(null, {
              message: isOverlapping.message,
            });
          }
        }
        if (deliveryData.seriesOption === 2 || deliveryData.seriesOption === 3) {
          let requestSeries = [];
          if (deliveryData.seriesOption === 2) {
            requestSeries = await DeliveryRequest.findAll({
              where: [
                Sequelize.and({
                  recurrenceId,
                  id: {
                    [Op.gte]: deliveryData.id,
                  },
                }),
              ],
            });
          }
          if (deliveryData.seriesOption === 3) {
            requestSeries = await DeliveryRequest.findAll({
              where: [
                Sequelize.and({
                  recurrenceId,
                  deliveryStart: {
                    [Op.gte]: moment.tz(deliveryData.timeZone).utc().format('YYYY-MM-DD HH:mm:ssZ'),
                  },
                }),
              ],
            });
          }
          const requestArray = [];
          for (let i = 0; i < requestSeries.length; i += 1) {
            const deliveryStartDate = await this.convertTimezoneToUtc(
              moment
                .utc(requestSeries[i].deliveryStart)
                .tz(deliveryData.timezone)
                .format('MM/DD/YYYY'),
              deliveryData.timezone,
              deliveryData.deliveryStartTime,
            );
            const deliveryEndDate = await this.convertTimezoneToUtc(
              moment
                .utc(requestSeries[i].deliveryEnd)
                .tz(deliveryData.timezone)
                .format('MM/DD/YYYY'),
              deliveryData.timezone,
              deliveryData.deliveryEndTime,
            );
            requestArray.push({
              ProjectId: deliveryData.ProjectId,
              deliveryStart: !moment(deliveryStartDate).isSame(
                moment(requestSeries[i].deliveryStart),
              )
                ? deliveryStartDate
                : requestSeries[i].deliveryStart,
              deliveryEnd: !moment(deliveryEndDate).isSame(moment(requestSeries[i].deliveryEnd))
                ? deliveryEndDate
                : requestSeries[i].deliveryEnd,
              id: requestSeries[i].id,
            });
          }
          const utcRecurrenceEndDate = requestData.recurrence.recurrenceEndDate;
          const existingRecurrenceEndDate = moment(utcRecurrenceEndDate)
            .tz(deliveryData.timezone)
            .format('YYYY-MM-DD');
          const newRecurrenceEndDate = deliveryData.recurrenceEndDate;
          if (!moment(existingRecurrenceEndDate).isSame(moment(newRecurrenceEndDate))) {
            const startDate = moment(existingRecurrenceEndDate).add(1, 'day');
            const endDate = moment(newRecurrenceEndDate);
            for (let date = startDate; date.isSameOrBefore(endDate); date.add(1, 'day')) {
              requestArray.push({
                ProjectId: deliveryData.ProjectId,
                deliveryStart: await this.convertTimezoneToUtc(
                  moment(date).format('MM/DD/YYYY'),
                  deliveryData.timezone,
                  deliveryData.deliveryStartTime,
                ),
                deliveryEnd: await this.convertTimezoneToUtc(
                  moment(date).format('MM/DD/YYYY'),
                  deliveryData.timezone,
                  deliveryData.deliveryEndTime,
                ),
              });
            }
            if (requestArray.length > 0) {
              const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                requestArray,
                projectSettingDetails,
                'edit',
              );
              if (isOverlapping && isOverlapping.error) {
                return done(null, {
                  message: isOverlapping.message,
                });
              }
            }
          } else if (requestArray.length > 0) {
            const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
              requestArray,
              projectSettingDetails,
              'edit',
            );
            if (isOverlapping && isOverlapping.error) {
              return done(null, {
                message: isOverlapping.message,
              });
            }
          }
        }
      }
      const roleDetails = await Role.getBy('Project Admin');
      const accountRoleDetails = await Role.getBy('Account Admin');
      // This event
      if (deliveryData.seriesOption === 1) {
        editSeriesRequests = await DeliveryRequest.findAll({
          where: [
            Sequelize.and({
              id: deliveryData.id,
            }),
          ],
        });
        if (editSeriesRequests && editSeriesRequests[0] && deliveryData.recurrenceId) {
          const previousRecordInThisEventSeries = await DeliveryRequest.findAll({
            where: [
              Sequelize.and({
                recurrenceId,
                id: {
                  [Op.lt]: deliveryData.id,
                },
              }),
            ],
            order: [['id', 'DESC']],
          });
          const NextSeriesLastRecord = await DeliveryRequest.findAll({
            where: [
              Sequelize.and({
                recurrenceId,
                id: {
                  [Op.gt]: deliveryData.id,
                },
              }),
            ],
            order: [['id', 'DESC']],
          });
          if (
            ((NextSeriesLastRecord && NextSeriesLastRecord.length > 0) ||
              (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0)) &&
            !(
              NextSeriesLastRecord &&
              NextSeriesLastRecord.length > 0 &&
              previousRecordInThisEventSeries &&
              previousRecordInThisEventSeries.length > 0
            )
          ) {
            if (NextSeriesLastRecord && NextSeriesLastRecord.length > 0) {
              const chosenTimezoneDeliveryStart = moment.tz(
                `${deliveryData.nextSeriesRecurrenceStartDate}  '00:00'`,
                'YYYY-MM-DD HH:mm',
                deliveryData.timezone,
              );
              const utcDate = chosenTimezoneDeliveryStart
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');
              await RequestRecurrenceSeries.update(
                {
                  recurrenceStartDate: utcDate,
                },
                {
                  where: {
                    id: NextSeriesLastRecord[0].recurrenceId,
                  },
                },
              );
            }
            if (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0) {
              // const chosenTimezoneDeliveryStart = moment.tz(
              //   `${previousRecordInThisEventSeries[0].deliveryData.previousSeriesRecurrenceEndDate}  '00:00'`,
              //   'YYYY-MM-DD HH:mm',
              //   deliveryData.timezone,
              // );
              // const utcDate = chosenTimezoneDeliveryStart
              //   .clone()
              //   .tz('UTC')
              //   .format('YYYY-MM-DD HH:mm:ssZ');
              await RequestRecurrenceSeries.update(
                {
                  recurrenceEndDate: previousRecordInThisEventSeries[0].deliveryStart,
                },
                {
                  where: {
                    id: previousRecordInThisEventSeries[0].recurrenceId,
                  },
                },
              );
            }
          }
        }
      }
      // This and all following events
      if (deliveryData.seriesOption === 2) {
        editSeriesRequests = await DeliveryRequest.findAll({
          where: [
            Sequelize.and({
              recurrenceId,
              id: {
                [Op.gte]: deliveryData.id,
              },
            }),
          ],
        });
        previousRecordInSeries = await DeliveryRequest.findOne({
          where: [
            Sequelize.and({
              recurrenceId,
              id: {
                [Op.lt]: deliveryData.id,
              },
            }),
          ],
          order: [['id', 'DESC']],
        });
      }
      // All events in the series
      if (deliveryData.seriesOption === 3) {
        editSeriesRequests = await DeliveryRequest.findAll({
          where: [
            Sequelize.and({
              recurrenceId,
              deliveryStart: {
                [Op.gte]: moment.tz(deliveryData.timeZone).utc().format('YYYY-MM-DD HH:mm:ssZ'),
              },
            }),
          ],
        });
      }

      if (editSeriesRequests && editSeriesRequests[0] && editSeriesRequests.length > 0) {
        const requestData = await DeliveryRequest.getSingleDeliveryRequestData({
          id: editSeriesRequests[0].id,
        });
        if (requestData && requestData.recurrence) {
          requestData.recurrence.ParentCompanyId = deliveryData.ParentCompanyId;
          requestData.recurrence.ProjectId = deliveryData.ProjectId;
          if (deliveryData.seriesOption === 1) {
            requestData.recurrence.deliveryStart = deliveryData.recurrenceSeriesStartDate;
            requestData.recurrence.deliveryEnd = deliveryData.recurrenceSeriesEndDate;
          }
          if (deliveryData.seriesOption === 2) {
            requestData.recurrence.deliveryStart = deliveryData.recurrenceSeriesStartDate;
            requestData.recurrence.deliveryEnd = deliveryData.recurrenceEndDate;
          }
          if (deliveryData.seriesOption === 2 && previousRecordInSeries) {
            newRecurrenceId = await concreteRequestService.insertRecurrenceSeries(
              requestData.recurrence,
              loginUser,
              requestData.requestType,
              deliveryData.timezone,
            );
          }
        }
        if (deliveryData.seriesOption === 2 || deliveryData.seriesOption === 3) {
          const utcRecurrenceEndDate = requestData.recurrence.recurrenceEndDate;
          let existingRecurrenceEndDate = moment(utcRecurrenceEndDate)
            .tz(deliveryData.timezone)
            .format('YYYY-MM-DD');
          const newRecurrenceEndDate = deliveryData.recurrenceEndDate;
          if (!moment(existingRecurrenceEndDate).isSame(moment(newRecurrenceEndDate))) {
            const dates = [];
            const chosenTimezoneDeliveryStart = moment.tz(
              `${deliveryData.recurrenceEndDate}  '00:00'`,
              'YYYY-MM-DD HH:mm',
              deliveryData.timezone,
            );
            const utcDate = chosenTimezoneDeliveryStart
              .clone()
              .tz('UTC')
              .format('YYYY-MM-DD HH:mm:ssZ');
            await RequestRecurrenceSeries.update(
              {
                recurrenceEndDate: utcDate,
              },
              {
                where: {
                  id: deliveryData.recurrenceId,
                },
              },
            );
            while (moment(existingRecurrenceEndDate).isBefore(moment(newRecurrenceEndDate))) {
              existingRecurrenceEndDate = moment(existingRecurrenceEndDate).add(1, 'day');
              dates.push(moment(existingRecurrenceEndDate).format('MM/DD/YYYY'));
            }
            await this.createCopyofDeliveryRequest(
              requestData,
              deliveryData,
              dates,
              loginUser,
              newRecurrenceId || deliveryData.recurrenceId,
            );
          }
        }
        if (deliveryData.seriesOption === 2 && previousRecordInSeries) {
          // const chosenTimezoneDeliveryStart = moment.tz(
          //   `${deliveryData.previousSeriesRecurrenceEndDate}  '00:00'`,
          //   'YYYY-MM-DD HH:mm',
          //   deliveryData.timezone,
          // );
          // const utcDate = chosenTimezoneDeliveryStart
          //   .clone()
          //   .tz('UTC')
          //   .format('YYYY-MM-DD HH:mm:ssZ');
          await RequestRecurrenceSeries.update(
            {
              recurrenceEndDate: previousRecordInSeries.deliveryStart,
            },
            {
              where: {
                id: previousRecordInSeries.recurrenceId,
              },
            },
          );
        }
      }
      for (let indexLoop = 0; indexLoop < editSeriesRequests.length; indexLoop += 1) {
        const seriesData = editSeriesRequests[indexLoop];
        const idDetails = await DeliveryRequest.findOne({
          where: [
            Sequelize.and({
              id: seriesData.id,
            }),
          ],
        });
        if (!idDetails) {
          return done(null, { message: 'Delivery Booking id is not available' });
        }
        const existsDeliveryRequest = await DeliveryRequest.getSingleDeliveryRequestData({
          id: +idDetails.id,
        });
        if (existsDeliveryRequest) {
          await this.checkInputDatas(inputData, async (checkResponse, checkError) => {
            if (checkError) {
              return done(null, checkError);
            }
            const memberData = await Member.getBy({
              UserId: loginUser.id,
              ProjectId: deliveryData.ProjectId,
            });
            const history = {
              DeliveryRequestId: idDetails.id,
              DeliveryId: idDetails.DeliveryId,
              MemberId: memberData.id,
              type: 'edit',
              description: `${loginUser.firstName} ${loginUser.lastName} Edited this Delivery Booking.`,
            };
            const notification = history;
            let lastData = {};
            lastData = await CraneRequest.findOne({
              where: { ProjectId: +deliveryData.ProjectId, isDeleted: false },
              order: [['CraneRequestId', 'DESC']],
            });
            const deliveryRequestList = await DeliveryRequest.findOne({
              where: {
                ProjectId: +deliveryData.ProjectId,
                isDeleted: false,
                isAssociatedWithCraneRequest: true,
              },
              order: [['CraneRequestId', 'DESC']],
            });
            if (deliveryRequestList) {
              if (lastData) {
                if (deliveryRequestList.CraneRequestId > lastData.CraneRequestId) {
                  lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
                }
              } else {
                lastData = {};
                lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
              }
            }
            if (lastData) {
              const data = lastData.CraneRequestId;
              lastData.CraneRequestId = 0;
              lastData.CraneRequestId = data + 1;
            } else {
              lastData = {};
              lastData.CraneRequestId = 1;
            }
            let craneId = 0;
            const newId = JSON.parse(JSON.stringify(lastData));
            if (newId && newId.CraneRequestId !== null && newId.CraneRequestId !== undefined) {
              craneId = newId.CraneRequestId;
            }
            const DeliverParam = {
              description: deliveryData.description,
              escort: deliveryData.escort,
              vehicleDetails: deliveryData.vehicleDetails,
              notes: deliveryData.notes,
              isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
              requestType: deliveryData.requestType,
              cranePickUpLocation: deliveryData.cranePickUpLocation,
              craneDropOffLocation: deliveryData.craneDropOffLocation,
              // CraneRequestId: deliveryData.CraneRequestId,
              recurrenceId: deliveryData.seriesOption !== 1 ? newRecurrenceId : null,
              LocationId: deliveryData.LocationId,
            };
            if (
              !idDetails.CraneRequestId &&
              deliveryData.requestType === 'deliveryRequestWithCrane'
            ) {
              DeliverParam.CraneRequestId = craneId;
            }
            if (deliveryData.seriesOption === 1) {
              DeliverParam.deliveryStart = deliveryData.deliveryStart;
              DeliverParam.deliveryEnd = deliveryData.deliveryEnd;
            }
            if (deliveryData.seriesOption === 2 || deliveryData.seriesOption === 3) {
              const utcDeliveryStartTimestamp = moment.utc(idDetails.deliveryStart);
              const localStartTimestamp = utcDeliveryStartTimestamp.tz(deliveryData.timezone);
              const utcDeliveryEndTimestamp = moment.utc(idDetails.deliveryEnd);
              const localEndTimestamp = utcDeliveryEndTimestamp.tz(deliveryData.timezone);
              DeliverParam.deliveryStart = await this.convertTimezoneToUtc(
                moment(localStartTimestamp).format('MM/DD/YYYY'),
                deliveryData.timezone,
                deliveryData.deliveryStartTime,
              );
              DeliverParam.deliveryEnd = await this.convertTimezoneToUtc(
                moment(localEndTimestamp).format('MM/DD/YYYY'),
                deliveryData.timezone,
                deliveryData.deliveryEndTime,
              );
            }

            if (
              ((memberData.RoleId === 2 || memberData.RoleId === 1) &&
                idDetails.status === 'Approved') ||
              memberData.isAutoApproveEnabled ||
              projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
            ) {
              DeliverParam.status = 'Approved';
              DeliverParam.approvedBy = memberData.id;
              DeliverParam.approved_at = new Date();
            }
            await DeliveryRequest.update(DeliverParam, {
              where: { id: idDetails.id },
            });
            const { companies, persons, define } = deliveryData;
            const gates = [deliveryData.GateId];
            const equipments = [deliveryData.EquipmentId];
            const condition = Sequelize.and({
              ProjectId: deliveryData.ProjectId,
              DeliveryId: idDetails.id,
            });
            const updateParam = {
              DeliveryId: idDetails.id,
              DeliveryCode: idDetails.DeliveryId,
              ProjectId: deliveryData.ProjectId,
              isDeleted: false,
              isActive: true,
            };
            const existCompanies = await DeliverCompany.findAll({ where: condition });
            const existGate = await DeliverGate.findAll({ where: condition });
            const existEquipment = await DeliverEquipment.findAll({ where: condition });
            const existPerson = await DeliveryPerson.findAll({ where: condition });
            const existDefine = await DeliverDefine.findAll({ where: condition });
            await this.updateValues(condition, async (response, error) => {
              if (!error) {
                const addedCompany = [];
                companies.forEach(async (element, i) => {
                  const index = existCompanies.findIndex((item) => item.CompanyId === element);
                  const companyParam = updateParam;
                  companyParam.CompanyId = element;
                  if (index !== -1) {
                    await DeliverCompany.update(companyParam, {
                      where: { id: existCompanies[index].id },
                    });
                    if (existCompanies[index].isDeleted !== false) {
                      addedCompany.push(existCompanies[index]);
                    }
                  } else {
                    const newCompanyData = await DeliverCompany.createInstance(companyParam);
                    addedCompany.push(newCompanyData);
                  }
                });
                const addedGate = [];
                gates.forEach(async (element, i) => {
                  const index = existGate.findIndex((item) => item.GateId === element);
                  const gateParam = updateParam;
                  gateParam.GateId = element;
                  if (index !== -1) {
                    await DeliverGate.update(gateParam, { where: { id: existGate[index].id } });
                    if (existGate[index].isDeleted !== false) {
                      addedGate.push(existGate[index]);
                    }
                  } else {
                    const newGateData = await DeliverGate.createInstance(gateParam);
                    addedGate.push(newGateData);
                  }
                });
                const addedEquipment = [];
                equipments.forEach(async (element, i) => {
                  const index = existEquipment.findIndex((item) => item.EquipmentId === element);
                  const equipmentParam = updateParam;
                  equipmentParam.EquipmentId = element;
                  if (index !== -1) {
                    await DeliverEquipment.update(equipmentParam, {
                      where: { id: existEquipment[index].id },
                    });
                    if (existEquipment[index].isDeleted !== false) {
                      addedEquipment.push(existEquipment[index]);
                    }
                  } else {
                    const newEquipmentData = await DeliverEquipment.createInstance(equipmentParam);
                    addedEquipment.push(newEquipmentData);
                  }
                });
                const addedPerson = [];
                persons.forEach(async (element, i) => {
                  const index = existPerson.findIndex((item) => item.MemberId === element);
                  const memberParam = updateParam;
                  memberParam.MemberId = element;
                  if (index !== -1) {
                    await DeliveryPerson.update(memberParam, {
                      where: { id: existPerson[index].id },
                    });
                    if (existPerson[index].isDeleted !== false) {
                      addedPerson.push(existPerson[index]);
                    }
                  } else {
                    const newPersonData = await DeliveryPerson.createInstance(memberParam);
                    addedPerson.push(newPersonData);
                  }
                });
                const addedDefineData = [];
                define.forEach(async (element, i) => {
                  const index = existDefine.findIndex(
                    (item) => item.DeliverDefineWorkId === element,
                  );
                  const defineParam = updateParam;
                  defineParam.DeliverDefineWorkId = element;
                  if (index !== -1) {
                    await DeliverDefine.update(defineParam, {
                      where: { id: existDefine[index].id },
                    });
                    if (existDefine[index].isDeleted !== false) {
                      addedDefineData.push(existDefine[index]);
                    }
                  } else {
                    const newDefineData = await DeliverDefine.createInstance(defineParam);
                    addedDefineData.push(newDefineData);
                  }
                });
                const locationChosen = await Locations.findOne({
                  where: {
                    ProjectId: deliveryData.ProjectId,
                    id: deliveryData.LocationId,
                  },
                });
                history.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Delivery Booking, ${deliveryData.description}`;
                history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Updated the Delivery Booking, ${deliveryData.description}. Location: ${locationChosen.locationPath}.`;
                history.MemberId = memberData.id;
                history.firstName = loginUser.firstName;
                history.profilePic = loginUser.profilePic;
                history.createdAt = new Date();
                history.ProjectId = deliveryData.ProjectId;
                const projectDetails = await Project.findByPk(deliveryData.ProjectId);
                history.projectName = projectDetails.projectName;
                notification.ProjectId = idDetails.ProjectId;
                notification.title = `Delivery Booking Updated by ${loginUser.firstName} ${loginUser.lastName}`;
                if (
                  existsDeliveryRequest &&
                  existsDeliveryRequest.recurrence &&
                  existsDeliveryRequest.recurrence.recurrence
                ) {
                  notification.recurrenceType = `${existsDeliveryRequest.recurrence.recurrence
                    } From ${moment(existsDeliveryRequest.recurrence.recurrenceStartDate).format(
                      'MM/DD/YYYY',
                    )} to ${moment(existsDeliveryRequest.recurrence.recurrenceEndDate).format(
                      'MM/DD/YYYY',
                    )}`;
                }
                notification.requestType = 'deliveryRequest';
                const newNotification = await Notification.createInstance(notification);
                const memberLocationPreference = await LocationNotificationPreferences.findAll({
                  where: {
                    ProjectId: deliveryData.ProjectId,
                    LocationId: deliveryData.LocationId,
                    follow: true,
                  },
                  include: [
                    {
                      association: 'Member',
                      attributes: ['id', 'RoleId'],
                      where: {
                        [Op.and]: [
                          {
                            id: { [Op.ne]: memberData.id },
                          },
                        ],
                      },
                      include: [
                        {
                          association: 'User',
                          attributes: ['id', 'firstName', 'lastName', 'email'],
                        },
                      ],
                    },
                  ],
                });
                const locationFollowMembers = [];
                memberLocationPreference.forEach(async (element) => {
                  locationFollowMembers.push(element.Member.id);
                });
                const adminData = await Member.findAll({
                  where: {
                    [Op.and]: [
                      { ProjectId: deliveryData.ProjectId },
                      { isDeleted: false },
                      { id: { [Op.in]: persons } },
                      { id: { [Op.ne]: newNotification.MemberId } },
                      { id: { [Op.notIn]: locationFollowMembers } },
                    ],
                  },
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'firstName', 'lastName', 'email'],
                    },
                  ],
                  attributes: ['id', 'RoleId'],
                });
                if (memberLocationPreference && memberLocationPreference.length > 0) {
                  await pushNotification.sendMemberLocationPreferencePushNotification(
                    memberLocationPreference,
                    deliveryData.DeliveryRequestId,
                    history.locationFollowDescription,
                    deliveryData.requestType,
                    deliveryData.ProjectId,
                    deliveryData.id,
                    5,
                  );
                  await notificationHelper.createMemberDeliveryLocationInAppNotification(
                    DeliveryPersonNotification,
                    deliveryData.ProjectId,
                    newNotification.id,
                    memberLocationPreference,
                    5,
                  );
                }
                history.adminData = adminData;
                if (memberLocationPreference && memberLocationPreference.length > 0) {
                  history.memberData = [];
                  history.memberData.push(...memberLocationPreference);
                }
                await notificationHelper.createDeliveryPersonNotification(
                  adminData,
                  [],
                  projectDetails,
                  newNotification,
                  DeliveryPersonNotification,
                  memberData,
                  loginUser,
                  5,
                  'updated a',
                  'Delivery Request',
                  `delivery Booking (${idDetails.DeliveryId} - ${idDetails.description})`,
                  idDetails.id,
                );
                const checkMemberNotification = await NotificationPreference.findAll({
                  where: {
                    ProjectId: deliveryData.ProjectId,
                    isDeleted: false,
                  },
                  attributes: [
                    'id',
                    'MemberId',
                    'ProjectId',
                    'ParentCompanyId',
                    'NotificationPreferenceItemId',
                    'instant',
                    'dailyDigest',
                  ],
                  include: [
                    {
                      association: 'NotificationPreferenceItem',
                      where: {
                        id: 5,
                        isDeleted: false,
                      },
                      attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                    },
                  ],
                });
                history.notificationPreference = checkMemberNotification;
                const updatedDeliveryRequest = await DeliveryRequest.getSingleDeliveryRequestData({
                  id: +idDetails.id,
                });
                if (
                  updatedDeliveryRequest.status === 'Approved' &&
                  idDetails.status !== 'Approved' &&
                  idDetails.isQueued === false
                ) {
                  const object = {
                    ProjectId: deliveryData.ProjectId,
                    MemberId: memberData.id,
                    DeliveryRequestId: updatedDeliveryRequest.id,
                    isDeleted: false,
                    type: 'approved',
                    description: history.description,
                  };
                  await DeliverHistory.createInstance(object);
                }
                if (!deliveryData.ndrStatus) {
                  await this.updateEditDeliveryRequestHistory(
                    deliveryData,
                    existsDeliveryRequest,
                    updatedDeliveryRequest,
                    history,
                    loginUser,
                  );
                  await pushNotification.sendDeviceToken(history, 5, deliveryData.ProjectId);
                }
                const editedNDR = await DeliveryRequest.getNDRData({
                  id: idDetails.id,
                });
                if (editedNDR.isQueued === true) {
                  if (
                    editedNDR.description &&
                    editedNDR.deliveryStart &&
                    editedNDR.deliveryEnd &&
                    editedNDR.memberDetails.length > 0 &&
                    editedNDR.companyDetails.length > 0 &&
                    // editedNDR.defineWorkDetails.length > 0 &&
                    editedNDR.gateDetails.length > 0 &&
                    editedNDR.equipmentDetails.length > 0 &&
                    editedNDR.escort !== null
                  ) {
                    await DeliveryRequest.update(
                      { isAllDetailsFilled: true },
                      {
                        where: { id: editedNDR.id },
                      },
                    );
                    if (deliveryData.updateQueuedRequest === 1) {
                      const queuedRequestPayload = {
                        isQueued: false,
                      };
                      if (
                        editedNDR.isQueued === true &&
                        (memberData.RoleId === roleDetails.id ||
                          memberData.RoleId === accountRoleDetails.id ||
                          memberData.isAutoApproveEnabled ||
                          projectSettingDetails.ProjectSettings.isAutoApprovalEnabled)
                      ) {
                        queuedRequestPayload.status = 'Approved';
                        queuedRequestPayload.approvedBy = memberData.id;
                        queuedRequestPayload.approved_at = new Date();
                        const historyUpdateObject = {
                          ProjectId: +deliveryData.ProjectId,
                          MemberId: memberData.id,
                          DeliveryRequestId: editedNDR.id,
                          isDeleted: false,
                          type: 'approved',
                          description: `${loginUser.firstName} ${loginUser.lastName} Approved Delivery Booking, ${editedNDR.description}.`,
                        };
                        await DeliverHistory.createInstance(historyUpdateObject);
                      }
                      await DeliveryRequest.update(queuedRequestPayload, {
                        where: { id: editedNDR.id },
                      });
                    }
                  }
                }
                if (!deliveryData.ndrStatus) {
                  let tagsUpdated = false;
                  let fieldsChanged = false;
                  if (
                    editedNDR.defineWorkDetails.length > 0 &&
                    existsDeliveryRequest.defineWorkDetails.length > 0
                  ) {
                    const addedDfow1 = editedNDR.defineWorkDetails.filter((el) => {
                      return !existsDeliveryRequest.defineWorkDetails.find((element) => {
                        return element.id === el.id;
                      });
                    });
                    const deletedDfow1 = existsDeliveryRequest.defineWorkDetails.filter((el) => {
                      return !existsDeliveryRequest.defineWorkDetails.find((element) => {
                        return element.id === el.id;
                      });
                    });
                    if (addedDfow1.length > 0) {
                      tagsUpdated = true;
                    }
                    if (deletedDfow1.length > 0) {
                      tagsUpdated = true;
                    }
                  }
                  if (
                    editedNDR.gateDetails.length > 0 &&
                    existsDeliveryRequest.gateDetails.length > 0
                  ) {
                    const addedGate1 = editedNDR.gateDetails.filter((el) => {
                      return !existsDeliveryRequest.gateDetails.find((element) => {
                        return element.Gate.id === el.Gate.id;
                      });
                    });
                    const deletedGate1 = existsDeliveryRequest.gateDetails.filter((el) => {
                      return !existsDeliveryRequest.gateDetails.find((element) => {
                        return element.Gate.id === el.Gate.id;
                      });
                    });
                    if (addedGate1.length > 0) {
                      tagsUpdated = true;
                    }
                    if (deletedGate1.length > 0) {
                      tagsUpdated = true;
                    }
                  }
                  if (
                    editedNDR.equipmentDetails.length > 0 &&
                    existsDeliveryRequest.equipmentDetails.length > 0
                  ) {
                    const addedEquipment1 = editedNDR.equipmentDetails.filter((el) => {
                      return !existsDeliveryRequest.equipmentDetails.find((element) => {
                        return element.Equipment.id === el.Equipment.id;
                      });
                    });
                    const deletedEquipment1 = existsDeliveryRequest.equipmentDetails.filter(
                      (el) => {
                        return !existsDeliveryRequest.equipmentDetails.find((element) => {
                          return element.Equipment.id === el.Equipment.id;
                        });
                      },
                    );
                    if (addedEquipment1.length > 0) {
                      tagsUpdated = true;
                    }
                    if (deletedEquipment1.length > 0) {
                      tagsUpdated = true;
                    }
                  }
                  if (
                    editedNDR.companyDetails.length > 0 &&
                    existsDeliveryRequest.companyDetails.length > 0
                  ) {
                    const addedCompany1 = editedNDR.companyDetails.filter((el) => {
                      return !existsDeliveryRequest.companyDetails.find((element) => {
                        return element.Company.id === el.Company.id;
                      });
                    });
                    const deletedCompany1 = existsDeliveryRequest.companyDetails.filter((el) => {
                      return !existsDeliveryRequest.companyDetails.find((element) => {
                        return element.Company.id === el.Company.id;
                      });
                    });
                    if (addedCompany1.length > 0) {
                      tagsUpdated = true;
                    }
                    if (deletedCompany1.length > 0) {
                      tagsUpdated = true;
                    }
                  }
                  if (
                    editedNDR.memberDetails.length > 0 &&
                    existsDeliveryRequest.memberDetails.length > 0
                  ) {
                    const addedMember1 = editedNDR.memberDetails.filter((el) => {
                      return !existsDeliveryRequest.memberDetails.find((element) => {
                        return element.Member.id === el.Member.id;
                      });
                    });
                    const deletedMember1 = existsDeliveryRequest.memberDetails.filter((el) => {
                      return !editedNDR.memberDetails.find((element) => {
                        return element.Member.id === el.Member.id;
                      });
                    });
                    if (addedMember1.length > 0) {
                      tagsUpdated = true;
                    }
                    if (deletedMember1.length > 0) {
                      tagsUpdated = true;
                    }
                  }
                  if (
                    existsDeliveryRequest.description !== editedNDR.description ||
                    existsDeliveryRequest.CraneRequestId !== editedNDR.CraneRequestId ||
                    existsDeliveryRequest.LocationId !== editedNDR.LocationId ||
                    existsDeliveryRequest.requestType !== editedNDR.requestType ||
                    existsDeliveryRequest.vehicleDetails !== editedNDR.vehicleDetails ||
                    existsDeliveryRequest.notes !== editedNDR.notes ||
                    existsDeliveryRequest.isAssociatedWithCraneRequest !==
                    editedNDR.isAssociatedWithCraneRequest ||
                    existsDeliveryRequest.escort !== editedNDR.escort ||
                    existsDeliveryRequest.craneDropOffLocation !== editedNDR.craneDropOffLocation ||
                    existsDeliveryRequest.cranePickUpLocation !== editedNDR.cranePickUpLocation ||
                    tagsUpdated ||
                    existsDeliveryRequest.recurrence !== editedNDR.recurrence ||
                    existsDeliveryRequest.chosenDateOfMonth !== editedNDR.chosenDateOfMonth ||
                    existsDeliveryRequest.dateOfMonth !== editedNDR.dateOfMonth ||
                    existsDeliveryRequest.monthlyRepeatType !== editedNDR.monthlyRepeatType ||
                    existsDeliveryRequest.days !== editedNDR.days ||
                    existsDeliveryRequest.repeatEveryType !== editedNDR.repeatEveryType ||
                    existsDeliveryRequest.repeatEveryCount !== editedNDR.repeatEveryCount
                  ) {
                    fieldsChanged = true;
                  }
                  let deliveryDateTimeChanged = false;
                  if (
                    moment(existsDeliveryRequest.deliveryStart).format('h:mm a') !==
                    moment(editedNDR.deliveryStart).format('h:mm a') ||
                    moment(existsDeliveryRequest.deliveryEnd).format('h:mm a') !==
                    moment(editedNDR.deliveryEnd).format('h:mm a')
                  ) {
                    deliveryDateTimeChanged = true;
                  }
                  if (existsDeliveryRequest.status === 'Delivered') {
                    if (fieldsChanged && memberData.RoleId === 2) {
                      await DeliveryRequest.update(
                        { status: 'Approved' },
                        {
                          where: { id: editedNDR.id },
                        },
                      );
                    }
                    if (
                      (fieldsChanged || deliveryDateTimeChanged) &&
                      memberData.RoleId !== 2 &&
                      !memberData.isAutoApproveEnabled &&
                      !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      await DeliveryRequest.update(
                        { status: 'Pending' },
                        {
                          where: { id: editedNDR.id },
                        },
                      );
                    }
                  }
                  if (existsDeliveryRequest.status === 'Approved') {
                    if (
                      (fieldsChanged || deliveryDateTimeChanged) &&
                      memberData.RoleId !== 2 &&
                      !memberData.isAutoApproveEnabled &&
                      !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      await DeliveryRequest.update(
                        { status: 'Pending' },
                        {
                          where: { id: editedNDR.id },
                        },
                      );
                    }
                    if (
                      ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId === 2) ||
                      memberData.isAutoApproveEnabled ||
                      projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      await DeliveryRequest.update(
                        { status: 'Approved' },
                        {
                          where: { id: editedNDR.id },
                        },
                      );
                    }
                  }
                  if (
                    existsDeliveryRequest.status === 'Expired' ||
                    existsDeliveryRequest.status === 'Declined'
                  ) {
                    if (
                      (fieldsChanged || deliveryDateTimeChanged) &&
                      memberData.RoleId !== 2 &&
                      !memberData.isAutoApproveEnabled &&
                      !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      await DeliveryRequest.update(
                        { status: 'Pending' },
                        {
                          where: { id: editedNDR.id },
                        },
                      );
                    }
                    if (
                      ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId === 2) ||
                      memberData.isAutoApproveEnabled ||
                      projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      const isStatusDataUpdated = await DeliveryRequest.update(
                        { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
                        {
                          where: { id: editedNDR.id },
                        },
                      );
                      if (isStatusDataUpdated) {
                        const object = {
                          ProjectId: deliveryData.ProjectId,
                          MemberId: memberData.id,
                          DeliveryRequestId: updatedDeliveryRequest.id,
                          isDeleted: false,
                          type: 'approved',
                          description: `${loginUser.firstName} ${loginUser.lastName} Approved the Delivery Booking, ${deliveryData.description}`,
                        };
                        await DeliverHistory.createInstance(object);
                      }
                    }
                  }
                  if (existsDeliveryRequest.status === 'Pending') {
                    if (
                      (fieldsChanged || deliveryDateTimeChanged) &&
                      memberData.RoleId !== 2 &&
                      (memberData.isAutoApproveEnabled ||
                        projectSettingDetails.ProjectSettings.isAutoApprovalEnabled)
                    ) {
                      const isStatusDataUpdated = await DeliveryRequest.update(
                        { status: 'Approved' },
                        {
                          where: { id: editedNDR.id },
                        },
                      );
                      if (isStatusDataUpdated) {
                        const object = {
                          ProjectId: deliveryData.ProjectId,
                          MemberId: memberData.id,
                          DeliveryRequestId: updatedDeliveryRequest.id,
                          isDeleted: false,
                          type: 'approved',
                          description: `${loginUser.firstName} ${loginUser.lastName} Approved the Delivery Request, ${deliveryData.description}`,
                        };
                        await DeliverHistory.createInstance(object);
                      }
                    }
                    if (
                      ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId === 2) ||
                      memberData.isAutoApproveEnabled ||
                      projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      const isStatusDataUpdated = await DeliveryRequest.update(
                        { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
                        {
                          where: { id: editedNDR.id },
                        },
                      );
                      if (isStatusDataUpdated) {
                        const object = {
                          ProjectId: deliveryData.ProjectId,
                          MemberId: memberData.id,
                          DeliveryRequestId: updatedDeliveryRequest.id,
                          isDeleted: false,
                          type: 'approved',
                          description: `${loginUser.firstName} ${loginUser.lastName} Approved the Delivery Request, ${deliveryData.description}`,
                        };
                        await DeliverHistory.createInstance(object);
                      }
                    }
                  }
                  if (+memberData.RoleId === 4 || +memberData.RoleId === 3) {
                    const userEmails = await this.getMemberDetailData(
                      history,
                      memberLocationPreference,
                    );
                    if (userEmails.length > 0) {
                      userEmails.forEach(async (element) => {
                        if (element.RoleId === 2) {
                          let name;
                          if (!element.firstName) {
                            name = 'user';
                          } else {
                            name = `${element.firstName} ${element.lastName}`;
                          }
                          const memberRole = await Role.findOne({
                            where: {
                              id: memberData.RoleId,
                              isDeleted: false,
                            },
                          });
                          const mailPayload = {
                            name,
                            email: element.email,
                            content: `We would like to inform you that 
                          ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has updated a delivery booking ${idDetails.DeliveryId} and waiting for your approval.Kindly review the booking and update the status.`,
                          };
                          const isMemberFollowLocation =
                            await LocationNotificationPreferences.findOne({
                              where: {
                                MemberId: +element.MemberId,
                                ProjectId: +deliveryData.ProjectId,
                                LocationId: +deliveryData.LocationId,
                                isDeleted: false,
                                // follow: true,
                              },
                            });
                          if (isMemberFollowLocation) {
                            const memberNotification = await NotificationPreference.findOne({
                              where: {
                                MemberId: +element.MemberId,
                                ProjectId: +deliveryData.ProjectId,
                                isDeleted: false,
                              },
                              include: [
                                {
                                  association: 'NotificationPreferenceItem',
                                  where: {
                                    id: 9,
                                    isDeleted: false,
                                  },
                                },
                              ],
                            });
                            if (memberNotification && memberNotification.instant) {
                              await MAILER.sendMail(
                                mailPayload,
                                'notifyPAForReApproval',
                                `Delivery Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                                `Delivery Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                                async (info, err) => {
                                  console.log(info, err);
                                },
                              );
                            }
                            if (memberNotification && memberNotification.dailyDigest) {
                              await this.createDailyDigestDataApproval(
                                +memberData.RoleId,
                                +element.MemberId,
                                +deliveryData.ProjectId,
                                +deliveryData.ParentCompanyId,
                                loginUser,
                                'updated a',
                                'Delivery Request',
                                `delivery Booking (${idDetails.DeliveryId} - ${idDetails.description})`,
                                'and waiting for your approval',
                                idDetails.id,
                              );
                            }
                          }
                        }
                      });
                      return done(history, false);
                    }
                  } else {
                    return done(history, false);
                  }
                } else {
                  return done(history, false);
                }
              } else {
                return done(null, error);
              }
            });
          });
        }
      }
    } catch (e) {
      return done(null, e);
    }
  },
  async updateCompanyHistory(addedCompany, deletedCompany, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Company`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Company`;
    addedCompany.forEach(async (element, i) => {
      const newCompanyData = await Company.findOne({
        where: { id: element.CompanyId },
      });
      if (i === 0) {
        if (i === addedCompany.length - 1) {
          addDesc += ` ${newCompanyData.companyName}`;
          newHistory.description = addDesc;
          DeliverHistory.createInstance(newHistory);
        } else {
          addDesc += ` ${newCompanyData.companyName}`;
        }
      } else if (i === addedCompany.length - 1) {
        addDesc += `,${newCompanyData.companyName}`;
        newHistory.description = addDesc;
        DeliverHistory.createInstance(newHistory);
      } else {
        addDesc += `,${newCompanyData.companyName}`;
      }
    });
    deletedCompany.forEach(async (element, i) => {
      const newCompanyData = await Company.findOne({
        where: { id: element.CompanyId },
      });
      if (i === 0) {
        if (i === deletedCompany.length - 1) {
          deleteDesc += ` ${newCompanyData.companyName}`;
          newHistory.description = deleteDesc;
          DeliverHistory.createInstance(newHistory);
        } else {
          deleteDesc += ` ${newCompanyData.companyName}`;
        }
      } else if (i === deletedCompany.length - 1) {
        deleteDesc += `,${newCompanyData.companyName}`;
        newHistory.description = deleteDesc;
        DeliverHistory.createInstance(newHistory);
      } else {
        deleteDesc += `,${newCompanyData.companyName}`;
      }
    });
  },
  async updateGateHistory(addedGate, deletedGate, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Gate`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Gate`;
    addedGate.forEach(async (element, i) => {
      const newGateData = await Gates.findOne({
        where: { id: element.GateId },
      });
      if (i === 0) {
        if (i === addedGate.length - 1) {
          addDesc += ` ${newGateData.gateName}`;
          newHistory.description = addDesc;
          DeliverHistory.createInstance(newHistory);
        } else {
          addDesc += ` ${newGateData.gateName}`;
        }
      } else if (i === addedGate.length - 1) {
        addDesc += `,${newGateData.gateName}`;
        newHistory.description = addDesc;
        DeliverHistory.createInstance(newHistory);
      } else {
        addDesc += `,${newGateData.gateName}`;
      }
    });
    deletedGate.forEach(async (element, i) => {
      const newGateData = await Gates.findOne({
        where: { id: element.GateId },
      });
      if (i === 0) {
        if (i === deletedGate.length - 1) {
          deleteDesc += ` ${newGateData.gateName}`;
          newHistory.description = deleteDesc;
          DeliverHistory.createInstance(newHistory);
        } else {
          deleteDesc += ` ${newGateData.gateName}`;
        }
      } else if (i === deletedGate.length - 1) {
        deleteDesc += `,${newGateData.gateName}`;
        newHistory.description = deleteDesc;
        DeliverHistory.createInstance(newHistory);
      } else {
        deleteDesc += `,${newGateData.gateName}`;
      }
    });
  },
  async updateEquipmentHistory(addedEquipment, deletedEquipment, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Equipment`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Equipment`;
    addedEquipment.forEach(async (element, i) => {
      const newEquipmentData = await Equipments.findOne({
        where: { id: element.EquipmentId },
      });
      if (i === 0) {
        if (i === addedEquipment.length - 1) {
          addDesc += ` ${newEquipmentData.equipmentName}`;
          newHistory.description = addDesc;
          DeliverHistory.createInstance(newHistory);
        } else {
          addDesc += ` ${newEquipmentData.equipmentName}`;
        }
      } else if (i === addedEquipment.length - 1) {
        addDesc += `,${newEquipmentData.equipmentName}`;
        newHistory.description = addDesc;
        DeliverHistory.createInstance(newHistory);
      } else {
        addDesc += `,${newEquipmentData.equipmentName}`;
      }
    });
    deletedEquipment.forEach(async (element, i) => {
      const newEquipmentData = await Equipments.findOne({
        where: { id: element.EquipmentId },
      });
      if (i === 0) {
        if (i === deletedEquipment.length - 1) {
          deleteDesc += ` ${newEquipmentData.equipmentName}`;
          newHistory.description = deleteDesc;
          DeliverHistory.createInstance(newHistory);
        } else {
          deleteDesc += ` ${newEquipmentData.equipmentName}`;
        }
      } else if (i === deletedEquipment.length - 1) {
        deleteDesc += `,${newEquipmentData.equipmentName}`;
        newHistory.description = deleteDesc;
        DeliverHistory.createInstance(newHistory);
      } else {
        deleteDesc += `,${newEquipmentData.equipmentName}`;
      }
    });
  },
  async updatePersonHistory(addedPerson, deletedPerson, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Member`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Member`;
    addedPerson.forEach(async (element, i) => {
      const newMemberData = await Member.findOne({
        where: { id: element.MemberId, isDeleted: false },
        include: [
          {
            association: 'User',
            attributes: ['firstName', 'lastName'],
          },
        ],
      });
      if (i === 0) {
        if (i === addedPerson.length - 1) {
          if (newMemberData.User.firstName) {
            addDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
          }
          newHistory.description = addDesc;
          DeliverHistory.createInstance(newHistory);
        } else if (newMemberData.User.firstName) {
          addDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
      } else if (i === addedPerson.length - 1) {
        if (newMemberData.User.firstName) {
          addDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
        newHistory.description = addDesc;
        DeliverHistory.createInstance(newHistory);
      } else if (newMemberData.User.firstName) {
        addDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
      }
    });
    deletedPerson.forEach(async (element, i) => {
      const newMemberData = await Member.findOne({
        where: { id: element.MemberId, isDeleted: false },
        include: [
          {
            association: 'User',
            attributes: ['firstName', 'lastName'],
          },
        ],
      });
      if (i === 0) {
        if (i === deletedPerson.length - 1) {
          if (newMemberData.User.firstName) {
            deleteDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
          }
          newHistory.description = deleteDesc;
          DeliverHistory.createInstance(newHistory);
        } else if (newMemberData.User.firstName) {
          deleteDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
      } else if (i === deletedPerson.length - 1) {
        if (newMemberData.User.firstName) {
          deleteDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
        newHistory.description = deleteDesc;
        DeliverHistory.createInstance(newHistory);
      } else if (newMemberData.User.firstName) {
        deleteDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
      }
    });
  },
  async updateDefineHistory(addedDefine, deletedDefine, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the DFOW`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the DFOW`;
    addedDefine.forEach(async (element, i) => {
      const newDefineData = await DeliverDefineWork.findOne({
        where: { id: element.DeliverDefineWorkId },
      });
      if (i === 0) {
        if (i === addedDefine.length - 1) {
          addDesc += ` ${newDefineData.DFOW}`;
          newHistory.description = addDesc;
          DeliverHistory.createInstance(newHistory);
        } else {
          addDesc += ` ${newDefineData.DFOW}`;
        }
      } else if (i === addedDefine.length - 1) {
        addDesc += `,${newDefineData.DFOW}`;
        newHistory.description = addDesc;
        DeliverHistory.createInstance(newHistory);
      } else {
        addDesc += `,${newDefineData.DFOW}`;
      }
    });
    deletedDefine.forEach(async (element, i) => {
      const newDefineData = await DeliverDefineWork.findOne({
        where: { id: element.DeliverDefineWorkId },
      });
      if (i === 0) {
        if (i === deletedDefine.length - 1) {
          deleteDesc += ` ${newDefineData.DFOW}`;
          newHistory.description = deleteDesc;
          DeliverHistory.createInstance(newHistory);
        } else {
          deleteDesc += ` ${newDefineData.DFOW}`;
        }
      } else if (i === deletedDefine.length - 1) {
        deleteDesc += `,${newDefineData.DFOW}`;
        newHistory.description = deleteDesc;
        DeliverHistory.createInstance(newHistory);
      } else {
        deleteDesc += `,${newDefineData.DFOW}`;
      }
    });
  },
  async lastDelivery(inputData, done) {
    try {
      const { params } = inputData;
      let data;
      const lastData = await DeliveryRequest.findOne({
        where: { ProjectId: params.ProjectId, isDeleted: false },
        order: [['DeliveryId', 'DESC']],
      });
      if (lastData) {
        data = lastData.DeliveryId + 1;
      } else {
        data = 1;
      }
      done({ DeliveryId: data }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async updateValues(condition, done) {
    try {
      await DeliverCompany.update({ isDeleted: true }, { where: condition });
      await DeliveryPerson.update({ isDeleted: true }, { where: condition });
      await DeliverGate.update({ isDeleted: true }, { where: condition });
      await DeliverEquipment.update({ isDeleted: true }, { where: condition });
      await DeliverDefine.update({ isDeleted: true }, { where: condition });
      done({ status: 'ok' }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async listNDR(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      let { sort } = inputData.body;
      let { sortByField } = inputData.body;
      let order;
      let searchCondition = {};
      if (params.void >= 1 && params.void <= 0) {
        done(null, { message: 'Please enter void as 1 or 0' });
      } else {
        const memberDetails = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: params.ProjectId,
            isDeleted: false,
            isActive: true,
          }),
        });
        if (memberDetails) {
          const voidDelivery = [];
          const voidList = await VoidList.findAll({
            where: {
              ProjectId: params.ProjectId,
              isDeliveryRequest: true,
              DeliveryRequestId: { [Op.ne]: null },
            },
          });
          voidList.forEach(async (element) => {
            voidDelivery.push(element.DeliveryRequestId);
          });
          const offset = (+params.pageNo - 1) * +params.pageSize;
          const condition = {
            ProjectId: +params.ProjectId,
            isQueued: incomeData.queuedNdr,
          };
          if (params.void === '0' || params.void === 0) {
            condition['$DeliveryRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidDelivery }],
            };
          } else {
            condition['$DeliveryRequest.id$'] = {
              [Op.and]: [{ [Op.in]: voidDelivery }],
            };
          }
          if (incomeData.descriptionFilter) {
            condition.description = {
              [Sequelize.Op.iLike]: `%${incomeData.descriptionFilter}%`,
            };
          }
          if (incomeData.pickFrom) {
            condition.cranePickUpLocation = {
              [Sequelize.Op.iLike]: `%${incomeData.pickFrom}%`,
            };
          }
          if (incomeData.pickTo) {
            condition.craneDropOffLocation = {
              [Sequelize.Op.iLike]: `%${incomeData.pickTo}%`,
            };
          }
          if (incomeData.equipmentFilter) {
            condition['$equipmentDetails.Equipment.id$'] = incomeData.equipmentFilter;
          }
          if (incomeData.locationFilter) {
            condition['$location.locationPath$'] = incomeData.locationFilter;
          }
          if (incomeData.statusFilter) {
            condition.status = incomeData.statusFilter;
          }
          if (incomeData.dateFilter) {
            const startDateTime = moment(incomeData.dateFilter, 'YYYY-MM-DD')
              .startOf('day')
              .utcOffset(Number(inputData.headers.timezoneoffset), true);
            const endDateTime = moment(incomeData.dateFilter, 'YYYY-MM-DD')
              .endOf('day')
              .utcOffset(Number(inputData.headers.timezoneoffset), true);
            condition.deliveryStart = {
              [Op.between]: [moment(startDateTime), moment(endDateTime)],
            };
          }
          if (incomeData.upcoming) {
            condition.deliveryStart = {
              [Op.gt]: new Date(),
            };
            order = 'ASC';
            sort = 'ASC';
            sortByField = 'deliveryStart';
          }
          if (incomeData.search) {
            const searchDefault = [
              {
                '$approverDetails.User.firstName$': {
                  [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                },
              },
              {
                '$equipmentDetails.Equipment.equipmentName$': {
                  [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                },
              },
              {
                '$location.locationPath$': {
                  [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                },
              },
              {
                description: {
                  [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                },
              },
              {
                cranePickUpLocation: {
                  [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                },
              },
              {
                craneDropOffLocation: {
                  [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                },
              },
            ];
            if (!Number.isNaN(+incomeData.search)) {
              searchCondition = {
                [Op.and]: [
                  {
                    [Op.or]: [
                      searchDefault,
                      {
                        [Op.and]: [
                          {
                            DeliveryId: +incomeData.search,
                            isDeleted: false,
                            ProjectId: +params.ProjectId,
                          },
                        ],
                      },
                    ],
                  },
                ],
              };
            } else {
              searchCondition = {
                [Op.and]: [
                  {
                    [Op.or]: searchDefault,
                  },
                ],
              };
            }
          }
          const roleId = memberDetails.RoleId;
          const memberId = memberDetails.id;
          if (
            incomeData.companyFilter ||
            incomeData.gateFilter ||
            incomeData.memberFilter ||
            incomeData.assignedFilter ||
            (memberDetails.RoleId === 4 &&
              (params.void === '0' || params.void === 0) &&
              !incomeData.upcoming) ||
            (memberDetails.RoleId === 3 &&
              (params.void === '0' || params.void === 0) &&
              !incomeData.upcoming)
          ) {
            const result = { count: 0, rows: [] };
            const deliveryList = await DeliveryRequest.getCalendarData(
              condition,
              roleId,
              memberId,
              searchCondition,
              order,
              sort,
              sortByField,
            );
            this.getSearchData(
              incomeData,
              deliveryList.rows,
              [],
              +params.pageSize,
              0,
              0,
              memberDetails,
              async (checkResponse, checkError) => {
                if (!checkError) {
                  this.getLimitData(
                    checkResponse,
                    0,
                    +params.pageSize,
                    [],
                    incomeData,
                    inputData.headers.timezoneoffset,
                    async (newResponse, newError) => {
                      if (!newError) {
                        if (sort === 'ASC') {
                          newResponse.sort(function (a, b) {
                            // slint-disable-next-line no-nested-ternary
                            return a[sortByField] > b[sortByField]
                              ? 1
                              : b[sortByField] > a[sortByField]
                                ? -1
                                : 0;
                          });
                        } else {
                          newResponse.sort(function (a, b) {
                            // slint-disable-next-line no-nested-ternary
                            return b[sortByField] > a[sortByField]
                              ? 1
                              : a[sortByField] > b[sortByField]
                                ? -1
                                : 0;
                          });
                        }
                        result.rows = newResponse.slice(offset, offset + +params.pageSize);
                        result.count = checkResponse.length;
                        done(result, false);
                      } else {
                        done(null, { message: 'Something went wrong' });
                      }
                    },
                  );
                } else {
                  done(null, { message: 'Something went wrong' });
                }
              },
            );
          } else {
            const newResult = { count: 0, rows: [] };
            const deliveryList = await DeliveryRequest.getAll(
              condition,
              roleId,
              memberId,
              +params.pageSize,
              offset,
              searchCondition,
              order,
              sort,
              sortByField,
            );
            this.getLimitData(
              deliveryList,
              0,
              +params.pageSize,
              [],
              incomeData,
              inputData.headers.timezoneoffset,
              async (newResponse, newError) => {
                if (!newError) {
                  if (sort === 'ASC') {
                    newResponse.sort(function (a, b) {
                      // slint-disable-next-line no-nested-ternary
                      return a[sortByField] > b[sortByField]
                        ? 1
                        : b[sortByField] > a[sortByField]
                          ? -1
                          : 0;
                    });
                  } else {
                    newResponse.sort(function (a, b) {
                      // slint-disable-next-line no-nested-ternary
                      return b[sortByField] > a[sortByField]
                        ? 1
                        : a[sortByField] > b[sortByField]
                          ? -1
                          : 0;
                    });
                  }
                  newResult.rows = newResponse.slice(offset, offset + +params.pageSize);
                  newResult.count = deliveryList.length;
                  done(newResult, false);
                } else {
                  done(null, { message: 'Something went wrong' });
                }
              },
            );
          }
        } else {
          done(null, { message: 'Project Id/Member does not exist' });
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getLimitData(result, index, limit, finalResult, incomeData, timezoneoffset, done) {
    if (index < limit) {
      finalResult.push(result);
      this.getLimitData(
        result,
        index + 1,
        limit,
        finalResult,
        incomeData,
        timezoneoffset,
        (response, err) => {
          if (!err) {
            done(response, false);
          } else {
            done(null, err);
          }
        },
      );
    } else {
      done(result, false);
    }
  },
  async getSearchData(incomeData, deliveryList, result, limit, index, count, memberDetails, done) {
    const elementValue = deliveryList[index];
    if (elementValue) {
      const element = JSON.parse(JSON.stringify(elementValue));
      const status = { companyCondition: true, gateCondition: true, memberCondition: true };
      if (incomeData.companyFilter) {
        const data = await DeliverCompany.findOne({
          where: {
            DeliveryId: element.id,
            CompanyId: incomeData.companyFilter,
            isDeleted: false,
          },
        });
        if (!data) {
          status.companyCondition = false;
        }
      }
      if (incomeData.gateFilter) {
        const data = await DeliverGate.findOne({
          where: {
            DeliveryId: element.id,
            GateId: incomeData.gateFilter,
            isDeleted: false,
          },
        });
        if (!data) {
          status.gateCondition = false;
        }
      }
      if (incomeData.memberFilter) {
        const data = await DeliveryPerson.findOne({
          where: {
            DeliveryId: element.id,
            MemberId: incomeData.memberFilter,
            isDeleted: false,
          },
        });
        if (!data) {
          status.memberCondition = false;
        }
      }
      // if (memberDetails.RoleId === 4 || memberDetails.RoleId === 3) {
      //   const data = await DeliveryPerson.findOne({
      //     where: {
      //       DeliveryId: element.id,
      //       MemberId: memberDetails.id,
      //       isDeleted: false,
      //       isActive: true,
      //     },
      //   });
      //   if (!data) {
      //     status.memberCondition = false;
      //   }
      // }

      if (status.companyCondition && status.gateCondition && status.memberCondition) {
        result.push(element);
      }
      if (index < deliveryList.length - 1) {
        this.getSearchData(
          incomeData,
          deliveryList,
          result,
          limit,
          index + 1,
          count + 1,
          memberDetails,
          (response, err) => {
            if (!err) {
              done(response, false);
            } else {
              done(null, err);
            }
          },
        );
      } else {
        done(result, false);
      }
    } else {
      done(result, false);
    }
  },
  async getNDRData(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const condition = {
        id: params.DeliveryRequestId,
      };
      const deliveryList = await DeliveryRequest.getNDRData(condition);
      done(deliveryList, false);
    } catch (e) {
      done(null, e);
    }
  },
  async getMemberData(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const condition = {
        UserId: inputData.user.id,
        ProjectId: params.ProjectId,
      };
      const memberData = await Member.getBy(condition);
      done(memberData, false);
    } catch (e) {
      done(null, e);
    }
  },
  async updateNDRStatus(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const updateData = inputData.body;
      const loginUser = inputData.user;
      const statusValue = await DeliveryRequest.findOne({
        where: { id: updateData.id },
        include: [
          {
            association: 'recurrence',
            required: false,
            attributes: [
              'id',
              'recurrence',
              'recurrenceStartDate',
              'recurrenceEndDate',
              'dateOfMonth',
              'monthlyRepeatType',
              'repeatEveryCount',
              'days',
              'requestType',
              'repeatEveryType',
              'chosenDateOfMonth',
              'createdBy',
            ],
          },
        ],
      });
      const NDRData = await DeliveryRequest.getNDRData({ id: updateData.id });
      if (!statusValue) {
        done(null, { message: 'Id does not exist.' });
      } else {
        const memberValue = await Member.findOne({
          where: Sequelize.and({
            UserId: inputData.user.id,
            ProjectId: statusValue.ProjectId,
            isDeleted: false,
          }),
        });
        if (memberValue) {
          if (
            memberValue.RoleId === 2 ||
            memberValue.RoleId === 3 ||
            memberValue.RoleId === 1 ||
            memberValue.RoleId === 4
          ) {
            if (memberValue.RoleId === 4) {
              if (+loginUser.id !== +NDRData.createdUserDetails.User.id) {
                return done(null, {
                  message: 'SC can able to deliver the NDR which was created by him only.',
                });
              }
            }
            const locationChosen = await Locations.findOne({
              where: {
                ProjectId: statusValue.ProjectId,
                id: statusValue.LocationId,
              },
            });
            const memberLocationPreference = await LocationNotificationPreferences.findAll({
              where: {
                ProjectId: statusValue.ProjectId,
                LocationId: statusValue.LocationId,
                follow: true,
              },
              include: [
                {
                  association: 'Member',
                  attributes: ['id', 'RoleId'],
                  where: {
                    [Op.and]: [
                      {
                        id: { [Op.ne]: memberValue.id },
                      },
                    ],
                  },
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'firstName', 'lastName', 'email'],
                    },
                  ],
                },
              ],
            });
            const locationFollowMembers = [];
            memberLocationPreference.forEach(async (element) => {
              locationFollowMembers.push(element.Member.id);
            });
            const bookingMemberDetails = [];
            NDRData.memberDetails.forEach(async (element) => {
              bookingMemberDetails.push(element.Member.id);
            });
            const history = {
              DeliveryRequestId: statusValue.id,
              MemberId: memberValue.id,
              DeliveryId: statusValue.DeliveryId,
            };
            if (updateData.status === 'Approved') {
              if (updateData.statuschange && updateData.statuschange === 'Reverted') {
                history.type = 'approved';
                history.description = `${loginUser.firstName} ${loginUser.lastName} Reverted the status from delivery to approved for Delivery Booking , ${statusValue.description}`;
                history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Reverted the status from delivery to approved for Delivery Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
              } else {
                history.type = 'approved';
                history.description = `${loginUser.firstName} ${loginUser.lastName} Approved the Delivery Booking`;
                history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Approved the Delivery Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
              }

              const notification = history;
              notification.ProjectId = statusValue.ProjectId;
              if (statusValue && statusValue.recurrence && statusValue.recurrence.recurrence) {
                notification.recurrenceType = `${statusValue.recurrence.recurrence} From ${moment(
                  statusValue.recurrence.recurrenceStartDate,
                ).format('MM/DD/YYYY')} to ${moment(
                  statusValue.recurrence.recurrenceEndDate,
                ).format('MM/DD/YYYY')}`;
              }
              notification.title = `Delivery Booking Approved by ${loginUser.firstName} ${loginUser.lastName}`;
              await DeliveryRequest.update(
                { status: updateData.status, approvedBy: memberValue.id, approved_at: new Date() },
                { where: { id: updateData.id } },
              );
              const object = {
                ProjectId: statusValue.ProjectId,
                MemberId: memberValue.id,
                DeliveryRequestId: statusValue.id,
                isDeleted: false,
                type: updateData.status.toLowerCase(),
                description: history.description,
              };
              await DeliverHistory.createInstance(object);
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = statusValue.ProjectId;
              const projectDetails = await Project.findByPk(statusValue.ProjectId);
              history.projectName = projectDetails.projectName;
              notification.requestType = 'deliveryRequest';
              const newNotification = await Notification.createInstance(notification);
              const personData = await DeliveryPerson.findAll({
                where: { DeliveryId: statusValue.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    { ProjectId: statusValue.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.in]: bookingMemberDetails } },
                    { id: { [Op.ne]: newNotification.MemberId } },
                    { id: { [Op.notIn]: locationFollowMembers } },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                  },
                ],
                attributes: ['id'],
              });
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                await pushNotification.sendMemberLocationPreferencePushNotification(
                  memberLocationPreference,
                  statusValue.DeliveryRequestId,
                  history.locationFollowDescription,
                  statusValue.requestType,
                  statusValue.ProjectId,
                  statusValue.id,
                  6,
                );
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  statusValue.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  6,
                );
              }
              history.memberData = personData;
              history.adminData = adminData;
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                personData,
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberValue,
                loginUser,
                6,
                'approved a',
                'Delivery Request',
                `delivery Booking (${statusValue.DeliveryId} - ${statusValue.description})`,
                statusValue.id,
              );
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: statusValue.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 6,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              await pushNotification.sendDeviceToken(history, 6, statusValue.ProjectId);
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                history.memberData.push(...memberLocationPreference);
              }
              return done(history, false);
            }
            if (updateData.status === 'Declined' || updateData.status === 'Delivered') {
              history.type = updateData.status.toLowerCase();
              history.description = `${loginUser.firstName} ${loginUser.lastName} ${updateData.status} the Delivery Booking`;
              history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} ${updateData.status} the Delivery Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
              const notification = history;
              notification.ProjectId = statusValue.ProjectId;
              if (statusValue && statusValue.recurrence && statusValue.recurrence.recurrence) {
                notification.recurrenceType = `${statusValue.recurrence.recurrence} From ${moment(
                  statusValue.recurrence.recurrenceStartDate,
                ).format('MM/DD/YYYY')} to ${moment(
                  statusValue.recurrence.recurrenceEndDate,
                ).format('MM/DD/YYYY')}`;
              }
              notification.title = `Delivery Booking ${updateData.status} by ${loginUser.firstName} ${loginUser.lastName}`;
              await DeliveryRequest.update(
                { status: updateData.status },
                { where: { id: updateData.id } },
              );
              const object1 = {
                ProjectId: statusValue.ProjectId,
                MemberId: memberValue.id,
                DeliveryRequestId: statusValue.id,
                isDeleted: false,
                type: updateData.status.toLowerCase(),
                description: history.description,
              };
              await DeliverHistory.createInstance(object1);
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = statusValue.ProjectId;
              const projectDetails = await Project.findByPk(statusValue.ProjectId);
              history.projectName = projectDetails.projectName;
              notification.requestType = 'deliveryRequest';
              const newNotification = await Notification.createInstance(notification);
              const personData = await DeliveryPerson.findAll({
                where: { DeliveryId: statusValue.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    { ProjectId: statusValue.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.in]: bookingMemberDetails } },
                    { id: { [Op.ne]: newNotification.MemberId } },
                    { id: { [Op.notIn]: locationFollowMembers } },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                  },
                ],
                attributes: ['id'],
              });
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                await pushNotification.sendMemberLocationPreferencePushNotification(
                  memberLocationPreference,
                  statusValue.DeliveryRequestId,
                  history.locationFollowDescription,
                  statusValue.requestType,
                  statusValue.ProjectId,
                  statusValue.id,
                  6,
                );
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  statusValue.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  6,
                );
              }
              history.memberData = personData;
              history.adminData = adminData;
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                history.memberData.push(...memberLocationPreference);
              }
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: statusValue.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 6,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                personData,
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberValue,
                loginUser,
                6,
                `${updateData.status.toLowerCase()}`,
                'Delivery Request',
                `delivery Booking (${statusValue.DeliveryId} - ${statusValue.description})`,
                statusValue.id,
              );
              await pushNotification.sendDeviceToken(history, 6, statusValue.ProjectId);
              if (updateData.status === 'Delivered') {
                const userEmails = await this.getMemberDetailData(
                  history,
                  memberLocationPreference,
                );
                if (userEmails.length > 0) {
                  userEmails.forEach(async (element) => {
                    let name;
                    if (!element.firstName) {
                      name = 'user';
                    } else {
                      name = `${element.firstName} ${element.lastName}`;
                    }
                    const time = moment(statusValue.deliveryStart).format('MM-DD-YYYY');
                    const mailPayload = {
                      userName: name,
                      email: element.email,
                      description: statusValue.description,
                      userName1: `${loginUser.firstName} ${loginUser.lastName}`,
                      deliveryID: statusValue.DeliveryId,
                      deliveryId: statusValue.DeliveryId,
                      status_timestamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
                      timestamp: time,
                    };
                    const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
                      where: {
                        MemberId: +element.MemberId,
                        ProjectId: +statusValue.ProjectId,
                        LocationId: +statusValue.LocationId,
                        isDeleted: false,
                        // follow: true,
                      },
                    });
                    if (isMemberFollowLocation) {
                      const memberNotification = await NotificationPreference.findOne({
                        where: {
                          MemberId: +element.MemberId,
                          ProjectId: +statusValue.ProjectId,
                          isDeleted: false,
                        },
                        include: [
                          {
                            association: 'NotificationPreferenceItem',
                            where: {
                              id: 10,
                              isDeleted: false,
                            },
                          },
                        ],
                      });
                      if (memberNotification && memberNotification.instant) {
                        await MAILER.sendMail(
                          mailPayload,
                          'deliveredDR',
                          'Delivery Booking status updated',
                          'Delivery Booking status updated',
                          async (info, err) => {
                            console.log(info, err);
                          },
                        );
                      }
                      if (memberNotification && memberNotification.dailyDigest) {
                        await this.createDailyDigestData(
                          +memberValue.RoleId,
                          +element.MemberId,
                          +statusValue.ProjectId,
                          +statusValue.ParentCompanyId,
                          loginUser,
                          'delivered a',
                          'Delivery Request',
                          `delivery Booking (${statusValue.DeliveryId} - ${statusValue.description})`,
                          statusValue.id,
                        );
                      }
                    }
                  });
                  return done(history, false);
                }
                return done(history, false);
              }
              return done(history, false);
            }
            return done(null, { message: 'Invalid Status' });
          }
          return done(null, {
            message: 'Only Project Admin and General Contracter allowed to update the status',
          });
        }
        return done(null, {
          message: 'Not a Valid Member for this Delivery Booking',
        });
      }
    } catch (e) {
      return done(null, e);
    }
  },
  async bulkdeliveryRequestUpload(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { file } = inputData;
      const ProjectId = +inputData.params.ProjectId;
      const memberDetail = await Member.findOne({
        where: [
          Sequelize.and(
            {
              UserId: inputData.user.id,
              ProjectId,
              isDeleted: false,
            },
            Sequelize.or({ RoleId: [1, 2, 3, 4] }),
          ),
        ],
      });
      if (memberDetail) {
        if (file && file.originalname) {
          const splitValue = file.originalname.split('.');
          const extension = splitValue[splitValue.length - 1];
          const fileName = splitValue[0];
          const firstSplitFileName = fileName.split('_');
          if (firstSplitFileName.length === 3) {
            const projectFileName = firstSplitFileName[0];
            const projectId = firstSplitFileName[1];
            const projectDetails = await Project.findByPk(ProjectId);
            if (
              projectDetails.projectName.toLowerCase() === projectFileName.toLowerCase() &&
              +ProjectId === +projectId
            ) {
              if (extension === 'xlsx') {
                const newWorkbook = new ExcelJS.Workbook();
                await newWorkbook.xlsx.readFile(file.path);
                const worksheet = newWorkbook.getWorksheet('Delivery Booking');
                this.createDeliveryRequestFile(worksheet, inputData, (resValue, error) => {
                  if (!error) {
                    return done(resValue, false);
                  }
                  return done(null, error);
                });
              } else {
                return done(null, { message: 'Please choose valid file' });
              }
            } else {
              return done(null, { message: 'Invalid file' });
            }
          } else {
            return done(null, { message: 'Invalid file' });
          }
        } else {
          return done(null, { message: 'Please select a file.' });
        }
      } else {
        return done(null, { message: 'Project Does not exist or you are not a valid member.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createDeliveryRequestFile(deliveryRequestWorksheet, inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const existProjectId = inputData.params.ProjectId;
      const ProjectId = +existProjectId;
      const loginUser = inputData.user;
      const projectDetails = await Project.findByPk(ProjectId);
      let fileFormat = true;
      const worksheet = deliveryRequestWorksheet;
      const ndrRecords = [];
      let headers;
      if (worksheet) {
        worksheet.eachRow(async (rowData, rowNumber) => {
          const singleRowData = rowData.values;
          singleRowData.shift();
          if (rowNumber === 2) {
            headers = rowData.values;
          } else if (singleRowData.length > 0 && rowNumber >= 2) {
            const getRow = rowData.values;
            const description = getRow[2];
            if (description) {
              ndrRecords.push(rowData.values);
            }
          }
        });
        if (ndrRecords !== undefined && ndrRecords.length === 0) {
          return done(null, {
            message: 'Please upload proper document / Please fill Mandatory columns',
          });
        }
        if (inputData.file) {
          if (+headers.length === 15) {
            fileFormat = false;
          }
        }
        if (fileFormat) {
          const worker = new Worker(bulkNdrProcess);
          const object = stringify({
            projectDetails,
            loginUser,
            ndrRecords,
            ProjectId,
            inputData,
          });
          worker.postMessage(object);
          worker.on('message', (data) => {
            if (data === 'success') {
              const socketObject = {
                message: data,
                loginUserId: loginUser.id,
              };
              global.io.emit('bulkNdrNotification', socketObject);
              worker.terminate();
            }
          });
          worker.on('exit', (data) => {
            console.log('worker thread exit ', data);
          });
          done({ message: 'success' }, false);
        } else {
          done(null, { message: 'Invalid File Format' });
        }
      } else {
        done(null, { message: 'Invalid File' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async deleteQueuedNdr(input, done) {
    try {
      await this.getDynamicModel(input);
      const reqData = input.body;
      const inputData = {
        isDeleted: true,
      };
      if (reqData.queuedDeliveryRequestSelectAll) {
        const deleteValue = await DeliveryRequest.update(inputData, {
          where: {
            ProjectId: reqData.ProjectId,
            isQueued: true,
          },
        });
        done(deleteValue, false);
      } else {
        const { id } = input.body;
        const deleteValue = await DeliveryRequest.update(inputData, {
          where: { id: { [Op.in]: id } },
        });
        done(deleteValue, false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async editMultipleDeliveryRequest(req) {
    try {
      const payload = req.body;
      if (payload.deliveryRequestIds && payload.deliveryRequestIds.length > 0) {
        await this.getDynamicModel(req);
        const loginUser = req.user;
        let history = {};
        let memberData = {};
        let addedPerson = [];
        for (let mainIndex = 1; mainIndex <= payload.deliveryRequestIds.length; mainIndex += 1) {
          const deliveryRequestId = payload.deliveryRequestIds[mainIndex - 1];
          if (deliveryRequestId) {
            let getDeliveryRequestDetail = {};
            getDeliveryRequestDetail = await DeliveryRequest.findOne({
              where: [
                Sequelize.and({
                  id: deliveryRequestId,
                }),
              ],
            });
            if (getDeliveryRequestDetail) {
              const condition = Sequelize.and({
                ProjectId: payload.ProjectId,
                DeliveryId: deliveryRequestId,
              });
              const updateParam = {
                DeliveryId: getDeliveryRequestDetail.id,
                DeliveryCode: getDeliveryRequestDetail.DeliveryId,
                ProjectId: getDeliveryRequestDetail.ProjectId,
                isDeleted: false,
              };
              memberData = await Member.getBy({
                UserId: loginUser.id,
                ProjectId: payload.ProjectId,
              });
              history = {
                DeliveryRequestId: deliveryRequestId,
                MemberId: memberData.id,
                type: 'edit',
                description: `${loginUser.firstName} ${loginUser.lastName} Edited this Delivery Booking.`,
              };
              if (payload.companies && payload.companies.length > 0) {
                const addedCompany = [];
                const existCompanies = await DeliverCompany.findAll({ where: condition });
                const deletedCompany = existCompanies.filter((e) => {
                  return payload.companies.indexOf(e.CompanyId) === -1 && e.isDeleted === false;
                });
                await DeliverCompany.update({ isDeleted: true }, { where: condition });
                payload.companies.forEach(async (element, companyIndexValue) => {
                  const index = existCompanies.findIndex((item) => item.CompanyId === element);
                  const companyParam = updateParam;
                  companyParam.CompanyId = element;
                  if (index !== -1) {
                    await DeliverCompany.update(companyParam, {
                      where: { id: existCompanies[index].id },
                    });
                    if (existCompanies[index].isDeleted !== false) {
                      addedCompany.push(existCompanies[index]);
                    }
                  } else {
                    const newCompanyData = await DeliverCompany.createInstance(companyParam);
                    addedCompany.push(newCompanyData);
                  }
                  if (companyIndexValue === payload.companies.length - 1) {
                    this.updateCompanyHistory(addedCompany, deletedCompany, history, loginUser);
                  }
                });
              }
              if (payload.persons && payload.persons.length > 0) {
                addedPerson = [];
                const existPerson = await DeliveryPerson.findAll({ where: condition });
                const deletedPerson = existPerson.filter((e) => {
                  return payload.persons.indexOf(e.MemberId) === -1 && e.isDeleted === false;
                });
                await DeliveryPerson.update({ isDeleted: true }, { where: condition });
                payload.persons.forEach(async (element, personIndexValue) => {
                  const index = existPerson.findIndex((item) => item.MemberId === element);
                  const memberParam = updateParam;
                  memberParam.MemberId = element;
                  if (index !== -1) {
                    await DeliveryPerson.update(memberParam, {
                      where: { id: existPerson[index].id },
                    });
                    if (existPerson[index].isDeleted !== false) {
                      addedPerson.push(existPerson[index]);
                    }
                  } else {
                    const newPersonData = await DeliveryPerson.createInstance(memberParam);
                    addedPerson.push(newPersonData);
                  }
                  if (personIndexValue === payload.persons.length - 1) {
                    this.updatePersonHistory(addedPerson, deletedPerson, history, loginUser);
                  }
                });
              }
              if (payload.define && payload.define.length > 0) {
                const addedDefineData = [];
                const existDefine = await DeliverDefine.findAll({ where: condition });
                const deletedDefine = existDefine.filter((e) => {
                  return (
                    payload.define.indexOf(e.DeliverDefineWorkId) === -1 && e.isDeleted === false
                  );
                });
                await DeliverDefine.update({ isDeleted: true }, { where: condition });
                payload.define.forEach(async (element, defineIndexValue) => {
                  const index = existDefine.findIndex(
                    (item) => item.DeliverDefineWorkId === element,
                  );
                  const defineParam = updateParam;
                  defineParam.DeliverDefineWorkId = element;
                  if (index !== -1) {
                    await DeliverDefine.update(defineParam, {
                      where: { id: existDefine[index].id },
                    });
                    if (existDefine[index].isDeleted !== false) {
                      addedDefineData.push(existDefine[index]);
                    }
                  } else {
                    const newDefineData = await DeliverDefine.createInstance(defineParam);
                    addedDefineData.push(newDefineData);
                  }
                  if (defineIndexValue === payload.define.length - 1) {
                    this.updateDefineHistory(addedDefineData, deletedDefine, history, loginUser);
                  }
                });
              }
              if (payload.escort) {
                await DeliveryRequest.update(
                  { escort: payload.escort, status: payload.status ? payload.status : 'Pending' },
                  {
                    where: { id: deliveryRequestId },
                  },
                );
              }
              if (payload.GateId) {
                const addedGate = [];
                const gates = [payload.GateId];
                const existGate = await DeliverGate.findAll({ where: condition });
                const deletedGate = existGate.filter((e) => {
                  return gates.indexOf(e.GateId) === -1 && e.isDeleted === false;
                });
                await DeliverGate.update({ isDeleted: true }, { where: condition });
                gates.forEach(async (element, gateIndexValue) => {
                  const index = existGate.findIndex((item) => item.GateId === element);
                  const gateParam = updateParam;
                  gateParam.GateId = element;
                  if (index !== -1) {
                    await DeliverGate.update(gateParam, { where: { id: existGate[index].id } });
                    if (existGate[index].isDeleted !== false) {
                      addedGate.push(existGate[index]);
                    }
                  } else {
                    const newGateData = await DeliverGate.createInstance(gateParam);
                    addedGate.push(newGateData);
                  }
                  if (gateIndexValue === gates.length - 1) {
                    this.updateGateHistory(addedGate, deletedGate, history, loginUser);
                  }
                });
              }
              if (payload.EquipmentId) {
                const addedEquipment = [];

                const equipments = [payload.EquipmentId];
                await DeliveryRequest.update(
                  { escort: payload.escort, status: payload.status ? payload.status : 'Pending' },
                  {
                    where: { id: deliveryRequestId },
                  },
                );
                const existEquipment = await DeliverEquipment.findAll({ where: condition });
                const deletedEquipment = existEquipment.filter((e) => {
                  return equipments.indexOf(e.EquipmentId) === -1 && e.isDeleted === false;
                });
                await DeliverEquipment.update({ isDeleted: true }, { where: condition });
                equipments.forEach(async (element, equipmentIndexValue) => {
                  const index = existEquipment.findIndex((item) => item.EquipmentId === element);
                  const equipmentParam = updateParam;
                  equipmentParam.EquipmentId = element;
                  if (index !== -1) {
                    await DeliverEquipment.update(equipmentParam, {
                      where: { id: existEquipment[index].id },
                    });
                    if (existEquipment[index].isDeleted !== false) {
                      addedEquipment.push(existEquipment[index]);
                    }
                  } else {
                    const newEquipmentData = await DeliverEquipment.createInstance(equipmentParam);
                    addedEquipment.push(newEquipmentData);
                  }
                  if (equipmentIndexValue === equipments.length - 1) {
                    this.updateEquipmentHistory(
                      addedEquipment,
                      deletedEquipment,
                      history,
                      loginUser,
                    );
                  }
                });
              }
              if (payload.deliveryStart && payload.deliveryEnd) {
                const startDate = new Date(payload.deliveryStart).getTime();
                const currentDate = new Date().getTime();
                const endDate = new Date(payload.deliveryEnd).getTime();
                if (startDate > currentDate && endDate > currentDate) {
                  const DeliverParam = {
                    status: payload.status,
                    deliveryStart: payload.deliveryStart,
                    deliveryEnd: payload.deliveryEnd,
                  };
                  await DeliveryRequest.update(DeliverParam, {
                    where: { id: deliveryRequestId },
                  });
                }
              }

              if (payload.void === true) {
                const existVoid = await VoidList.findOne({
                  where: Sequelize.and({
                    DeliveryRequestId: deliveryRequestId,
                  }),
                });
                if (!existVoid) {
                  await voidService.deliveryRequestVoidHistory(existVoid, memberData, loginUser);
                  const voidcreate = {
                    DeliveryRequestId: deliveryRequestId,
                    ProjectId: payload.ProjectId,
                    ParentCompanyId: payload.ParentCompanyId,
                  };
                  await VoidList.createInstance(voidcreate);
                }
              }
              if (payload.status) {
                if (memberData.RoleId === 2 || memberData.RoleId === 1) {
                  const DeliverParam = {};
                  DeliverParam.status = payload.status;
                  DeliverParam.approvedBy = memberData.id;
                  DeliverParam.approved_at = new Date();
                  await DeliveryRequest.update(DeliverParam, {
                    where: { id: deliveryRequestId },
                  });
                }
              }
            }
          }
          if (mainIndex === payload.deliveryRequestIds.length) {
            return { message: 'Success.!' };
          }
        }
      } else {
        return { message: 'Please select Delivery Booking to update.!' };
      }
    } catch (e) {
      return e;
    }
  },
  async updateEditDeliveryRequestHistory(
    userEditedDeliveryRequestData,
    existsDeliveryRequestData,
    updatedDeliveryRequest,
    history,
    loginUser,
  ) {
    const historyObject = history;
    if (
      (userEditedDeliveryRequestData.description &&
        userEditedDeliveryRequestData.description.toLowerCase()) !==
      (existsDeliveryRequestData.description && existsDeliveryRequestData.description.toLowerCase())
    ) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Description ${userEditedDeliveryRequestData.description}`;
      DeliverHistory.createInstance(historyObject);
    }
    if (
      userEditedDeliveryRequestData.LocationId !== existsDeliveryRequestData.LocationId &&
      updatedDeliveryRequest &&
      updatedDeliveryRequest.location &&
      updatedDeliveryRequest.location.locationPath
    ) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Location, ${updatedDeliveryRequest.location.locationPath} `;
      DeliverHistory.createInstance(historyObject);
    }
    if (
      new Date(userEditedDeliveryRequestData.deliveryStart).getTime() !==
      new Date(existsDeliveryRequestData.deliveryStart).getTime()
    ) {
      historyObject.description = `${loginUser.firstName} 
      ${loginUser.lastName} Updated the Delivery Start Date ${userEditedDeliveryRequestData.deliveryStart}`;
      DeliverHistory.createInstance(historyObject);
    }
    if (
      new Date(userEditedDeliveryRequestData.deliveryEnd).getTime() !==
      new Date(existsDeliveryRequestData.deliveryEnd).getTime()
    ) {
      historyObject.description = `${loginUser.firstName} 
      ${loginUser.lastName} Updated the Delivery End Date  ${userEditedDeliveryRequestData.deliveryEnd}`;
      DeliverHistory.createInstance(historyObject);
    }
    if (userEditedDeliveryRequestData.notes) {
      if (existsDeliveryRequestData.notes) {
        if (
          existsDeliveryRequestData.notes.toLowerCase() !==
          userEditedDeliveryRequestData.notes.toLowerCase()
        ) {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Notes ${existsDeliveryRequestData.notes}`;
          DeliverHistory.createInstance(historyObject);
        }
      } else {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Notes ${existsDeliveryRequestData.notes}`;
        DeliverHistory.createInstance(historyObject);
      }
    }
    if (!userEditedDeliveryRequestData.notes) {
      if (existsDeliveryRequestData.notes) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Removed the Notes ${existsDeliveryRequestData.notes}`;
        DeliverHistory.createInstance(historyObject);
      }
    }
    if (userEditedDeliveryRequestData.cranePickUpLocation) {
      if (
        userEditedDeliveryRequestData.cranePickUpLocation !==
        existsDeliveryRequestData.cranePickUpLocation
      ) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Picking From ${userEditedDeliveryRequestData.cranePickUpLocation}`;
        DeliverHistory.createInstance(historyObject);
      }
    }
    if (
      !userEditedDeliveryRequestData.cranePickUpLocation &&
      existsDeliveryRequestData.cranePickUpLocation
    ) {
      if (
        userEditedDeliveryRequestData.cranePickUpLocation !==
        existsDeliveryRequestData.cranePickUpLocation
      ) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Removed the Picking From ${existsDeliveryRequestData.cranePickUpLocation}`;
        DeliverHistory.createInstance(historyObject);
      }
    }
    if (userEditedDeliveryRequestData.craneDropOffLocation) {
      if (
        userEditedDeliveryRequestData.craneDropOffLocation !==
        existsDeliveryRequestData.craneDropOffLocation
      ) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Picking To ${userEditedDeliveryRequestData.craneDropOffLocation}`;
        DeliverHistory.createInstance(historyObject);
      }
    }
    if (
      !userEditedDeliveryRequestData.craneDropOffLocation &&
      existsDeliveryRequestData.craneDropOffLocation
    ) {
      if (
        userEditedDeliveryRequestData.craneDropOffLocation !==
        existsDeliveryRequestData.craneDropOffLocation
      ) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Removed the Picking To ${existsDeliveryRequestData.craneDropOffLocation}`;
        DeliverHistory.createInstance(historyObject);
      }
    }
    if (userEditedDeliveryRequestData.vehicleDetails) {
      if (existsDeliveryRequestData.vehicleDetails) {
        if (
          existsDeliveryRequestData.vehicleDetails !== userEditedDeliveryRequestData.vehicleDetails
        ) {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Vechicle Details ${existsDeliveryRequestData.vehicleDetails}`;
          DeliverHistory.createInstance(historyObject);
        }
      } else {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Vechicle Details ${existsDeliveryRequestData.vehicleDetails}`;
        DeliverHistory.createInstance(historyObject);
      }
    }
    if (!userEditedDeliveryRequestData.vehicleDetails) {
      if (existsDeliveryRequestData.vehicleDetails) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Removed the Vechicle Details ${existsDeliveryRequestData.vehicleDetails}`;
        DeliverHistory.createInstance(historyObject);
      }
    }
    if (userEditedDeliveryRequestData.escort !== existsDeliveryRequestData.escort) {
      if (userEditedDeliveryRequestData.escort === true) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} enabled the Escort`;
      } else {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} disabled the Escort`;
      }
      DeliverHistory.createInstance(historyObject);
    }
    if (
      updatedDeliveryRequest.memberDetails.length > 0 &&
      existsDeliveryRequestData.memberDetails.length > 0
    ) {
      const addedMember = updatedDeliveryRequest.memberDetails.filter((el) => {
        return !existsDeliveryRequestData.memberDetails.find((element) => {
          return element.id === el.id;
        });
      });
      const deletedMember = existsDeliveryRequestData.memberDetails.filter((el) => {
        return !updatedDeliveryRequest.memberDetails.find((element) => {
          return element.id === el.id;
        });
      });
      if (addedMember.length > 0) {
        addedMember.forEach(async (element) => {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the member ${element.Member.User.firstName} ${element.Member.User.lastName}`;
          await DeliverHistory.createInstance(historyObject);
        });
      }
      if (deletedMember.length > 0) {
        deletedMember.forEach(async (element) => {
          if (element.Member.User.firstName) {
            historyObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the member ${element.Member.User.firstName} ${element.Member.User.lastName}`;
          } else {
            historyObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the member`;
          }
          await DeliverHistory.createInstance(historyObject);
        });
      }
    }
    if (
      updatedDeliveryRequest.companyDetails.length > 0 &&
      existsDeliveryRequestData.companyDetails.length > 0
    ) {
      const addedCompany = updatedDeliveryRequest.companyDetails.filter((el) => {
        return !existsDeliveryRequestData.companyDetails.find((element) => {
          return element.id === el.id;
        });
      });
      const deletedCompany = existsDeliveryRequestData.companyDetails.filter((el) => {
        return !updatedDeliveryRequest.companyDetails.find((element) => {
          return element.id === el.id;
        });
      });
      if (addedCompany.length > 0) {
        addedCompany.forEach(async (element) => {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the company ${element.Company.companyName}`;
          await DeliverHistory.createInstance(historyObject);
        });
      }
      if (deletedCompany.length > 0) {
        deletedCompany.forEach(async (element) => {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the company ${element.Company.companyName}`;
          await DeliverHistory.createInstance(historyObject);
        });
      }
    }
    if (
      updatedDeliveryRequest.defineWorkDetails.length > 0 &&
      existsDeliveryRequestData.defineWorkDetails.length > 0
    ) {
      const addedDfow = updatedDeliveryRequest.defineWorkDetails.filter((el) => {
        return !existsDeliveryRequestData.defineWorkDetails.find((element) => {
          return element.id === el.id;
        });
      });
      const deletedDfow = existsDeliveryRequestData.defineWorkDetails.filter((el) => {
        return !updatedDeliveryRequest.defineWorkDetails.find((element) => {
          return element.id === el.id;
        });
      });
      if (addedDfow.length > 0) {
        addedDfow.forEach(async (element) => {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the Definable feature of work ${element.DeliverDefineWork.DFOW}`;
          await DeliverHistory.createInstance(historyObject);
        });
      }
      if (deletedDfow.length > 0) {
        deletedDfow.forEach(async (element) => {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Definable feature of work ${element.DeliverDefineWork.DFOW}`;
          await DeliverHistory.createInstance(historyObject);
        });
      }
    }
    if (
      updatedDeliveryRequest.equipmentDetails.length > 0 &&
      existsDeliveryRequestData.equipmentDetails.length > 0
    ) {
      const addedEquipment = updatedDeliveryRequest.equipmentDetails.filter((el) => {
        return !existsDeliveryRequestData.equipmentDetails.find((element) => {
          return element.Equipment.id === el.Equipment.id;
        });
      });
      const deletedEquipment = existsDeliveryRequestData.equipmentDetails.filter((el) => {
        return !updatedDeliveryRequest.equipmentDetails.find((element) => {
          return element.Equipment.id === el.Equipment.id;
        });
      });
      if (addedEquipment.length > 0) {
        addedEquipment.forEach(async (element) => {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the Equipment ${element.Equipment.equipmentName}`;
          await DeliverHistory.createInstance(historyObject);
        });
      }
      if (deletedEquipment.length > 0) {
        deletedEquipment.forEach(async (element) => {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Equipment ${element.Equipment.equipmentName}`;
          await DeliverHistory.createInstance(historyObject);
        });
      }
    }
    if (
      updatedDeliveryRequest.gateDetails.length > 0 &&
      existsDeliveryRequestData.gateDetails.length > 0
    ) {
      const addedGate = updatedDeliveryRequest.gateDetails.filter((el) => {
        return !existsDeliveryRequestData.gateDetails.find((element) => {
          return element.Gate.id === el.Gate.id;
        });
      });
      const deletedGate = existsDeliveryRequestData.gateDetails.filter((el) => {
        return !updatedDeliveryRequest.gateDetails.find((element) => {
          return element.Gate.id === el.Gate.id;
        });
      });
      if (addedGate.length > 0) {
        addedGate.forEach(async (element) => {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the Gate ${element.Gate.gateName}`;
          await DeliverHistory.createInstance(historyObject);
        });
      }
      if (deletedGate.length > 0) {
        deletedGate.forEach(async (element) => {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Gate ${element.Gate.gateName}`;
          await DeliverHistory.createInstance(historyObject);
        });
      }
    }
  },
  async getMemberDetailData(data, memberLocationPreference) {
    const emailArray = [];
    const existAdminData = [];
    if (data.memberData !== undefined) {
      data.memberData.forEach((element) => {
        const index = existAdminData.findIndex(
          (adminNew) => adminNew.email === element.Member.User.email,
        );
        if (index === -1) {
          existAdminData.push({ email: element.Member.User.email });
          emailArray.push({
            email: element.Member.User.email,
            firstName: element.Member.User.firstName,
            lastName: element.Member.User.lastName,
            UserId: element.Member.User.id,
            MemberId: element.Member.id,
            RoleId: element.Member.RoleId,
          });
        }
      });
    }
    if (data.adminData !== undefined) {
      data.adminData.forEach((element) => {
        const index = existAdminData.findIndex((adminNew) => adminNew.email === element.User.email);
        if (index === -1) {
          existAdminData.push({ email: element.User.email });
          emailArray.push({
            email: element.User.email,
            firstName: element.User.firstName,
            lastName: element.User.lastName,
            UserId: element.User.id,
            MemberId: element.id,
            RoleId: element.RoleId,
          });
        }
      });
    }
    if (memberLocationPreference !== undefined && memberLocationPreference.length > 0) {
      memberLocationPreference.forEach((element) => {
        const index = existAdminData.findIndex(
          (adminNew) => adminNew.email === element.Member.User.email,
        );
        if (index === -1) {
          existAdminData.push({ email: element.Member.User.email });
          emailArray.push({
            email: element.Member.User.email,
            firstName: element.Member.User.firstName,
            lastName: element.Member.User.lastName,
            UserId: element.Member.User.id,
            MemberId: element.Member.id,
            RoleId: element.Member.RoleId,
          });
        }
      });
    }
    return emailArray;
  },
  // prettier-ignore
  async createDailyDigestData(
    RoleId,
    MemberId,
    ProjectId,
    ParentCompanyId,
    loginUser,
    dailyDigestMessage,
    requestType,
    messages,
    requestId,
  ) {
    const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
    const encryptedRequestId = cryptr.encrypt(requestId);
    const encryptedMemberId = cryptr.encrypt(MemberId);
    let imageUrl;
    let link;
    let height;
    if (requestType === 'Delivery Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/87758081-8ccd-4e4d-a4eb-6257466876da.png';
      link = 'delivery-request';
      height = 'height:18px;';
    }
    if (requestType === 'Crane Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/ab400721-520c-4f6f-941c-ac07acc28809.png';
      link = 'crane-request';
      height = 'height:32px;';
    }
    if (requestType === 'Concrete Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/c43e4c58-4c4d-44ff-b78c-fe9948ea53eb.png';
      link = 'concrete-request';
      height = 'height:18px;';
    }
    const object = {
      description: `<div>
	<ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
		<li style="display:flex;">
			<img src="${imageUrl}" alt="message-icon" style="${height}">
				<p style="margin:0px;font-size:12px;padding-left:10px;">
					<a href="#" ta
        rget="" style="text-decoration: none;color:#4470FF;">
			${loginUser.firstName}  ${loginUser.lastName}
					</a>
					${dailyDigestMessage}
      <a href = "${process.env.BASE_URL}/${link}?requestId=${encryptedRequestId}&memberId=${encryptedMemberId} " style="text - decoration: none; color:#4470FF; " >${messages}</a>
  <span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment()
          .utc()
          .format('hh:mm A zz')}</span>
				</p>
		</li>
	</ul>
</div> `,
      MemberId,
      ProjectId,
      isSent: false,
      isDeleted: false,
      ParentCompanyId,
    };
    await DigestNotification.create(object);
  },
  async sendEmailNotificationToUser(
    history,
    memberDetails,
    loginUser,
    newDeliverData,
    deliveryData,
    memberLocationPreference,
  ) {
    const userEmails = await this.getMemberDetailData(history, memberLocationPreference);
    if (userEmails.length > 0) {
      userEmails.forEach(async (element) => {
        let name;
        if (!element.firstName) {
          name = 'user';
        } else {
          name = `${element.firstName} ${element.lastName} `;
        }
        if (+element.MemberId !== +memberDetails.id) {
          const memberRole = await Role.findOne({
            where: {
              id: memberDetails.RoleId,
              isDeleted: false,
            },
          });
          const time = moment(newDeliverData.deliveryStart).format('MM-DD-YYYY');
          const mailPayload = {
            userName: name,
            email: element.email,
            deliveryId: newDeliverData.DeliveryId,
            description: newDeliverData.description,
            timestamp: time,
            createdTimestamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
            content: ` ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has created the delivery booking ${newDeliverData.DeliveryId}.Please see below for more details`,
          };
          const memberNotification = await NotificationPreference.findOne({
            where: {
              MemberId: +element.MemberId,
              ProjectId: +deliveryData.ProjectId,
              isDeleted: false,
            },
            include: [
              {
                association: 'NotificationPreferenceItem',
                where: {
                  id: 12,
                  isDeleted: false,
                },
              },
            ],
          });
          if (memberNotification && memberNotification.instant) {
            await MAILER.sendMail(
              mailPayload,
              'deliveryRequestCreated',
              `Delivery Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} `,
              `Delivery Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} `,
              async (info, err) => {
                console.log(info, err);
              },
            );
          }
          if (memberNotification && memberNotification.dailyDigest) {
            await this.createDailyDigestData(
              +memberDetails.RoleId,
              +element.MemberId,
              +deliveryData.ProjectId,
              +deliveryData.ParentCompanyId,
              loginUser,
              'created a',
              'Delivery Request',
              `delivery Booking(${newDeliverData.DeliveryId} - ${newDeliverData.description})`,
              newDeliverData.id,
            );
          }
        }
      });
    }
    return true;
  },
  async createDailyDigestDataApproval(
    RoleId,
    MemberId,
    ProjectId,
    ParentCompanyId,
    loginUser,
    dailyDigestMessage,
    requestType,
    messages,
    messages2,
    requestId,
  ) {
    const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
    const encryptedRequestId = cryptr.encrypt(requestId);
    const encryptedMemberId = cryptr.encrypt(MemberId);
    let imageUrl;
    let link;
    let height;
    if (requestType === 'Delivery Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/87758081-8ccd-4e4d-a4eb-6257466876da.png';
      link = 'delivery-request';
      height = 'height:18px;';
    }
    if (requestType === 'Crane Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/ab400721-520c-4f6f-941c-ac07acc28809.png';
      link = 'crane-request';
      height = 'height:32px;';
    }
    if (requestType === 'Concrete Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/c43e4c58-4c4d-44ff-b78c-fe9948ea53eb.png';
      link = 'concrete-request';
      height = 'height:18px;';
    }
    const object = {
      description: `<div>
  <ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
    <li style="display:flex;">
      <img src="${imageUrl}" alt="message-icon" style="${height}">
        <p style="margin:0px;font-size:12px;padding-left:10px;">
          <a href="
        #" target="" style="text-decoration: none;color:#4470FF;">
          ${loginUser.firstName}  ${loginUser.lastName}
          </a>
          ${dailyDigestMessage}
      <a href = "${process.env.BASE_URL
        }/ ${link}?requestId = ${encryptedRequestId}& memberId=${encryptedMemberId} " style="text - decoration: none; color:#4470FF; " >
          ${messages}
        </a>
  ${messages2}
<span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment()
          .utc()
          .format('hh:mm A zz')}</span>
        </p>
    </li>
  </ul>
</div> `,
      MemberId,
      ProjectId,
      isSent: false,
      isDeleted: false,
      ParentCompanyId,
    };
    await DigestNotification.create(object);
  },
  async convertTimezoneToUtc(date, timezone, time) {
    const chosenTimezoneDeliveryStart = moment.tz(`${date} ${time}`, 'MM/DD/YYYY HH:mm', timezone);
    const utcDate = chosenTimezoneDeliveryStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
    return utcDate;
  },
  async createCopyofDeliveryRequest(dataInSeries, payload, dates, loginUser, newRecurrenceId) {
    const eventsArray = [];
    const memberDetails = await Member.getBy({
      UserId: loginUser.id,
      ProjectId: payload.ProjectId,
      isActive: true,
      isDeleted: false,
    });
    const projectDetails = await Project.getProjectAndSettings({
      isDeleted: false,
      id: +payload.ProjectId,
    });
    if (dataInSeries && dataInSeries.recurrence && dataInSeries.recurrence.recurrence) {
      let startDate;
      let endDate;
      if (payload.recurrence && dates && dates.length > 0) {
        startDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          dates[0],
          payload.deliveryStartTime,
          payload.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
        endDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          dates[0],
          payload.deliveryEndTime,
          payload.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
      }
      if (startDate || endDate) {
        throw new Error(
          `Bookings can not be submitted within ${projectDetails.ProjectSettings.deliveryWindowTime} ${projectDetails.ProjectSettings.deliveryWindowTimeUnit} prior to the event`,
        );
      }
      let DeliverParam = {};
      const lastIdValue = await DeliveryRequest.findOne({
        where: { ProjectId: memberDetails.ProjectId, isDeleted: false },
        order: [['DeliveryId', 'DESC']],
      });
      let id = 0;
      const newValue = JSON.parse(JSON.stringify(lastIdValue));
      if (newValue && newValue.DeliveryId !== null && newValue.DeliveryId !== undefined) {
        id = newValue.DeliveryId;
      }
      let lastData = {};
      lastData = await CraneRequest.findOne({
        where: { ProjectId: +memberDetails.ProjectId, isDeleted: false },
        order: [['CraneRequestId', 'DESC']],
      });
      const deliveryRequestList = await DeliveryRequest.findOne({
        where: {
          ProjectId: +memberDetails.ProjectId,
          isDeleted: false,
          isAssociatedWithCraneRequest: true,
        },
        order: [['CraneRequestId', 'DESC']],
      });
      if (deliveryRequestList) {
        if (lastData) {
          if (deliveryRequestList.CraneRequestId > lastData.CraneRequestId) {
            lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
          }
        } else {
          lastData = {};
          lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
        }
      }
      if (lastData) {
        const data = lastData.CraneRequestId;
        lastData.CraneRequestId = 0;
        lastData.CraneRequestId = data + 1;
      } else {
        lastData = {};
        lastData.CraneRequestId = 1;
      }
      let craneId = 0;
      const newId = JSON.parse(JSON.stringify(lastData));
      if (newId && newId.CraneRequestId !== null && newId.CraneRequestId !== undefined) {
        craneId = newId.CraneRequestId;
      }
      const roleDetails = await Role.getBy('Project Admin');
      const accountRoleDetails = await Role.getBy('Account Admin');
      if (dataInSeries.recurrence.recurrence === 'Daily') {
        let dailyIndex = 0;
        while (dailyIndex < dates.length) {
          const data = dates[dailyIndex];
          if (
            moment(data).isBetween(moment(dates[0]), moment(dates[dates.length - 1]), null, '[]') ||
            moment(data).isSame(dates[0]) ||
            moment(data).isSame(dates[dates.length - 1])
          ) {
            id += 1;
            craneId += 1;
            DeliverParam = {
              description: payload.description,
              escort: payload.escort,
              vehicleDetails: payload.vehicleDetails,
              notes: payload.notes,
              DeliveryId: id,
              deliveryStart: await this.convertTimezoneToUtc(
                data,
                payload.timezone,
                payload.deliveryStartTime,
              ),
              deliveryEnd: await this.convertTimezoneToUtc(
                data,
                payload.timezone,
                payload.deliveryEndTime,
              ),
              ProjectId: payload.ProjectId,
              createdBy: memberDetails.id,
              isAssociatedWithCraneRequest: payload.isAssociatedWithCraneRequest,
              requestType: payload.requestType,
              cranePickUpLocation: payload.cranePickUpLocation,
              craneDropOffLocation: payload.craneDropOffLocation,
              recurrenceId: newRecurrenceId,
            };
            if (payload.requestType === 'deliveryRequestWithCrane') {
              DeliverParam.CraneRequestId = craneId;
            }
            if (
              memberDetails.RoleId === roleDetails.id ||
              memberDetails.RoleId === accountRoleDetails.id ||
              memberDetails.isAutoApproveEnabled ||
              projectDetails.ProjectSettings.isAutoApprovalEnabled
            ) {
              DeliverParam.status = 'Approved';
              DeliverParam.approvedBy = memberDetails.id;
              DeliverParam.approved_at = new Date();
            }
            eventsArray.push(DeliverParam);
            // eslint-disable-next-line no-const-assign
            dailyIndex += +dataInSeries.recurrence.repeatEveryCount;
          }
        }
      }
      if (dataInSeries.recurrence.recurrence === 'Weekly') {
        const startDayWeek = moment(dates[0]).startOf('week');
        const endDayWeek = moment(dates[dates.length - 1]).endOf('week');
        const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
        const totalDaysOfRecurrence = Array.from(range1.by('day'));
        let count;
        let weekIncrement;
        if (+dataInSeries.recurrence.repeatEveryCount > 1) {
          count = +dataInSeries.recurrence.repeatEveryCount - 1;
          weekIncrement = 7;
        } else {
          count = 1;
          weekIncrement = 0;
        }
        for (
          let indexba = 0;
          indexba < totalDaysOfRecurrence.length;
          indexba += weekIncrement * count
        ) {
          const totalLength = indexba + 6;
          for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
            const data = totalDaysOfRecurrence[indexb];
            indexba += 1;
            if (
              data &&
              !moment(data).isBefore(dates[0]) &&
              !moment(data).isAfter(dates[dates.length - 1])
            ) {
              const day = moment(data).format('dddd');
              const indexVal = dataInSeries.recurrence.days.includes(day);
              if (indexVal) {
                id += 1;
                craneId += 1;
                const date = moment(`${data}`).format('MM/DD/YYYY');
                DeliverParam = {
                  description: payload.description,
                  escort: payload.escort,
                  vehicleDetails: payload.vehicleDetails,
                  notes: payload.notes,
                  DeliveryId: id,
                  deliveryStart: await this.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryStartTime,
                  ),
                  deliveryEnd: await this.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryEndTime,
                  ),
                  ProjectId: payload.ProjectId,
                  createdBy: memberDetails.id,
                  isAssociatedWithCraneRequest: payload.isAssociatedWithCraneRequest,
                  requestType: payload.requestType,
                  cranePickUpLocation: payload.cranePickUpLocation,
                  craneDropOffLocation: payload.craneDropOffLocation,
                  recurrenceId: newRecurrenceId,
                };
                if (payload.requestType === 'deliveryRequestWithCrane') {
                  DeliverParam.CraneRequestId = craneId;
                }
                if (
                  memberDetails.RoleId === roleDetails.id ||
                  memberDetails.RoleId === accountRoleDetails.id ||
                  memberDetails.isAutoApproveEnabled ||
                  projectDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  DeliverParam.status = 'Approved';
                  DeliverParam.approvedBy = memberDetails.id;
                  DeliverParam.approved_at = new Date();
                }
                eventsArray.push(DeliverParam);
              }
            }
          }
        }
      }
      if (dataInSeries.recurrence.recurrence === 'Monthly') {
        const startMonth = moment(dates[0]).startOf('month');
        const startMonthNumber = moment(startMonth).format('MM');
        const endMonth = moment(dates[dates.length - 1]).endOf('month');
        const endMonthNumber = moment(endMonth).format('MM');
        let startDate1 = moment(dates[0]);
        const endDate1 = moment(dates[dates.length - 1]).endOf('month');
        const allMonthsInPeriod = [];
        while (startDate1.isBefore(endDate1)) {
          allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
          startDate1 = startDate1.add(1, 'month');
        }
        let currentMonthDates = [];
        let totalNumberOfMonths = endMonthNumber - startMonthNumber;
        if (totalNumberOfMonths < 0) {
          totalNumberOfMonths *= -1;
        }
        let k = 0;
        while (k < allMonthsInPeriod.length + 1) {
          currentMonthDates = Array.from(
            { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
            (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
          );

          if (dataInSeries.recurrence.chosenDateOfMonth) {
            const getDate = currentMonthDates.filter(
              (value) => moment(value).format('DD') === dataInSeries.recurrence.dateOfMonth,
            );
            if (getDate.length === 1) {
              if (
                moment(getDate[0]).isBetween(
                  moment(dates[0]),
                  moment(dates[dates.length - 1]),
                  null,
                  '[]',
                ) ||
                moment(getDate[0]).isSame(dates[0]) ||
                moment(getDate[0]).isSame(dates[dates.length - 1])
              ) {
                id += 1;
                craneId += 1;
                const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                DeliverParam = {
                  description: payload.description,
                  escort: payload.escort,
                  vehicleDetails: payload.vehicleDetails,
                  notes: payload.notes,
                  DeliveryId: id,
                  deliveryStart: await this.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryStartTime,
                  ),
                  deliveryEnd: await this.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryEndTime,
                  ),
                  ProjectId: payload.ProjectId,
                  createdBy: memberDetails.id,
                  isAssociatedWithCraneRequest: payload.isAssociatedWithCraneRequest,
                  requestType: payload.requestType,
                  cranePickUpLocation: payload.cranePickUpLocation,
                  craneDropOffLocation: payload.craneDropOffLocation,
                  recurrenceId: newRecurrenceId,
                };
                if (payload.requestType === 'deliveryRequestWithCrane') {
                  DeliverParam.CraneRequestId = craneId;
                }
                if (
                  memberDetails.RoleId === roleDetails.id ||
                  memberDetails.RoleId === accountRoleDetails.id ||
                  memberDetails.isAutoApproveEnabled ||
                  projectDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  DeliverParam.status = 'Approved';
                  DeliverParam.approvedBy = memberDetails.id;
                  DeliverParam.approved_at = new Date();
                }
                eventsArray.push(DeliverParam);
              }
            }
          } else if (allMonthsInPeriod[k]) {
            const dayOfMonth = dataInSeries.recurrence.monthlyRepeatType;
            const week = dayOfMonth.split(' ')[0].toLowerCase();
            const day = dayOfMonth.split(' ')[1].toLowerCase();
            const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').day(day);
            const getAllDays = [];
            if (chosenDay.date() > 7) chosenDay.add(7, 'd');
            const month = chosenDay.month();
            while (month === chosenDay.month()) {
              getAllDays.push(chosenDay.toString());
              chosenDay.add(7, 'd');
            }
            let i = 0;
            if (week === 'second') {
              i += 1;
            } else if (week === 'third') {
              i += 2;
            } else if (week === 'fourth') {
              i += 3;
            } else if (week === 'last') {
              i = getAllDays.length - 1;
            }
            const finalDay = getAllDays[i];
            if (
              moment(finalDay).isBetween(
                moment(dates[0]),
                moment(dates[dates.length - 1]),
                null,
                '[]',
              ) ||
              moment(finalDay).isSame(dates[0]) ||
              moment(finalDay).isSame(dates[dates.length - 1])
            ) {
              id += 1;
              craneId += 1;
              const date = moment(finalDay).format('MM/DD/YYYY');
              DeliverParam = {
                description: payload.description,
                escort: payload.escort,
                vehicleDetails: payload.vehicleDetails,
                notes: payload.notes,
                DeliveryId: id,
                deliveryStart: await this.convertTimezoneToUtc(
                  date,
                  payload.timezone,
                  payload.deliveryStartTime,
                ),
                deliveryEnd: await this.convertTimezoneToUtc(
                  date,
                  payload.timezone,
                  payload.deliveryEndTime,
                ),
                ProjectId: payload.ProjectId,
                createdBy: memberDetails.id,
                isAssociatedWithCraneRequest: payload.isAssociatedWithCraneRequest,
                requestType: payload.requestType,
                cranePickUpLocation: payload.cranePickUpLocation,
                craneDropOffLocation: payload.craneDropOffLocation,
                recurrenceId: newRecurrenceId,
              };
              if (payload.requestType === 'deliveryRequestWithCrane') {
                DeliverParam.CraneRequestId = craneId;
              }
              if (
                memberDetails.RoleId === roleDetails.id ||
                memberDetails.RoleId === accountRoleDetails.id ||
                memberDetails.isAutoApproveEnabled ||
                projectDetails.ProjectSettings.isAutoApprovalEnabled
              ) {
                DeliverParam.status = 'Approved';
                DeliverParam.approvedBy = memberDetails.id;
                DeliverParam.approved_at = new Date();
              }
              eventsArray.push(DeliverParam);
            }
          }
          k += +dataInSeries.recurrence.repeatEveryCount;
        }
      }
      if (dataInSeries.recurrence.recurrence === 'Yearly') {
        const startMonth = moment(dates[0]).startOf('month');
        const startMonthNumber = moment(startMonth).format('MM');
        const endMonth = moment(dates[dates.length - 1]).endOf('month');
        const endMonthNumber = moment(endMonth).format('MM');
        let startDate1 = moment(dates[0]);
        const endDate1 = moment(dates[dates.length - 1]).endOf('month');
        const allMonthsInPeriod = [];
        while (startDate1.isBefore(endDate1)) {
          allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
          startDate1 = startDate1.add(12, 'month');
        }
        let currentMonthDates = [];
        let totalNumberOfMonths = endMonthNumber - startMonthNumber;
        if (totalNumberOfMonths < 0) {
          totalNumberOfMonths *= -1;
        }
        for (let k = 0; k < allMonthsInPeriod.length + 1; k += 1) {
          currentMonthDates = Array.from(
            { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
            (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
          );
          if (dataInSeries.recurrence.chosenDateOfMonth) {
            const getDate = currentMonthDates.filter(
              (value) => moment(value).format('DD') === dataInSeries.recurrence.dateOfMonth,
            );
            if (getDate.length === 1) {
              if (
                moment(getDate[0]).isBetween(
                  moment(dates[0]),
                  moment(dates[dates.length - 1]),
                  null,
                  '[]',
                ) ||
                moment(getDate[0]).isSame(dates[0]) ||
                moment(getDate[0]).isSame(dates[dates.length - 1])
              ) {
                id += 1;
                craneId += 1;
                const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                DeliverParam = {
                  description: payload.description,
                  escort: payload.escort,
                  vehicleDetails: payload.vehicleDetails,
                  notes: payload.notes,
                  DeliveryId: id,
                  deliveryStart: await this.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryStartTime,
                  ),
                  deliveryEnd: await this.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryEndTime,
                  ),
                  ProjectId: payload.ProjectId,
                  createdBy: memberDetails.id,
                  isAssociatedWithCraneRequest: payload.isAssociatedWithCraneRequest,
                  requestType: payload.requestType,
                  cranePickUpLocation: payload.cranePickUpLocation,
                  craneDropOffLocation: payload.craneDropOffLocation,
                  recurrenceId: newRecurrenceId,
                };
                if (payload.requestType === 'deliveryRequestWithCrane') {
                  DeliverParam.CraneRequestId = craneId;
                }
                if (
                  memberDetails.RoleId === roleDetails.id ||
                  memberDetails.RoleId === accountRoleDetails.id ||
                  memberDetails.isAutoApproveEnabled ||
                  projectDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  DeliverParam.status = 'Approved';
                  DeliverParam.approvedBy = memberDetails.id;
                  DeliverParam.approved_at = new Date();
                }
                eventsArray.push(DeliverParam);
              }
            }
          } else if (allMonthsInPeriod[k]) {
            const dayOfMonth = dataInSeries.recurrence.monthlyRepeatType;
            const week = dayOfMonth.split(' ')[0].toLowerCase();
            const day = dayOfMonth.split(' ')[1].toLowerCase();
            const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').day(day);
            const getAllDays = [];
            if (chosenDay.date() > 7) chosenDay.add(7, 'd');
            const month = chosenDay.month();
            while (month === chosenDay.month()) {
              getAllDays.push(chosenDay.toString());
              chosenDay.add(7, 'd');
            }
            let i = 0;
            if (week === 'second') {
              i += 1;
            } else if (week === 'third') {
              i += 2;
            } else if (week === 'fourth') {
              i += 3;
            } else if (week === 'last') {
              i = getAllDays.length - 1;
            }
            const finalDay = getAllDays[i];
            if (
              moment(finalDay).isBetween(
                moment(dates[0]),
                moment(dates[dates.length - 1]),
                null,
                '[]',
              ) ||
              moment(finalDay).isSame(dates[0]) ||
              moment(finalDay).isSame(dates[dates.length - 1])
            ) {
              id += 1;
              craneId += 1;
              const date = moment(finalDay).format('MM/DD/YYYY');
              DeliverParam = {
                description: payload.description,
                escort: payload.escort,
                vehicleDetails: payload.vehicleDetails,
                notes: payload.notes,
                DeliveryId: id,
                deliveryStart: await this.convertTimezoneToUtc(
                  date,
                  payload.timezone,
                  payload.deliveryStartTime,
                ),
                deliveryEnd: await this.convertTimezoneToUtc(
                  date,
                  payload.timezone,
                  payload.deliveryEndTime,
                ),
                ProjectId: payload.ProjectId,
                createdBy: memberDetails.id,
                isAssociatedWithCraneRequest: payload.isAssociatedWithCraneRequest,
                requestType: payload.requestType,
                cranePickUpLocation: payload.cranePickUpLocation,
                craneDropOffLocation: payload.craneDropOffLocation,
                recurrenceId: newRecurrenceId,
              };
              if (payload.requestType === 'deliveryRequestWithCrane') {
                DeliverParam.CraneRequestId = craneId;
              }
              if (
                memberDetails.RoleId === roleDetails.id ||
                memberDetails.RoleId === accountRoleDetails.id ||
                memberDetails.isAutoApproveEnabled ||
                projectDetails.ProjectSettings.isAutoApprovalEnabled
              ) {
                DeliverParam.status = 'Approved';
                DeliverParam.approvedBy = memberDetails.id;
                DeliverParam.approved_at = new Date();
              }
              eventsArray.push(DeliverParam);
            }
          }
        }
      }
    }

    if (eventsArray.length > 0) {
      for (let i = 0; i < eventsArray.length; i += 1) {
        const newDeliverData = await DeliveryRequest.createInstance(eventsArray[i]);
        const { companies, persons, define } = payload;
        const gates = [payload.GateId];
        const equipments = [payload.EquipmentId];
        const updateParam = {
          DeliveryId: newDeliverData.id,
          DeliveryCode: newDeliverData.DeliveryId,
          ProjectId: payload.ProjectId,
        };
        companies.forEach(async (element) => {
          const companyParam = updateParam;
          companyParam.CompanyId = element;
          await DeliverCompany.createInstance(companyParam);
        });
        gates.forEach(async (element) => {
          const gateParam = updateParam;
          gateParam.GateId = element;
          await DeliverGate.createInstance(gateParam);
        });
        equipments.forEach(async (element) => {
          const equipmentParam = updateParam;
          equipmentParam.EquipmentId = element;
          await DeliverEquipment.createInstance(equipmentParam);
        });
        persons.forEach(async (element) => {
          const memberParam = updateParam;
          memberParam.MemberId = element;
          await DeliveryPerson.createInstance(memberParam);
        });
        define.forEach(async (element) => {
          const defineParam = updateParam;
          defineParam.DeliverDefineWorkId = element;
          await DeliverDefine.createInstance(defineParam);
        });
        const history = {
          DeliveryRequestId: newDeliverData.id,
          DeliveryId: newDeliverData.DeliveryId,
          MemberId: memberDetails.id,
          isDeleted: false,
          ProjectId: payload.ProjectId,
          type: 'create',
          description: `${loginUser.firstName} ${loginUser.lastName} Created Delivery Booking, ${payload.description}.`,
        };
        await DeliverHistory.createInstance(history);
        if (newDeliverData.status === 'Approved') {
          const object = {
            ProjectId: payload.ProjectId,
            MemberId: memberDetails.id,
            DeliveryRequestId: newDeliverData.id,
            isDeleted: false,
            type: 'approved',
            description: `${loginUser.firstName} ${loginUser.lastName} Approved Delivery Booking, ${payload.description}.`,
          };
          await DeliverHistory.createInstance(object);
        }
      }
    }
  },
  async checkDeliveryConflictsWithAlreadyScheduled(requestsArray, type) {
    if (requestsArray && requestsArray.length > 0) {
      const deliveryStartDateArr = [];
      const deliveryEndDateArr = [];
      const requestIds = [];
      requestsArray.forEach((data) => {
        deliveryStartDateArr.push(new Date(data.deliveryStart));
        deliveryEndDateArr.push(new Date(data.deliveryEnd));
        if (type === 'edit' && data.id) {
          requestIds.push(data.id);
        }
      });
      let condition = {
        ProjectId: requestsArray[0].ProjectId,
        status: {
          [Op.notIn]: ['Delivered', 'Expired'],
        },
      };
      if (type === 'edit') {
        condition = {
          ...condition,
          id: {
            [Op.notIn]: requestIds,
          },
        };
      }
      const isDeliveryBookingOverlapping = await DeliveryRequest.findAll({
        where: {
          ...condition,
          [Op.or]: [
            {
              [Op.or]: deliveryStartDateArr.map((date) => ({
                deliveryStart: { [Op.lte]: date },
                deliveryEnd: { [Op.gte]: date },
              })),
            },
            {
              [Op.or]: deliveryEndDateArr.map((date) => ({
                deliveryStart: { [Op.lte]: date },
                deliveryEnd: { [Op.gte]: date },
              })),
            },
          ],
        },
      });
      if (isDeliveryBookingOverlapping && isDeliveryBookingOverlapping.length > 0) {
        return true;
      }
      return false;
    }
  },
  async checkDoubleBookingAllowedOrNot(eventsArray, projectDetails, type) {
    if (!projectDetails.ProjectSettings.deliveryAllowOverlappingBooking) {
      const checkBookingOverlapping = await this.checkDeliveryConflictsWithAlreadyScheduled(
        eventsArray,
        type,
      );
      if (checkBookingOverlapping) {
        return {
          error: true,
          message:
            'This booking clashes with another booking. Overlapping is disabled by the administrator.',
        };
      }
    }
    if (
      projectDetails.ProjectSettings &&
      !projectDetails.ProjectSettings.deliveryAllowOverlappingCalenderEvents
    ) {
      const checkCalenderEventsOverlapping =
        await concreteRequestService.checkCalenderEventsOverlappingWithBooking(
          eventsArray,
          'delivery',
          type,
        );
      if (checkCalenderEventsOverlapping) {
        return {
          error: true,
          message:
            'This booking clashes with a scheduled calendar event. Overlapping is disabled by the administrator',
        };
      }
    }
    return {
      error: false,
      message: '',
    };
  },
};
module.exports = deliveryService;
