const puppeteer = require('../helpers/puppeteer');
const { parentPort } = require('worker_threads');

parentPort.on('message', async (message) => {
  const { fileLink, fileName, ProjectId } = JSON.parse(message);
  puppeteer.convertPdfToImage(fileLink, fileName, ProjectId)
    .then(async data => {
      console.log('data', data);
      parentPort.postMessage('success');
    })
    .catch(error => {
      done(null, error);
      parentPort.postMessage('error');
    });
});