/* eslint-disable no-await-in-loop */
const path = require('path');
const ExcelJS = require('exceljs');
const { stringify } = require('flatted');
const { Worker } = require('worker_threads');
let { Company, Project, Member, DeliverDefineWork, CompanyDefine, User } = require('../models');
const {
  Sequelize,
  Enterprise,
  DeliverCompany,
  CraneRequestCompany,
  ConcreteRequestCompany,
} = require('../models');

const { Op } = Sequelize;
const helper = require('../helpers/domainHelper');
const awsConfig = require('../middlewares/awsConfig');
// const mixpanelService = require('./mixpanelService');

const bulkCompanyProcess = path.join(__dirname, './bulkCompanyProcess.js');

let publicCompany;
let publicUser;
let publicMember;
const companyService = {
  async addCompany(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const companyData = inputData.body;
      const projectDetails = await Project.findByPk(companyData.ProjectId);
      if (projectDetails) {
        const exist = await Company.findOne({
          where: Sequelize.and(
            { ProjectId: companyData.ProjectId, isDeleted: false },
            Sequelize.or({
              companyName: companyData.companyName.trim().toLowerCase(),
              website: companyData.website
                ? companyData.website.trim().toLowerCase()
                : companyData.website,
            }),
          ),
        });
        if (exist) {
          done(null, { message: 'Company name/Website Already exist.' });
        } else {
          this.checkInputDatas(inputData, async (checkResponse, checkError) => {
            if (checkError) {
              done(null, checkError);
            } else {
              companyData.createdBy = inputData.user.id;
              const lastIdValue = await Company.findOne({
                where: { ProjectId: companyData.ProjectId, isDeleted: false },
                order: [['companyAutoId', 'DESC']],
              });
              let id = 1;
              const newValue = JSON.parse(JSON.stringify(lastIdValue));
              if (
                newValue &&
                newValue.companyAutoId !== null &&
                newValue.companyAutoId !== undefined
              ) {
                id = newValue.companyAutoId;
              }
              companyData.companyAutoId = id + 1;
              const newCompany = await Company.createInstance(companyData);
              if (inputData.user.domainName !== null && inputData.user.domainName !== undefined) {
                await this.createPublicCompany(companyData);
              }
              const definable = companyData.definableWorkId;
              definable.forEach(async (element) => {
                const companyParam = {
                  DeliverDefineWorkId: element,
                  CompanyId: newCompany.id,
                  ProjectId: companyData.ProjectId,
                };
                await CompanyDefine.createInstance(companyParam);
              });
              done(newCompany, false);
            }
          });
        }
      } else {
        done(null, 'Project Does not exist.');
      }
    } catch (e) {
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicCompany = modelData.Company;
    publicUser = modelData.User;
    publicMember = modelData.Member;
  },
  async createPublicCompany(newCompany) {
    await this.returnProjectModel();
    const tempData = newCompany;
    const existCompany = await publicCompany.findOne({
      where: { companyName: tempData.companyName, website: tempData.website },
    });
    if (!existCompany) {
      delete tempData.id;
      await publicCompany.createInstance(tempData);
    }
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const incomeData = inputData;
    let enterpriseValue;
    let ProjectId;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    Member = modelObj.Member;
    Company = modelObj.Company;
    Project = modelObj.Project;
    DeliverDefineWork = modelObj.DeliverDefineWork;
    CompanyDefine = modelObj.CompanyDefine;
    User = modelObj.User;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return ProjectId;
  },
  async companyLogoUpload(inputData, done) {
    try {
      await awsConfig.singleUpload(inputData, async (result, err) => {
        if (!err) {
          done(result, err);
        } else {
          done(null, err);
        }
      });
    } catch (e) {
      done(null, e);
    }
  },
  async checkInputDatas(inputData, done) {
    await this.getDynamicModel(inputData);
    const companyData = inputData.body;
    const { definableWorkId } = companyData;
    const defineList = await DeliverDefineWork.count({
      where: { id: { [Op.in]: definableWorkId }, ProjectId: companyData.ProjectId },
    });
    if (defineList === definableWorkId.length) {
      done(true, false);
    } else {
      done(null, { message: 'Some Definable feature not in this list.' });
    }
  },
  async editCompany(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const companyData = inputData.body;
      const definable = companyData.definableWorkId;
      const { id } = companyData;
      const projectDetails = await Project.findByPk(companyData.ProjectId);
      if (projectDetails) {
        const exist = await Company.findOne({
          where: Sequelize.and(
            {
              ProjectId: companyData.ProjectId,
              id: { [Op.not]: companyData.id },
            },
            Sequelize.or({
              companyName: companyData.companyName.trim().toLowerCase(),
              website: companyData.website
                ? companyData.website.trim().toLowerCase()
                : companyData.website,
            }),
          ),
        });
        if (exist) {
          done(null, { message: 'Company name/Website Already exist.' });
        } else {
          delete companyData.id;
          const updateCompany = await Company.update(companyData, { where: { id } });
          const condition = Sequelize.and({
            ProjectId: companyData.ProjectId,
            CompanyId: id,
          });
          this.updateValues(condition, inputData, async (response, error) => {
            if (!error) {
              const existDefine = await CompanyDefine.findAll({ where: condition });
              definable.forEach(async (element) => {
                const index = existDefine.findIndex((item) => item.DeliverDefineWorkId === element);
                const defineParam = {
                  CompanyId: id,
                  DeliverDefineWorkId: element,
                  ProjectId: companyData.ProjectId,
                  isDeleted: false,
                };
                if (index !== -1) {
                  await CompanyDefine.update(defineParam, {
                    where: { id: existDefine[index].id },
                  });
                } else {
                  await CompanyDefine.createInstance(defineParam);
                }
              });
              done(updateCompany, false);
            } else {
              done(null, error);
            }
          });
        }
      } else {
        done(null, 'Project Does not exist.');
      }
    } catch (e) {
      done(null, e);
    }
  },
  async updateValues(condition, inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      await CompanyDefine.update({ isDeleted: true }, { where: condition });
      done({ status: 'ok' }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async getAll(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const isInviteMemberPage = inputData.body.inviteMember;
      const incomeData = inputData.body;
      const { sort } = inputData.body;
      const { sortByField } = inputData.body;
      let searchCondition = {};
      const offset = (pageNumber - 1) * pageSize;
      const parentCompany = await Company.findAll({
        required: false,
        subQuery: false,
        include: [
          {
            association: 'Members',
            required: false,
            attributes: ['id'],
            where: {
              ProjectId: +params.ProjectId,
              isDeleted: false,
            },
          },
          {
            association: 'define',
            where: { isDeleted: false },
            required: false,
            include: ['DeliverDefineWork'],
          },
        ],
        attributes: [
          'id',
          'companyName',
          'website',
          'address',
          'secondAddress',
          'country',
          'city',
          'companyAutoId',
          'state',
          'zipCode',
          'scope',
          'logo',
        ],
        where: {
          isParent: true,
          ParentCompanyId: +params.ParentCompanyId,
          isDeleted: false,
        },
        group: ['Company.id', 'define.id', 'define.DeliverDefineWork.id', 'Members.id'],
      });
      const condition = {
        ProjectId: params.ProjectId,
        isDeleted: false,
        isParent: { [Op.not]: true },
      };
      if (incomeData.dfowFilter) {
        condition['$define.DeliverDefineWork.id$'] = incomeData.dfowFilter;
      }
      if (incomeData.companyFilter) {
        condition.companyName = {
          [Sequelize.Op.iLike]: `%${incomeData.companyFilter}%`,
        };
      }
      if (incomeData.search) {
        const searchDefault = [
          {
            companyName: {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$define.DeliverDefineWork.DFOW$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
        ];
        if (!Number.isNaN(+incomeData.search)) {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: [
                  searchDefault,
                  {
                    [Op.and]: [
                      {
                        companyAutoId: incomeData.search,
                        isDeleted: false,
                        ProjectId: params.ProjectId,
                      },
                    ],
                  },
                ],
              },
            ],
          };
        } else {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: searchDefault,
              },
            ],
          };
        }
      }
      const companyList = await Company.getAll(
        condition,
        pageSize,
        offset,
        searchCondition,
        sort,
        sortByField,
      );
      const companyArray = {};
      companyArray.count = companyList.rows.length;
      if (!isInviteMemberPage) {
        companyArray.rows = companyList.rows.slice(offset, offset + pageSize);
      } else {
        companyArray.rows = companyList.rows;
      }
      if (companyArray && companyArray.rows && companyArray.rows.length > 0 && isInviteMemberPage) {
        companyArray.rows.sort((a, b) =>
          a.companyName.toLowerCase() > b.companyName.toLowerCase() ? 1 : -1,
        );
      }
      done({ companyArray, parentCompany }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async getAllCompany(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const parentCompany = await Company.findOne({
        required: false,
        subQuery: false,
        attributes: [
          'id',
          'companyName',
          'website',
          'address',
          'secondAddress',
          'country',
          'city',
          'companyAutoId',
          'state',
          'zipCode',
          'scope',
          'logo',
        ],
        where: { isParent: true, ParentCompanyId: +params.ParentCompanyId, isDeleted: false },
      });
      const companyList = await Company.getAllCompany({
        ProjectId: params.ProjectId,
        isDeleted: false,
        isParent: { [Op.not]: true },
      });
      done({ companyList, parentCompany }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async getDefinableWork(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const defineRecord = await DeliverDefineWork.findAll({
        where: Sequelize.and({ ProjectId: params.ProjectId, isDeleted: false }),
      });
      defineRecord.sort((a, b) => (a.DFOW.toLowerCase() > b.DFOW.toLowerCase() ? 1 : -1));
      done({ defineRecord }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async deleteCompany(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const companyData = inputData.body;
      const { id } = inputData.body;
      let getCompanies;
      if (companyData.isSelectAll) {
        getCompanies = await Company.findAll({
          where: { ProjectId: companyData.ProjectId, isDeleted: false },
        });
      } else {
        getCompanies = await Company.findAll({
          where: { ProjectId: companyData.ProjectId, isDeleted: false, id: { [Op.in]: id } },
        });
      }
      if (getCompanies && getCompanies.length > 0) {
        getCompanies.map(async (item, index) => {
          const companyDetails = await Company.findByPk(item.id);
          if (companyDetails) {
            if (!companyDetails.isParent) {
              const isCompanyMappedToDeliveryRequest = await DeliverCompany.findOne({
                where: {
                  CompanyId: +item.id,
                  isDeleted: false,
                  ProjectId: companyData.ProjectId,
                },
              });
              const isCompanyMappedToCraneRequest = await CraneRequestCompany.findOne({
                where: {
                  CompanyId: +item.id,
                  isDeleted: false,
                  ProjectId: companyData.ProjectId,
                },
              });
              const isCompanyMappedToConcreteRequest = await ConcreteRequestCompany.findOne({
                where: {
                  CompanyId: +item.id,
                  isDeleted: false,
                  ProjectId: companyData.ProjectId,
                },
              });
              const isCompanyMappedToMember = await Member.findOne({
                where: {
                  CompanyId: +item.id,
                  isDeleted: false,
                  ProjectId: companyData.ProjectId,
                },
              });
              if (isCompanyMappedToDeliveryRequest) {
                return done(null, {
                  message: `${item.companyName} cannot be deleted. ${item.companyName} is mapped to submitted bookings`,
                });
              }
              if (isCompanyMappedToCraneRequest) {
                return done(null, {
                  message: `${item.companyName} cannot be deleted. ${item.companyName} is mapped to submitted bookings`,
                });
              }
              if (isCompanyMappedToConcreteRequest) {
                return done(null, {
                  message: `${item.companyName} cannot be deleted. ${item.companyName} is mapped to submitted bookings`,
                });
              }
              if (isCompanyMappedToMember) {
                return done(null, {
                  message: `Company ${item.companyName} cannot be deleted. The company is mapped to a member`,
                });
              }
              await Company.update(
                { isDeleted: true },
                {
                  where: {
                    id: +item.id,
                    ProjectId: companyData.ProjectId,
                    isDeleted: false,
                  },
                },
              );
              if (index === getCompanies.length - 1) {
                return done('success', false);
              }
            } else {
              done(null, { message: 'You cannot able to delete, because this is Base Company.' });
            }
          } else {
            done(null, { message: 'Company Does not Exist.' });
          }
        });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async dfowAndCompanyForBulkUploadDeliveryRequest(inputData) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const defineRecord = await DeliverDefineWork.findAll({
        where: Sequelize.and({ ProjectId: params.ProjectId, isDeleted: false }),
      });
      const parentCompany = await Company.findOne({
        required: false,
        subQuery: false,
        attributes: [
          'id',
          'companyName',
          'website',
          'address',
          'secondAddress',
          'country',
          'city',
          'companyAutoId',
          'state',
          'zipCode',
          'scope',
          'logo',
        ],
        where: { isParent: true, ParentCompanyId: +params.ParentCompanyId, isDeleted: false },
      });
      const companyList = await Company.getAllCompany({
        ProjectId: params.ProjectId,
        isDeleted: false,
        isParent: { [Op.not]: true },
      });
      const newCompanyList = [];
      await companyList.rows.forEach((element) => {
        newCompanyList.push({
          id: element.id,
          companyName: element.companyName,
        });
      });
      if (parentCompany) {
        const index = newCompanyList.findIndex(
          (item) =>
            item.id === parentCompany.id ||
            item.companyName.toLowerCase() === parentCompany.companyName.toLowerCase(),
        );
        if (index === -1) {
          newCompanyList.push({
            id: parentCompany.id,
            companyName: parentCompany.companyName,
          });
        }
      }
      const returnData = { defineRecord, newCompanyList };
      return returnData;
    } catch (e) {
      console.log(e);
    }
  },
  async checkExistCompany(req) {
    try {
      await this.getDynamicModel(req);
      const projectDetail = req.body;
      let condition = {
        isDeleted: false,
      };
      let parentCompanyCondition = {
        isDeleted: false,
        isParent: true,
      };
      if (projectDetail.id) {
        condition = {
          ...condition,
          id: { [Op.not]: projectDetail.id },
        };
      }
      if (projectDetail.id) {
        parentCompanyCondition = {
          ...parentCompanyCondition,
          id: { [Op.not]: projectDetail.id },
        };
      }
      const companyName = projectDetail.companyName.split(' ')[0];
      const { ProjectId } = req.params;
      const { ParentCompanyId } = req.params;
      const projectDetails = await Project.findByPk(ProjectId);
      if (projectDetails) {
        const existInProject = await Company.findAll({
          where: Sequelize.and(
            condition,
            { ProjectId },
            { companyName: { [Sequelize.Op.iLike]: `${companyName}%` } },
          ),
        });
        const sameAsParentCompany = await Company.findOne({
          where: Sequelize.and(
            parentCompanyCondition,
            { ParentCompanyId },
            { companyName: { [Sequelize.Op.iLike]: `${companyName}%` } },
          ),
        });
        return { existInProject, sameAsParentCompany };
      }
      return { status: 500, message: 'ProjectId does not exist.' };
    } catch (e) {
      return e;
    }
  },
  async createCompany(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { file } = inputData;
      const ProjectId = +inputData.params.ProjectId;
      const memberDetail = await Member.findOne({
        where: [
          Sequelize.and(
            {
              UserId: inputData.user.id,
              ProjectId,
              isDeleted: false,
            },
            Sequelize.or({ RoleId: [1, 2, 3, 4] }),
          ),
        ],
      });
      if (memberDetail) {
        if (file != null && file.originalname != null) {
          const splitValue = file.originalname.split('.');
          const extension = splitValue[splitValue.length - 1];
          const fileName = splitValue[0];
          const firstSplitFileName = fileName.split('_');
          if (firstSplitFileName.length === 3) {
            const projectFileName = firstSplitFileName[0];
            const projectId = firstSplitFileName[1];
            const projectDetails = await Project.findByPk(ProjectId);
            if (
              projectDetails.projectName.toLowerCase() === projectFileName.toLowerCase() &&
              +ProjectId === +projectId
            ) {
              if (extension === 'xlsx') {
                const newWorkbook = new ExcelJS.Workbook();
                await newWorkbook.xlsx.readFile(file.path);
                const worksheet = newWorkbook.getWorksheet('Company');
                this.createCompanyData(worksheet, inputData, (resValue, error) => {
                  if (!error) {
                    return done(resValue, false);
                  }
                  return done(null, error);
                });
              } else {
                return done(null, { message: 'Please choose valid file' });
              }
            } else {
              return done(null, { message: 'Invalid file' });
            }
          } else {
            return done(null, { message: 'Invalid file' });
          }
        } else {
          return done(null, { message: 'Please select a file.' });
        }
      } else {
        return done(null, { message: 'Project Does not exist or you are not a valid member.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createCompanyData(companyWorksheet, inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const existProjectId = inputData.params.ProjectId;
      const ProjectId = +existProjectId;
      const loginUser = inputData.user;
      const projectDetails = await Project.findByPk(ProjectId);
      let fileFormat = true;
      const worksheet = companyWorksheet;
      const companyRecords = [];
      let headers;
      if (worksheet) {
        worksheet.eachRow(async (rowData, rowNumber) => {
          const singleRowData = rowData.values;
          singleRowData.shift();
          if (rowNumber === 2) {
            headers = singleRowData;
          } else if (singleRowData.length > 1 && rowNumber >= 2) {
            const getRow = singleRowData;
            const companyName = getRow[1];
            if (companyName) {
              companyRecords.push(singleRowData);
            }
          }
        });
        if (companyRecords !== undefined && companyRecords.length === 0) {
          return done(null, {
            message: 'Please upload proper document / Please fill mandatory column.',
          });
        }
        if (inputData.file) {
          if (+headers.length !== 11) {
            fileFormat = false;
          }
        }
        if (fileFormat) {
          const worker = new Worker(bulkCompanyProcess);
          const object = stringify({
            projectDetails,
            loginUser,
            companyRecords,
            ProjectId,
            inputData,
          });
          worker.postMessage(object);
          worker.on('message', (data) => {
            if (data === 'done') {
              const socketObject = {
                message: data,
                loginUserId: loginUser.id,
              };
              global.io.emit('bulkCompanyNotification', socketObject);
              worker.terminate();
            }
          });
          worker.on('exit', (data) => {
            console.log('worker thread exit ', data);
          });
          done({ message: 'success' }, false);
        } else {
          done(null, { message: 'Invalid File.!' });
        }
      } else {
        done(null, { message: 'Invalid File' });
      }
    } catch (e) {
      done(null, e);
    }
  },
};
module.exports = companyService;
