const moment = require('moment');
const { Sequelize } = require('sequelize');

const { Op } = Sequelize;
module.exports = (sequelize, DataTypes) => {
  const DeliveryRequest = sequelize.define(
    'DeliveryRequest',
    {
      description: DataTypes.STRING,
      isDeleted: DataTypes.BOOLEAN,
      CompanyId: DataTypes.INTEGER,
      escort: DataTypes.BOOLEAN,
      notes: DataTypes.STRING,
      DeliveryId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      status: {
        type: DataTypes.ENUM,
        values: ['Pending', 'Approved', 'Declined', 'Delivered', 'Expired'],
      },
      approvedBy: DataTypes.INTEGER,
      deliveryStart: DataTypes.DATE,
      approved_at: DataTypes.DATE,
      deliveryEnd: DataTypes.DATE,
      ProjectId: DataTypes.INTEGER,
      vehicleDetails: DataTypes.STRING,
      createdBy: DataTypes.INTEGER,
      isQueued: DataTypes.BOOLEAN,
      isAllDetailsFilled: DataTypes.BOOLEAN,
      cranePickUpLocation: DataTypes.STRING,
      craneDropOffLocation: DataTypes.STRING,
      isAssociatedWithCraneRequest: DataTypes.BOOLEAN,
      CraneRequestId: DataTypes.INTEGER,
      requestType: DataTypes.STRING,
      recurrenceId: DataTypes.INTEGER,
      LocationId: DataTypes.INTEGER,
      isCreatedByGuestUser:DataTypes.BOOLEAN,
      // chosenDateOfMonth: {
      //   type: DataTypes.BOOLEAN,
      // },
      // dateOfMonth: {
      //   type: DataTypes.STRING,
      // },
      // monthlyRepeatType: {
      //   type: DataTypes.STRING,
      // },
      // recurrence: {
      //   type: DataTypes.ENUM,
      //   values: ['Does Not Repeat', 'Daily', 'Weekly', 'Monthly', 'Yearly', 'Custom'],
      // },
      // repeatEveryCount: DataTypes.STRING,
      // repeatEveryType: DataTypes.STRING,
      // days: DataTypes.ARRAY(DataTypes.STRING),
    },
    {},
  );
  DeliveryRequest.associate = (models) => {
    // associations can be defined
    DeliveryRequest.hasMany(models.DeliveryPerson, {
      as: 'memberDetails',
      foreignKey: 'DeliveryId',
    });
    DeliveryRequest.hasMany(models.DeliveryPerson, {
      as: 'memberEditData',
      foreignKey: 'DeliveryId',
    });
    DeliveryRequest.belongsTo(models.Member, {
      as: 'approverDetails',
      foreignKey: 'approvedBy',
    });
    DeliveryRequest.belongsTo(models.Project);
    DeliveryRequest.belongsTo(models.Member, {
      as: 'createdUserDetails',
      foreignKey: 'createdBy',
    });
    DeliveryRequest.hasMany(models.DeliverCompany, {
      as: 'companyDetails',
      foreignKey: 'DeliveryId',
    });
    DeliveryRequest.hasMany(models.DeliverCompany, {
      as: 'companyEditData',
      foreignKey: 'DeliveryId',
    });
    DeliveryRequest.hasMany(models.DeliverGate, {
      as: 'gateDetails',
      foreignKey: 'DeliveryId',
    });
    DeliveryRequest.hasMany(models.DeliverEquipment, {
      as: 'equipmentDetails',
      foreignKey: 'DeliveryId',
    });
    DeliveryRequest.hasMany(models.DeliverDefine, {
      as: 'defineWorkDetails',
      foreignKey: 'DeliveryId',
    });
    DeliveryRequest.hasMany(models.DeliverDefine, {
      as: 'defineEditData',
      foreignKey: 'DeliveryId',
    });
    DeliveryRequest.hasMany(models.VoidList, {
      as: 'voidList',
      foreignKey: 'DeliveryRequestId',
    });
    DeliveryRequest.belongsTo(models.RequestRecurrenceSeries, {
      as: 'recurrence',
      foreignKey: 'recurrenceId',
    });
    DeliveryRequest.belongsTo(models.Locations, {
      as: 'location',
      foreignKey: 'LocationId',
    });
    DeliveryRequest.hasMany(models.DeliverComment, {
      as: 'comments',
      foreignKey: 'DeliveryRequestId',
    });
    DeliveryRequest.hasMany(models.DeliverAttachement, {
      as: 'attachements',
      foreignKey: 'DeliveryRequestId',
    });
    DeliveryRequest.hasMany(models.DeliverHistory, {
      as: 'history',
      foreignKey: 'DeliveryRequestId',
    });
  };
  DeliveryRequest.createInstance = async (paramData) => {
    const newDeliveryRequest = await DeliveryRequest.create(paramData);
    return newDeliveryRequest;
  };
  DeliveryRequest.getAll = async (
    attr,
    roleId,
    memberId,
    limit,
    offset,
    searchCondition,
    order,
    sort,
    sortColumn,
  ) => {
    const sortByFieldName = sortColumn || 'id';
    let sortByColumnType = sort || 'DESC';
    if (order) {
      sortByColumnType = order;
    }
    let orderQuery;
    if (sortByFieldName === 'equipment') {
      orderQuery = [['equipmentDetails', 'Equipment', 'equipmentName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'gate') {
      orderQuery = [['gateDetails', 'Gate', 'gateName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'approvedUser') {
      orderQuery = [['approverDetails', 'User', 'firstName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'company') {
      orderQuery = [['companyDetails', 'Company', 'companyName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'dfow') {
      orderQuery = [['defineWorkDetails', 'DeliverDefineWork', 'DFOW', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'member') {
      orderQuery = [['memberDetails', 'Member', 'User', 'firstName', `${sortByColumnType}`]];
    }
    if (
      sortByFieldName === 'description' ||
      sortByFieldName === 'deliveryStart' ||
      sortByFieldName === 'id' ||
      sortByFieldName === 'status'
    ) {
      orderQuery = [[`${sortByFieldName}`, `${sortByColumnType}`]];
    }
    let requiredCondition = true;
    if (roleId === 2) {
      requiredCondition = false;
    }
    const newDeliveryRequest = await DeliveryRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id','isGuestUser'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'Project',
          attributes: ['projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          required: false,
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          required: false,
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'DeliveryRequestId'],
        },
        {
          association: 'location',
          required: false,
          attributes: ['id', 'locationPath'],
        },
      ],
      where: { ...attr, ...searchCondition, isDeleted: false },
      attributes: [
        'id',
        'description',
        'deliveryStart',
        'deliveryEnd',
        'status',
        'notes',
        'DeliveryId',
        'approved_at',
        'escort',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
      order: orderQuery,
    });
    return newDeliveryRequest;
  };
  DeliveryRequest.getCalendarData = async (
    attr,
    roleId,
    memberId,
    searchCondition,
    order,
    sort,
    sortColumn,
  ) => {
    const sortByFieldName = sortColumn || 'id';
    let sortByColumnType = sort || 'DESC';
    if (order) {
      sortByColumnType = order;
    }
    let orderQuery;
    if (sortByFieldName === 'equipment') {
      orderQuery = [['equipmentDetails', 'Equipment', 'equipmentName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'approvedUser') {
      orderQuery = [['approverDetails', 'User', 'firstName', `${sortByColumnType}`]];
    }
    if (
      sortByFieldName === 'description' ||
      sortByFieldName === 'deliveryStart' ||
      sortByFieldName === 'id' ||
      sortByFieldName === 'status'
    ) {
      orderQuery = [[`${sortByFieldName}`, `${sortByColumnType}`]];
    }
    let requiredCondition = true;
    if (roleId === 2) {
      requiredCondition = false;
    }
    const newDeliveryRequest = await DeliveryRequest.findAndCountAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id','isGuestUser'],
              include: [{ association: 'User', attributes: ['email', 'firstName', 'lastName'] }],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'Project',
          attributes: ['projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          required: false,
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'DeliveryRequestId'],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
          ],
        },
        {
          association: 'location',
          required: false,
          attributes: ['id', 'locationPath'],
        },
      ],
      where: { ...attr, ...searchCondition, isDeleted: false },
      attributes: [
        'id',
        'description',
        'deliveryStart',
        'deliveryEnd',
        'status',
        'DeliveryId',
        'notes',
        'escort',
        'approved_at',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
      order: orderQuery,
    });
    return newDeliveryRequest;
  };

  DeliveryRequest.getWeeklyCalendarData = async (
    req,
    attr,
    roleId,
    start,
    end,
    startTime,
    endTime,
    startDate,
    endDate,
    typeFormat,
    timezone,
    eventStartTime,
    eventEndTime,
  ) => {
    let requiredCondition = true;
    let commonSearch = {
      ...attr,
      isDeleted: false,
    };
    if (roleId === 2) {
      requiredCondition = false;
    }
    let startDate1 = start;
    let endDate1 = end;
    if (req.body.startDate && req.body.endDate) {
      startDate1 = req.body.startDate;
      endDate1 = req.body.endDate;
    }
    const finalFromTimeSeconds = timeToSeconds(eventStartTime);
    const finalToTimeSeconds = timeToSeconds(eventEndTime);

    function timeToSeconds(timeString) {
      const [hours, minutes, seconds] = timeString.split(':');
      return +hours * 60 * 60 + +minutes * 60 + +seconds;
    }

    let singleQuery = true;

    if (finalFromTimeSeconds > finalToTimeSeconds) {
      singleQuery = false;
    } else {
      singleQuery = true;
    }
    if (typeFormat) {
      if (start) {
        const startDateTime = moment(startDate1, 'YYYY-MM-DD');
        const endDateTime = moment(endDate1, 'YYYY-MM-DD');
        const nextDay = moment(startDate1).add(1, 'days');
        const queryStartDate = nextDay.format('YYYY-MM-DD');
        if (singleQuery) {
          commonSearch = {
            [Op.and]: [
              sequelize.literal(`(DATE_TRUNC('day', "DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDateTime}' AND '${endDateTime}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time <= '${eventEndTime}')`),
            ],
          };
        } else {
          commonSearch = {
            [Op.or]: [
              sequelize.literal(`(DATE_TRUNC('day', "DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDateTime}' AND '${endDateTime}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time <= '23:59:59')`),
              sequelize.literal(`(DATE_TRUNC('day', "DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${queryStartDate}' AND '${endDateTime}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time >= '00:00:00'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time <=  '${eventEndTime}')`),
            ],
          };
        }
      }
    } else {
      const nextDay = moment(startDate1).add(1, 'days');
      const queryStartDate = nextDay.format('YYYY-MM-DD');
      if (singleQuery) {
        commonSearch = {
          [Op.and]: [
            sequelize.literal(`(DATE_TRUNC('day', "DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate1}' AND '${endDate1}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time <= '${eventEndTime}')`),
          ],
        };
      } else {
        commonSearch = {
          [Op.or]: [
            sequelize.literal(`(DATE_TRUNC('day', "DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate1}' AND '${endDate1}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time <= '23:59:59')`),
            sequelize.literal(`(DATE_TRUNC('day', "DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${queryStartDate}' AND '${endDate1}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time >= '00:00:00'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time <=  '${eventEndTime}')`),
          ],
        };
      }
    }

    const newDeliveryRequest = await DeliveryRequest.findAndCountAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id','isGuestUser'],
              include: [{ association: 'User', attributes: ['email', 'firstName', 'lastName'] }],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'Project',
          attributes: ['projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          required: false,
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'DeliveryRequestId'],
        },
        {
          association: 'location',
          required: false,
        },
      ],
      where: { ...attr, ...commonSearch, isDeleted: false },
      attributes: [
        'id',
        'description',
        'deliveryStart',
        'deliveryEnd',
        'status',
        'DeliveryId',
        'notes',
        'escort',
        'approved_at',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
    });
    return newDeliveryRequest;
  };
  DeliveryRequest.getNDRData = async (attr) => {
    const newDeliveryRequest = await DeliveryRequest.findOne({
      subQuery: false,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id','isGuestUser'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [
            {
              include: [
                {
                  required: false,
                  where: { isDeleted: false, isActive: true, isCraneType: true },
                  association: 'PresetEquipmentType',
                  attributes: ['id', 'equipmentType', 'isCraneType'],
                },
              ],
              association: 'Equipment',
              attributes: ['equipmentName', 'id'],
            },
          ],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'voidList',
          required: false,
          attributes: ['id', 'MemberId', 'ProjectId', 'DeliveryRequestId'],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
          ],
        },
        {
          association: 'location',
          required: false,
        },
      ],
      where: { ...attr, isDeleted: false },
      attributes: [
        'id',
        'description',
        'deliveryStart',
        'deliveryEnd',
        'status',
        'notes',
        'DeliveryId',
        'escort',
        'approved_at',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
    });
    return newDeliveryRequest;
  };
  DeliveryRequest.updateInstance = async (id, args) => {
    const updatedDeliveryRequest = await DeliveryRequest.update(args, { where: { id } });
    return updatedDeliveryRequest;
  };
  DeliveryRequest.getCraneAssociatedRequest = async (
    req,
    roleId,
    memberId,
    attr,
    descriptionFilter,
    startdate,
    enddate,
    companyFilter,
    memberFilter,
    equipmentFilter,
    statusFilter,
    idFilter,
    pickFrom,
    pickTo,
    search,
    gateFilter,
    order,
    sort,
    sortColumn,
    voidType,
    dateFilter,
  ) => {
    const sortByFieldName = sortColumn || 'id';
    let sortByColumnType = sort || 'DESC';
    if (order) {
      sortByColumnType = order;
    }
    let locationFilter;
    if (req.body.locationFilter) {
      locationFilter = req.body.locationFilter;
    }
    let orderQuery;
    if (sortByFieldName === 'equipment') {
      orderQuery = [['equipmentDetails', 'Equipment', 'equipmentName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'approvedUser') {
      orderQuery = [['approverDetails', 'User', 'firstName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'datetime') {
      orderQuery = [['deliveryStart', `${sortByColumnType}`]];
    }
    if (
      sortByFieldName === 'description' ||
      sortByFieldName === 'deliveryStart' ||
      sortByFieldName === 'id' ||
      sortByFieldName === 'status' ||
      sortByFieldName === 'requestType'
    ) {
      orderQuery = [[`${sortByFieldName}`, `${sortByColumnType}`]];
    }
    let commonSearch = {
      ...attr,
      isDeleted: false,
      isQueued: false,
    };
    if (+voidType === 0) {
      commonSearch.isAssociatedWithCraneRequest = true;
    }
    if (descriptionFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ description: { [Sequelize.Op.iLike]: `%${descriptionFilter}%` } }],
          },
        ],
      };
    }
    if (pickFrom) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ cranePickUpLocation: { [Sequelize.Op.iLike]: `%${pickFrom}%` } }],
          },
        ],
      };
    }
    if (pickTo) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ craneDropOffLocation: { [Sequelize.Op.iLike]: `%${pickTo}%` } }],
          },
        ],
      };
    }
    if (memberFilter > 0) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$memberDetails.Member.id$': +memberFilter,
              },
            ],
          },
        ],
      };
    }
    if (equipmentFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$equipmentDetails.Equipment.equipmentName$': {
                  [Sequelize.Op.iLike]: `${equipmentFilter}`,
                },
              },
            ],
          },
        ],
      };
    }
    if (locationFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$location.locationPath$': {
                  [Sequelize.Op.iLike]: `${locationFilter}`,
                },
              },
            ],
          },
        ],
      };
    }
    if (typeof companyFilter === 'string' && companyFilter !== '') {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$companyDetails.Company.companyName$': {
                  [Sequelize.Op.iLike]: `${companyFilter}`,
                },
              },
            ],
          },
        ],
      };
    }
    if (dateFilter) {
      const startDateTime = moment(dateFilter, 'YYYY-MM-DD')
        .startOf('day')
        .utcOffset(Number(req.headers.timezoneoffset), true);
      const endDateTime = moment(dateFilter, 'YYYY-MM-DD')
        .endOf('day')
        .utcOffset(Number(req.headers.timezoneoffset), true);
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                deliveryStart: {
                  [Op.between]: [moment(startDateTime), moment(endDateTime)],
                },
              },
            ],
          },
        ],
      };
    }
    if (statusFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ status: statusFilter }],
          },
        ],
      };
    }
    if (idFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ CraneRequestId: idFilter }],
          },
        ],
      };
    }
    if (startdate) {
      const startDateTime = moment(startdate, 'YYYY-MM-DD')
        .startOf('day')
        .utcOffset(Number(req.headers.timezoneoffset), true);
      const endDateTime = moment(enddate, 'YYYY-MM-DD')
        .endOf('day')
        .utcOffset(Number(req.headers.timezoneoffset), true);
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                deliveryStart: {
                  [Op.between]: [moment(startDateTime), moment(endDateTime)],
                },
              },
            ],
          },
        ],
      };
    }
    if (gateFilter > 0) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$gateDetails.Gate.id$': +gateFilter,
              },
            ],
          },
        ],
      };
    }
    if (search) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              { description: { [Sequelize.Op.iLike]: `%${search}%` } },
              { cranePickUpLocation: { [Sequelize.Op.iLike]: `%${search}%` } },
              { craneDropOffLocation: { [Sequelize.Op.iLike]: `%${search}%` } },
              {
                '$equipmentDetails.Equipment.equipmentName$': {
                  [Sequelize.Op.iLike]: `%${search}%`,
                },
              },
              {
                '$location.locationPath$': {
                  [Sequelize.Op.iLike]: `%${search}%`,
                },
              },
            ],
          },
        ],
      };
    }
    let requiredCondition = true;
    if (roleId === 2) {
      requiredCondition = false;
    }
    const newDeliveryRequest = await DeliveryRequest.findAll({
      subQuery: false,
      distinct: true,
      required: false,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'UserId','isGuestUser'],
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'companyDetails',
          required: false,
          where: { isDeleted: false },
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
        },
        {
          association: 'Project',
          attributes: ['projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              include: [
                {
                  required: false,
                  where: { isDeleted: false, isActive: true, isCraneType: true },
                  association: 'PresetEquipmentType',
                  attributes: ['id', 'equipmentType', 'isCraneType'],
                },
              ],
              association: 'Equipment',
              attributes: ['equipmentName', 'id'],
            },
          ],
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'DeliveryRequestId'],
        },
        {
          required: false,
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
          ],
        },
        {
          association: 'location',
          required: false,
        },
      ],
      where: commonSearch,
      attributes: [
        'id',
        'description',
        'deliveryStart',
        'deliveryEnd',
        'status',
        'notes',
        'DeliveryId',
        'approved_at',
        'escort',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
      ],
    });
    return newDeliveryRequest;
  };
  DeliveryRequest.getSingleDeliveryRequestData = async (attr) => {
    const newDeliveryRequest = await DeliveryRequest.findOne({
      subQuery: false,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id','isGuestUser'],
              include: [{ association: 'User', attributes: ['email', 'firstName', 'lastName'] }],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
          ],
        },
        {
          association: 'location',
          required: false,
        },
      ],
      where: { ...attr, isDeleted: false },
      attributes: [
        'id',
        'description',
        'deliveryStart',
        'deliveryEnd',
        'DeliveryId',
        'status',
        'notes',
        'CraneRequestId',
        'ProjectId',
        'CraneRequestId',
        'approved_at',
        'escort',
        'vehicleDetails',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
      ],
    });
    return newDeliveryRequest;
  };
  DeliveryRequest.upcomingDeliveryRequestForMobile = async (condition, ProjectId) => {
    const commonSearch = {
      isDeleted: false,
      ProjectId,
      deliveryStart: { [Op.gt]: new Date() },
      requestType: 'deliveryRequestWithCrane',
      isAssociatedWithCraneRequest: true,
      ...condition,
    };
    const DeliveryRequestData = await DeliveryRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id','isGuestUser'],
              include: [{ association: 'User', attributes: ['email', 'firstName', 'lastName'] }],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
          ],
        },
        {
          association: 'location',
          required: false,
        },
      ],
      where: commonSearch,
      limit: 2,
      attributes: [
        'id',
        'description',
        'deliveryStart',
        'deliveryEnd',
        'DeliveryId',
        'status',
        'notes',
        'CraneRequestId',
        'ProjectId',
        'CraneRequestId',
        'approved_at',
        'escort',
        'vehicleDetails',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
      ],
      order: [['deliveryStart', 'ASC']],
    });
    return DeliveryRequestData;
  };
  DeliveryRequest.guestGetNDRData = async (attr) => {
    const newDeliveryRequest = await DeliveryRequest.findOne({
      subQuery: false,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'gateDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Gate', attributes: ['gateName', 'id'] }],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [
            {
              include: [
                {
                  required: false,
                  where: { isDeleted: false, isActive: true, isCraneType: true },
                  association: 'PresetEquipmentType',
                  attributes: ['id', 'equipmentType', 'isCraneType'],
                },
              ],
              association: 'Equipment',
              attributes: ['equipmentName', 'id'],
            },
          ],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'voidList',
          required: false,
          attributes: ['id', 'MemberId', 'ProjectId', 'DeliveryRequestId'],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
          ],
        },
        {
          association: 'location',
          required: false,
        },
        {
          association: 'comments',
          required: false,
          include: [
            {
              association: 'Member',
              attributes: ['id'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'firstName', 'lastName', 'profilePic'],
                },
              ],
            },
          ],
          order: [['id', 'DESC']],
        },
        {
          association: 'attachements',
          where: { isDeleted: false },
          order: [['id', 'DESC']],
          required: false,
        },
        {
          association: 'history',
          required: false,
          include: [
            {
              association: 'Member',
              include: [
                { association: 'User', attributes: ['firstName', 'lastName', 'profilePic'] },
              ],
            },
          ],
          order: [['id', 'DESC']],
        },
      ],
      where: { ...attr, isDeleted: false },
      attributes: [
        'id',
        'description',
        'deliveryStart',
        'deliveryEnd',
        'status',
        'notes',
        'DeliveryId',
        'escort',
        'approved_at',
        'vehicleDetails',
        'isQueued',
        'isAllDetailsFilled',
        'cranePickUpLocation',
        'craneDropOffLocation',
        'isAssociatedWithCraneRequest',
        'CraneRequestId',
        'requestType',
        'LocationId',
        'isCreatedByGuestUser',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
    });
    return newDeliveryRequest;
  };
  return DeliveryRequest;
};
