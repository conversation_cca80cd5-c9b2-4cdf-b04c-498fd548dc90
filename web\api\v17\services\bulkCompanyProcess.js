const { parentPort } = require('worker_threads');
const { parse } = require('flatted');
const { Company, CompanyDefine, Member, DeliverDefineWork } = require('../models');
const companyservice = require('./companyService');
const { companyService } = require('.');

parentPort.on('message', async (message) => {
  const { projectDetails, loginUser, companyRecords, ProjectId, inputData } = parse(message);
  await companyservice.getDynamicModel(inputData);
  /* eslint-disable no-restricted-syntax */
  /* eslint-disable no-await-in-loop */
  for (const [i, element] of companyRecords.entries()) {
    const getRow = element;
    getRow.shift();
    const row = {
      companyName: getRow[0],
      dfow: getRow[1],
      address_line_1: getRow[2],
      address_line_2: getRow[3],
      country: getRow[4],
      state: getRow[5],
      city: getRow[6],
      zipcode: getRow[7],
      website: getRow[8],
      additional_notes: getRow[9],
    };
    if (row.companyName) {
      if (projectDetails) {
        const memberDetails = await Member.getBy({
          UserId: loginUser.id,
          ProjectId,
          isActive: true,
          isDeleted: false,
        });
        if (memberDetails) {
          if (row.companyName) {
            const companyParam = {
              companyName: row.companyName,
              address: row.address_line_1 ? row.address_line_1 : null,
              secondAddress: row.address_line_2 ? row.address_line_2 : null,
              country: row.country ? row.country : null,
              state: row.state ? row.state : null,
              city: row.city ? row.city : null,
              website: null,
              zipCode: row.zipcode ? row.zipcode : null,
              additional_notes: row.additional_notes ? row.additional_notes : null,
              ProjectId,
              isParent: false,
            };
            if (row.website) {
              if (typeof row.website === 'object') {
                companyParam.website = row.website.text;
              } else {
                companyParam.website = row.website;
              }
            }
            const lastIdValue = await Company.findOne({
              where: { ProjectId, isDeleted: false },
              order: [['companyAutoId', 'DESC']],
            });
            let id = 1;
            const newValue = JSON.parse(JSON.stringify(lastIdValue));
            if (
              newValue &&
              newValue.companyAutoId !== null &&
              newValue.companyAutoId !== undefined
            ) {
              id = newValue.companyAutoId;
            }
            companyParam.companyAutoId = id + 1;
            companyParam.isParent = false;
            const newCompany = await Company.createInstance(companyParam);
            if (inputData.user.domainName !== null && inputData.user.domainName !== undefined) {
              await companyService.createPublicCompany(companyParam);
            }
            if (row.dfow) {
              const dfowData = await DeliverDefineWork.findOne({
                where: { DFOW: row.dfow, ProjectId },
              });
              const companyDefineParams = {
                DeliverDefineWorkId: dfowData.id,
                CompanyId: newCompany.id,
                ProjectId,
              };
              await CompanyDefine.createInstance(companyDefineParams);
            }
          }
        }
      }
    }
    if (companyRecords.length - 1 === i) {
      console.log('success');
    }
  }
  parentPort.postMessage('done');
});
