const { Router } = require('express');
const { validate } = require('express-validation');
const passportConfig = require('../config/passport');
const { HistoryController } = require('../controllers');
const { historyValidation } = require('../middlewares/validations');

const commentRoute = {
  get router() {
    const router = Router();
    router.get(
      '/get_history/:DeliveryRequestId/?:ParentCompanyId',
      validate(historyValidation.getHistory, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      HistoryController.getHistory,
    );
    return router;
  },
};
module.exports = commentRoute;
