module.exports = (sequelize, DataTypes) => {
  const ConcreteRequestLocation = sequelize.define(
    'ConcreteRequestLocation',
    {
      ConcreteRequestId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      ConcreteRequestCode: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ConcreteLocationId: DataTypes.INTEGER,
      ProjectId: DataTypes.INTEGER,
    },
    {},
  );
  ConcreteRequestLocation.associate = (models) => {
    ConcreteRequestLocation.belongsTo(models.ConcreteRequest, {
      as: 'concreteRequest',
      foreignKey: 'ConcreteRequestId',
    });
    ConcreteRequestLocation.belongsTo(models.ConcreteLocation);
  };
  ConcreteRequestLocation.createInstance = async (paramData) => {
    const newConcreteRequestLocation = await ConcreteRequestLocation.create(paramData);
    return newConcreteRequestLocation;
  };
  return ConcreteRequestLocation;
};
