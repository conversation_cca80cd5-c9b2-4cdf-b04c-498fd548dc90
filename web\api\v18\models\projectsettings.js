module.exports = (sequelize, DataTypes) => {
  const projectSettings = sequelize.define(
    'ProjectSettings',
    {
      deliveryWindowTime: DataTypes.INTEGER,
      deliveryWindowTimeUnit: DataTypes.STRING,
      ProjectId: DataTypes.INTEGER,
      isAutoApprovalEnabled: DataTypes.BOOLEAN,
      deliveryAllowOverlappingBooking: DataTypes.BOOLEAN,
      deliveryAllowOverlappingCalenderEvents: DataTypes.BOOLEAN,
      craneAllowOverlappingBooking: DataTypes.BOOLEAN,
      craneAllowOverlappingCalenderEvents: DataTypes.BOOLEAN,
      concreteAllowOverlappingBooking: DataTypes.BOOLEAN,
      concreteAllowOverlappingCalenderEvents: DataTypes.BOOLEAN,
      statusColorCode: DataTypes.TEXT,
      defaultStatusColor: DataTypes.TEXT,
      deliveryCard: DataTypes.TEXT,
      craneCard: DataTypes.TEXT,
      concreteCard: DataTypes.TEXT,
      defaultDeliveryCard: DataTypes.TEXT,
      defaultCraneCard: DataTypes.TEXT,
      defaultConcreteCard: DataTypes.TEXT,
      isPublicWebsiteEnabled: DataTypes.BOOLEAN,
      shareProjectInformation: DataTypes.BOOLEAN,
      projectLogisticPlanUrl: DataTypes.TEXT,
      allowGuestToAddDeliveryBooking: DataTypes.BOOLEAN,
      allowGuestToAddCraneBooking: DataTypes.BOOLEAN,
      allowGuestToAddConcreteBooking: DataTypes.BOOLEAN,
      allowGuestToViewDeliveryCalendar: DataTypes.BOOLEAN,
      allowGuestToViewCraneCalendar: DataTypes.BOOLEAN,
      allowGuestToViewConcreteCalendar: DataTypes.BOOLEAN,
      autoRefreshRateInMinutes: DataTypes.STRING,
      publicWebsiteUrl: DataTypes.TEXT,
      shareprojectLogisticPlan: DataTypes.BOOLEAN,
      pdfOriginalName: DataTypes.TEXT,
      isPdfUploaded: DataTypes.BOOLEAN,
      isDefaultColor: DataTypes.BOOLEAN,
      useTextColorAsLegend: DataTypes.BOOLEAN,
      convertedImageLinks: DataTypes.STRING,
    },
    {},
  );
  projectSettings.associate = (models) => {
    projectSettings.belongsTo(models.Project);
    return projectSettings;
  };

  projectSettings.getCalendarStatusColor = async (ProjectId) => {
    const status = await projectSettings.findOne({
      where: {
        ProjectId,
      },
      attributes: ['statusColorCode', 'isDefaultColor','useTextColorAsLegend',],
    });
    return status;
  };

  projectSettings.getCalendarCard = async (ProjectId) => {
    const status = await projectSettings.findOne({
      where: {
        ProjectId,
      },
      attributes: ['deliveryCard', 'craneCard', 'concreteCard'],
    });
    return status;
  };

  return projectSettings;
};
