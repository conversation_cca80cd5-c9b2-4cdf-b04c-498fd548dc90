const status = require('http-status');
const ApiError = require('../helpers/apiError');
const { User, Enterprise, Sequelize, ParentCompany, Project } = require('../models');
const { UserSerializer } = require('../serializers');
const { adminService, authService } = require('../services');

const AdminController = {
  /**
   * @params {header: JWT 'token'}
   * @returns {Users}
   */
  async users(req, res) {
    let users = await User.getAll();

    if (users.length === 0) {
      res.status(status.OK).json({ users });
    } else {
      users = users.map((user) => UserSerializer.serialize(user));
      res.status(status.OK).json({ users });
    }
  },
  async adminLogin(req, res, next) {
    try {
      const userData = req.body;
      await adminService.adminLogin(userData, async (userDetail, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: `Login Successful`,
            token: userDetail.token,
            userDetails: UserSerializer.serialize(userDetail),
          });
        }
      });
    } catch (error) {
      next(error);
    }
  },
  async adminForgotPassword(req, res, next) {
    const user = await User.findOne({
      where: Sequelize.and(
        { email: req.body.email },
        Sequelize.or({ userType: ['super admin', 'folloit admin'] }),
      ),
    });
    if (user) {
      try {
        await authService.forgotPassword(user, (userData, error) => {
          if (error) {
            next(error);
          } else {
            res.status(status.OK).json({ message: 'Reset password email sent successfully' });
          }
        });
      } catch (error) {
        next(error);
      }
    } else {
      const error = new ApiError("Email doesn't exist", status.NOT_FOUND);
      next(error);
    }
  },
  async createAccountAdmin(req, res, next) {
    // await sequelize.transaction(async (t) => {
    const userData = req.body;
    const userExist = await User.findOne({
      where: Sequelize.or({ email: userData.basicDetails.email }),
    });
    try {
      const domainExist = await Enterprise.findOne({
        where: Sequelize.and({ name: userData.domainDetails.name }),
      });
      if (!domainExist) {
        const emailDomainName = await adminService.emailDomain(userData.basicDetails);
        const existParentCompany = await ParentCompany.getBy({ emailDomainName });
        let projectDet;
        if (existParentCompany) {
          projectDet = await Project.findAll({
            where: { ParentCompanyId: existParentCompany.id },
          });
        }
        if (projectDet && projectDet.length > userData.projectCount) {
          const err = new ApiError('Please enter higher project count', status.BAD_REQUEST);
          next(err);
        } else {
          const account =
            projectDet && projectDet[0] !== undefined ? projectDet[0].isAccount : false;
          if (account === true) {
            const err = new ApiError('Already grouped to a account', status.BAD_REQUEST);
            next(err);
          } else if (userExist) {
            userData.userExist = userExist;
            adminService.createExistAccountAdmin(userData, async (userDetail, error) => {
              if (error) {
                next(error);
              } else {
                res.status(status.CREATED).json({
                  message: 'Account Admin Created Successfully.',
                  user: UserSerializer.serialize(userDetail),
                });
              }
            });
          } else if (userData.group === true) {
            adminService.createNewAccountAdmin(userData, async (userDetail, error) => {
              if (error) {
                next(error);
              } else {
                res.status(status.CREATED).json({
                  message: 'Account Admin Created Successfully.',
                  user: UserSerializer.serialize(userDetail),
                });
              }
            });
          } else {
            adminService.createAccountAdmin(userData, async (userDetail, error) => {
              if (error) {
                next(error);
              } else {
                res.status(status.CREATED).json({
                  message: 'Account Admin Created Successfully.',
                  user: UserSerializer.serialize(userDetail),
                });
              }
            });
          }
        }
      } else {
        const err = new ApiError('Domain name already exist', status.BAD_REQUEST);
        next(err);
      }
    } catch (err) {
      next(err);
    }

    // });
  },
  /**
   * @params {header: JWT 'token'}
   * @params {int} req.params.id userId
   * @returns {User}
   */
  async show(req, res) {
    const user = await User.getBy({ id: req.params.id });
    if (!user) {
      res.status(status.NOT_FOUND).json({ message: 'User not found.' });
    } else {
      const serializedUser = UserSerializer.serialize(user);
      res.status(status.OK).json({ user: serializedUser });
    }
  },

  /**
   * update existing user
   * @params {header: JWT 'token'}
   * @params {string} req.body.name user name
   * @params {string} req.body.phone phone number
   * @returns {User}
   */
  async update(req, res) {
    let user = await User.getBy({ id: req.params.id });
    if (!user) {
      res.status(status.NOT_FOUND).json({ message: 'User not found.' });
    } else {
      user = await user.update(req.body);
      const serializedUser = UserSerializer.serialize(user);
      res.status(status.OK).json({ user: serializedUser });
    }
  },

  /**
   * @params {header: JWT 'token'}
   * @params {string} req.params.id
   * @returns {}
   */
  async delete(req, res) {
    let user = await User.getBy({ id: req.params.id });
    if (!user) {
      res.status(status.NOT_FOUND).json({ message: 'User not found.' });
    } else {
      user = await user.update({ isDeleted: true });
      const serializedUser = UserSerializer.serialize(user);
      res.status(status.OK).json({ user: serializedUser });
    }
  },
};

module.exports = AdminController;
