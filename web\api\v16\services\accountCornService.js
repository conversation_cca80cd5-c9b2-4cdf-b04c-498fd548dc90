const {
  Enterprise,
  Member,
  DeliverDefineWork,
  DeliveryRequest,
  DeliverGate,
  DeliverEquipment,
  DeliverCompany,
  DeliverAttachement,
  DeliverComment,
  DeliverDefine,
  VoidList,
  Notification,
  Company,
  Gates,
  CompanyDefine,
  Equipments,
  Project,
  ParentCompany,
  DeliverHistory,
  DeliveryPerson,
  DeliveryPersonNotification,
  User,
} = require('../models');
const db = require('../models');
const userRoles = require('../config/db/userRoles');
const domainHelper = require('../helpers/domainHelper');
const { generatePassword } = require('../helpers/generatePassword');
const dynamicModels = require('../helpers/dynamicSchemaModels');
const MAILER = require('../mailer');
const { bcryptPassword } = require('./password');

const dynamicUserData = [];

const accountCornService = {
  async createSchemas() {
    const accountDetail = await Enterprise.findAll({
      include: [
        {
          association: 'User',
          attributes: ['email', 'firstName', 'lastName', 'id', 'phoneNumber'],
        },
        {
          association: 'Projects',
          attributes: ['id', 'projectName', 'ParentCompanyId'],
        },
        {
          association: 'Members',
          attributes: ['id'],
        },
      ],
      where: { status: 'Not started' },
      order: [['id', 'ASC']],
      limit: 1,
    });
    if (accountDetail.length > 0) {
      this.createAccountAdmin(accountDetail, 0, [], async (response, err) => {
        if (!err) {
          if (response.length > 0) {
            await this.sendMail(response, 0);
          }
        }
      });
    }
  },

  async sendMail(newUser, enterprise) {
    const domainURL = `https://${enterprise.name.toLowerCase()}-${
      process.env.NODE_ENV
    }.folloit.com`;
    await Enterprise.update({ domainURL }, { where: { id: enterprise.id } });
    const userData = newUser;
    const mailData = {
      email: userData.email,
      firstName: userData.firstName,
      amount: enterprise.amount,
      password: userData.password,
      domainURL,
    };
    await MAILER.sendMail(
      mailData,
      'Account Creation',
      'Welcome to Follo!!!! You have got the credentials in this mail to start with us.',
      'Account Admin Created',
      async () => {
        // return { status: true };
      },
    );
    dynamicUserData.forEach(async (item, i) => {
      const useNewData = await User.findOne({ where: { email: item.email } });
      if (useNewData) {
        const newData = await Member.findOne({
          where: { UserId: useNewData.id, RoleId: 4, isDeleted: false },
        });
        if (!newData) {
          const memberData = {
            email: item.email,
            firstName: item.firstName,
            password: item.password,
            domainURL,
          };
          await MAILER.sendMail(
            memberData,
            'Account Member',
            'Account added',
            'Account Admin Created',
            async () => {},
          );
        }
      }
      if (i === dynamicUserData.length - 1) {
        return { status: true };
      }
    });
  },
  async createAccountAdmin(accountDetail, index) {
    const element = accountDetail[index];
    await this.dynamicSchemaCreation(element, accountDetail, index);
  },
  async dynamicSchemaCreation(element, accountDetail, index) {
    const schema = element.name.toLowerCase();
    await domainHelper.domainCreation(schema);
    await db.syncToSchema(schema);
    await dynamicModels.domain(schema);
    await db.DynamicRole.bulkCreateRoles(userRoles);
    await this.createUserProject(accountDetail, index);
  },
  async createUserProject(accountDetail, index) {
    const element = accountDetail[index];
    const userId = element.User.id;
    const currentUser = element.User;
    currentUser.publicSchemaId = currentUser.id;
    const existUser = await db.DynamicUser.findOne({ where: { email: currentUser.email } });
    const password = generatePassword();
    let newUser;
    if (existUser) {
      newUser = existUser;
      let encNewPwd;
      await bcryptPassword(password, (encPassword) => {
        encNewPwd = encPassword;
      });
      await db.DynamicUser.update(
        { password: encNewPwd, userType: 'account admin', isAccount: true },
        { where: { id: newUser.id } },
      );
    } else {
      currentUser.password = password;
      const user = JSON.parse(JSON.stringify(currentUser));
      user.userType = 'account admin';
      user.isAccount = true;
      delete user.id;
      newUser = await db.DynamicUser.createInstance(user);
      newUser = JSON.parse(JSON.stringify(newUser));
    }
    if (element.Projects.length > 0) {
      await Project.update(
        { isAccount: true, EnterpriseId: element.id },
        { where: { ParentCompanyId: element.Projects[0].ParentCompanyId } },
      );
      const projectList = await Project.findAll({
        where: { ParentCompanyId: element.Projects[0].ParentCompanyId },
      });
      await this.createProject(projectList, 0, newUser, element, password);
      if (index < accountDetail.length - 1) {
        await this.dynamicSchemaCreation(element, accountDetail, index + 1);
      } else {
        return true;
      }
    } else {
      const companyDet = await Company.findOne({ where: { createdBy: userId } });
      companyDet.publicSchemaId = companyDet.id;
      delete companyDet.id;
      const parentCompanyDet = await ParentCompany.findOne({
        where: { id: element.ParentCompanyId },
      });
      companyDet.createdBy = newUser.id;
      const newParentCompany = JSON.parse(JSON.stringify(parentCompanyDet));
      delete newParentCompany.id;
      const newParent = await db.DynamicParentCompany.createInstance(newParentCompany);
      companyDet.ParentCompanyId = newParent.id;
      const newCompanyDet = JSON.parse(JSON.stringify(companyDet));
      delete newCompanyDet.id;
      await db.DynamicCompany.createInstance(newCompanyDet);
      await this.EnterpriseUpdate(element, newUser, password);
      if (index < accountDetail.length - 1) {
        await this.dynamicSchemaCreation(element, accountDetail, index + 1);
      } else {
        return true;
      }
    }
  },
  async EnterpriseUpdate(EnterpriseValue, userData, password) {
    const newUser = userData;
    if (EnterpriseValue.mailSendOn !== null) {
      await Enterprise.update(
        { status: 'completed', mailSendOn: new Date() },
        { where: { id: EnterpriseValue.id } },
      );
      newUser.password = password;
      await this.sendMail(newUser, EnterpriseValue);
      return true;
    }
  },
  async convertObject(data) {
    return JSON.parse(JSON.stringify(data));
  },
  async checkParent(emailDomainName) {
    const parentDet = await db.DynamicParentCompany.findOne({ where: { emailDomainName } });
    if (parentDet) {
      return parentDet;
    }
    return null;
  },
  async createParentCompany(parentElementId) {
    const newParentCompanyDet = await ParentCompany.findOne({
      where: { id: parentElementId },
    });
    const parentCompanyDet = await this.convertObject(newParentCompanyDet);
    const parentDet = parentCompanyDet;
    parentDet.publicSchemaId = parentDet.id;
    delete parentDet.id;
    const result = await db.DynamicParentCompany.create(parentDet);
    return result;
  },
  async createProject(projectList, index, newUser, enterPriseValue, password) {
    const currentProject = projectList[index];
    const element = await this.convertObject(currentProject);
    const createdBy = await this.getCreatedBy(element.createdBy);
    element.createdBy = createdBy;
    const newParentCompanyDet = await ParentCompany.findOne({
      where: { id: element.ParentCompanyId },
    });
    const parentDet = await this.checkParent(newParentCompanyDet.emailDomainName);
    let newParent;
    if (parentDet !== null && parentDet !== undefined) {
      newParent = parentDet;
    } else {
      newParent = await this.createParentCompany(element.ParentCompanyId);
    }
    element.ParentCompanyId = newParent.id;
    element.publicSchemaId = element.id;
    const existDynamicProject = await db.DynamicProject.findOne({
      where: { publicSchemaId: element.id },
    });
    delete element.id;
    let newProject;
    if (!existDynamicProject) {
      newProject = await db.DynamicProject.createInstance(element);
    } else {
      newProject = existDynamicProject;
    }
    const condition = { ProjectId: element.publicSchemaId };
    const memberList = await Member.findAll({
      where: condition,
    });
    await this.createMember(
      memberList,
      0,
      newParent,
      newUser,
      newProject,
      createdBy,
      enterPriseValue,
    );

    const companyList = await Company.findAll({
      where: condition,
    });
    if (companyList.length > 0) {
      await this.createCompany(companyList, 0, newParent, newUser, newProject);
    }
    const gateList = await Gates.findAll({
      where: condition,
    });
    if (gateList.length > 0) {
      await this.createGate(gateList, 0, newProject);
    }
    const equipmentList = await Equipments.findAll({
      where: condition,
    });
    if (equipmentList.length > 0) {
      await this.createEquipment(equipmentList, 0, newProject);
    }

    const deliverWorkList = await DeliverDefineWork.findAll({
      where: condition,
    });
    if (deliverWorkList.length > 0) {
      await this.createDeliveryWork(deliverWorkList, 0, newProject);
    }
    const deliveryRequestList = await DeliveryRequest.findAll({
      where: condition,
    });
    if (deliveryRequestList.length > 0) {
      await this.createDeliveryRequest(deliveryRequestList, 0, newProject, newUser);
    }
    const companyDefineList = await CompanyDefine.findAll({
      where: condition,
    });
    if (companyDefineList.length > 0) {
      await this.createCompanyDefine(companyDefineList, 0, newProject);
    }
    if (index < projectList.length - 1) {
      this.createProject(projectList, index + 1, newUser, enterPriseValue, password);
    } else {
      await this.EnterpriseUpdate(enterPriseValue, newUser, password);
      return { status: true };
    }
  },
  async createCompanyDefine(companyDefineList, index, deliveryData, newProject) {
    const element = companyDefineList[index];
    element.publicSchemaId = element.id;
    element.DeliveryRequestId = deliveryData.id;
    const existGate = await db.DynamicCompanyDefine.findOne({
      where: {
        publicSchemaId: element.id,
      },
    });
    if (!existGate) {
      const convObj = await this.convertObject(element);
      const data = await db.DynamicDeliverDefineWork.findOne({
        where: { publicSchemaId: convObj.DeliverDefineWorkId },
      });
      convObj.DeliverDefineWorkId = data.id;
      delete convObj.id;
      await db.DynamicCompanyDefine.create(convObj);
    }
    if (index < companyDefineList.length - 1) {
      this.createCompanyDefine(companyDefineList, index + 1, deliveryData, newProject);
    } else {
      return { status: true };
    }
  },
  async createDeliveryRequest(deliveryList, index, newProject, newUser) {
    const temp = deliveryList[index];
    const element = await this.convertObject(temp);
    element.ProjectId = newProject.id;
    element.approvedBy = await this.getCreatedBy(element.approvedBy);
    element.createdBy = await this.getCreatedBy(element.createdBy);
    let existDelivery = await db.DynamicDeliveryRequest.findOne({
      where: {
        publicSchemaId: element.id,
      },
    });
    if (!existDelivery) {
      element.publicSchemaId = element.id;
      const convObj = await this.convertObject(element);
      delete convObj.id;
      existDelivery = await db.DynamicDeliveryRequest.create(convObj);
    }
    const condition = {
      where: {
        DeliveryRequestId: element.id,
      },
    };
    const deliverCondition = {
      where: {
        DeliveryId: element.id,
      },
    };
    const personList = await DeliveryPerson.findAll(deliverCondition);
    if (personList.length > 0) {
      await this.createPerson(personList, 0, existDelivery, newProject);
    }
    const historyList = await DeliverHistory.findAll(condition);
    if (historyList.length > 0) {
      await this.createHistory(historyList, 0, existDelivery, newProject);
    }
    const gateList = await DeliverGate.findAll(deliverCondition);
    if (gateList.length > 0) {
      await this.createDeliverGate(gateList, 0, existDelivery, newProject);
    }
    const equipmentList = await DeliverEquipment.findAll(deliverCondition);
    if (equipmentList.length > 0) {
      await this.createDeliverEquipment(equipmentList, 0, existDelivery, newProject);
    }
    const companyList = await DeliverCompany.findAll(deliverCondition);
    if (companyList.length > 0) {
      await this.createDeliverCompany(companyList, 0, existDelivery, newProject);
    }
    const defineList = await DeliverDefine.findAll(deliverCondition);
    if (defineList.length > 0) {
      await this.createDeliverDefine(defineList, 0, existDelivery, newProject);
    }
    const commentList = await DeliverComment.findAll(condition);
    if (commentList.length > 0) {
      await this.createDeliverComment(commentList, 0, existDelivery, newProject);
    }
    const attachementList = await DeliverAttachement.findAll(condition);
    if (attachementList.length > 0) {
      await this.createDeliverAttachment(attachementList, 0, existDelivery, newProject);
    }
    const notList = await Notification.findAll(condition);
    if (notList.length > 0) {
      await this.createNotification(notList, 0, existDelivery, newProject, newUser);
    }
    const voidLists = await VoidList.findAll(condition);
    if (voidLists.length > 0) {
      await this.createVoidList(voidLists, 0, existDelivery, newProject);
    }
    if (index < deliveryList.length - 1) {
      this.createDeliveryRequest(deliveryList, index + 1, newProject, newUser);
    } else {
      return { status: true };
    }
  },
  async createNotification(notList, index, deliveryData, newProject, newUser) {
    const element = notList[index];
    element.publicSchemaId = element.id;
    const { ProjectId } = element;
    element.DeliveryRequestId = deliveryData.id;
    const existGate = await db.DynamicNotification.findOne({
      where: {
        publicSchemaId: element.id,
      },
    });
    if (!existGate) {
      const convObj = await this.convertObject(element);
      convObj.MemberId = await this.createMemberHistory(convObj.MemberId, newProject);
      convObj.ProjectId = newProject.id;
      delete convObj.id;
      const newNotification = await db.DynamicNotification.create(convObj);
      await this.createDynamicNotification(newNotification, newProject, ProjectId, newUser);
    }
    if (index < notList.length - 1) {
      await this.createNotification(notList, index + 1, deliveryData, newProject, newUser);
    } else {
      return { status: true };
    }
  },
  async createDynamicNotification(newNotification, newProject, ProjectId) {
    const notNewList = await DeliveryPersonNotification.findAll({ where: { ProjectId } });
    const notList = await this.convertObject(notNewList);
    notList.forEach(async (newData) => {
      const element = await this.convertObject(newData);
      const memberData = db.DynamicMember.findOne({ where: { publicSchemaId: element.MemberId } });
      element.ProjectId = newProject.id;
      element.NotificationId = newNotification.id;
      element.MemberId = memberData.id;
      delete element.id;
      await db.DynamicDeliveryPersonNotification.create(element);
    });
  },
  async createVoidList(voidDataList, index, deliveryData, newProject) {
    const element = voidDataList[index];
    element.publicSchemaId = element.id;
    element.DeliveryRequestId = deliveryData.id;
    const existGate = await db.DynamicVoidList.findOne({
      where: {
        publicSchemaId: element.id,
      },
    });
    if (!existGate) {
      const convObj = await this.convertObject(element);
      convObj.MemberId = await this.createMemberHistory(convObj.MemberId, newProject);
      delete convObj.id;
      await db.DynamicVoidList.create(convObj);
    }
    if (index < voidDataList.length - 1) {
      this.createVoidList(voidDataList, index + 1, deliveryData, newProject);
    } else {
      return { status: true };
    }
  },
  async createDeliverComment(commentList, index, deliveryData, newProject) {
    const element = commentList[index];
    element.publicSchemaId = element.id;
    element.DeliveryRequestId = deliveryData.id;
    const existGate = await db.DynamicDeliverComment.findOne({
      where: {
        publicSchemaId: element.id,
      },
    });
    if (!existGate) {
      const convObj = await this.convertObject(element);
      convObj.MemberId = await this.createMemberHistory(convObj.MemberId, newProject);
      delete convObj.id;
      await db.DynamicDeliverComment.create(convObj);
    }
    if (index < commentList.length - 1) {
      this.createDeliverComment(commentList, index + 1, deliveryData, newProject);
    } else {
      return { status: true };
    }
  },
  async createDeliverAttachment(attachementList, index, deliveryData, newProject) {
    const element = attachementList[index];
    element.publicSchemaId = element.id;
    element.DeliveryRequestId = deliveryData.id;
    const existGate = await db.DynamicDeliverAttachement.findOne({
      where: {
        publicSchemaId: element.id,
      },
    });
    if (!existGate) {
      const convObj = await this.convertObject(element);
      delete convObj.id;
      await db.DynamicDeliverAttachement.create(convObj);
    }
    if (index < attachementList.length - 1) {
      this.createDeliverAttachment(attachementList, index + 1, deliveryData, newProject);
    } else {
      return { status: true };
    }
  },
  async createDeliverGate(gateList, index, deliveryData, newProject) {
    const element = gateList[index];
    element.publicSchemaId = element.id;
    element.DeliveryId = deliveryData.id;
    const existGate = await db.DynamicDeliverGate.findOne({
      where: {
        publicSchemaId: element.id,
      },
    });
    if (!existGate) {
      const convObj = await this.convertObject(element);
      const gateData = await db.DynamicGates.findOne({ where: { publicSchemaId: element.GateId } });
      convObj.GateId = gateData.id;
      delete convObj.id;
      await db.DynamicDeliverGate.create(convObj);
    }
    if (index < gateList.length - 1) {
      this.createDeliverGate(gateList, index + 1, deliveryData, newProject);
    } else {
      return { status: true };
    }
  },
  async createDeliverDefine(defineList, index, deliveryData, newProject) {
    try {
      const element = defineList[index];
      element.publicSchemaId = element.id;
      element.DeliveryId = deliveryData.id;
      const existDefine = await db.DynamicDeliverDefine.findOne({
        where: {
          publicSchemaId: element.id,
        },
      });
      if (!existDefine) {
        const convObj = await this.convertObject(element);
        let defineData = await db.DynamicDeliverDefineWork.findOne({
          where: { publicSchemaId: element.DeliverDefineWorkId },
        });
        if (!defineData) {
          const existNewDefine = await DeliverDefineWork.findOne({
            where: { id: element.DeliverDefineWorkId },
          });
          const localConv = await this.convertObject(existNewDefine);
          localConv.publicSchemaId = localConv.id;
          delete localConv.id;
          defineData = await db.DynamicDeliverDefineWork.create(localConv);
        }
        convObj.DeliverDefineWorkId = defineData.id;
        delete convObj.id;
        await db.DynamicDeliverDefine.create(convObj);
      }
      if (index < defineList.length - 1) {
        this.createDeliverDefine(defineList, index + 1, deliveryData, newProject);
      } else {
        return { status: true };
      }
    } catch (e) {
      return { status: false };
    }
  },
  async createDeliverCompany(companyList, index, deliveryData, newProject) {
    const element = companyList[index];
    element.publicSchemaId = element.id;
    element.DeliveryId = deliveryData.id;
    const existGate = await db.DynamicDeliverCompany.findOne({
      where: {
        publicSchemaId: element.id,
      },
    });
    if (!existGate) {
      const convObj = await this.convertObject(element);
      const companyData = await db.DynamicCompany.findOne({
        where: { publicSchemaId: element.CompanyId },
      });
      convObj.CompanyId = companyData.id;
      delete convObj.id;
      await db.DynamicDeliverCompany.create(convObj);
    }
    if (index < companyList.length - 1) {
      this.createDeliverCompany(companyList, index + 1, deliveryData, newProject);
    } else {
      return { status: true };
    }
  },
  async createDeliverEquipment(equipmentList, index, deliveryData, newProject) {
    const element = equipmentList[index];
    element.publicSchemaId = element.id;
    element.DeliveryId = deliveryData.id;
    const existEquipment = await db.DynamicDeliverEquipment.findOne({
      where: {
        publicSchemaId: element.id,
      },
    });
    if (!existEquipment) {
      const convObj = await this.convertObject(element);
      const equData = await db.DynamicEquipments.findOne({
        where: { publicSchemaId: element.EquipmentId },
      });
      convObj.EquipmentId = equData.id;
      delete convObj.id;
      await db.DynamicDeliverEquipment.create(convObj);
    }
    if (index < equipmentList.length - 1) {
      this.createDeliverEquipment(equipmentList, index + 1, deliveryData, newProject);
    } else {
      return { status: true };
    }
  },
  async createPerson(personList, index, deliveryData, newProject) {
    const element = personList[index];
    element.publicSchemaId = element.id;
    element.DeliveryId = deliveryData.id;
    const existPerson = await db.DynamicDeliveryPerson.findOne({
      where: {
        publicSchemaId: element.id,
      },
    });
    if (!existPerson) {
      const convObj = await this.convertObject(element);
      convObj.MemberId = await this.createMemberHistory(convObj.MemberId, newProject);
      delete convObj.id;
      await db.DynamicDeliveryPerson.create(convObj);
    }
    if (index < personList.length - 1) {
      this.createPerson(personList, index + 1, deliveryData, newProject);
    } else {
      return { status: true };
    }
  },
  async createHistory(historyList, index, deliveryData, newProject) {
    try {
      const element = historyList[index];
      element.publicSchemaId = element.id;
      element.DeliveryRequestId = deliveryData.id;
      const existPerson = await db.DynamicDeliverHistory.findOne({
        where: {
          publicSchemaId: element.id,
        },
      });
      if (!existPerson) {
        const convObj = await this.convertObject(element);
        convObj.MemberId = await this.createMemberHistory(convObj.MemberId, newProject);
        delete convObj.id;
        await db.DynamicDeliverHistory.create(convObj);
      }
      if (index < historyList.length - 1) {
        this.createHistory(historyList, index + 1, deliveryData, newProject);
      } else {
        return { status: true };
      }
    } catch (e) {
      return { status: false };
    }
  },
  async getCreatedBy(createdBy) {
    try {
      if (!createdBy) {
        return null;
      }
      let publicUser = await User.findOne({ where: { id: createdBy } });
      if (!publicUser) {
        const memberDet = await Member.findOne({ where: { id: createdBy } });
        publicUser = await User.findOne({ where: { id: memberDet.UserId } });
      }
      const existUser = await db.DynamicUser.findOne({ where: { email: publicUser.email } });
      let createdNew;

      if (existUser) {
        createdNew = existUser;
      } else {
        const convObj = await this.convertObject(publicUser);
        convObj.publicSchemaId = convObj.id;
        const password = generatePassword();
        convObj.password = password;
        if (!convObj.isAccount) {
          convObj.isAccount = false;
        }
        delete convObj.id;
        createdNew = await db.DynamicUser.createInstance(convObj);
        if (convObj.userType !== 'super admin' && convObj.userType !== 'folloit admin') {
          dynamicUserData.push({ email: convObj.email, firstName: convObj.firstName, password });
        }
      }
      return createdNew.id;
    } catch (e) {
      return e;
    }
  },
  async createNewEquipment(equElement, newProject) {
    const element = equElement;
    const newEquipment = await db.DynamicEquipments.findOne({
      where: { publicSchemaId: element.id },
    });
    if (!newEquipment) {
      delete element.id;
      element.ProjectId = newProject.id;
      const convObj = await this.convertObject(element);
      await db.DynamicEquipments.create(convObj);
    }
  },
  async createEquipment(equipmentList, index, newProject) {
    const convObj = equipmentList[index];
    const element = await this.convertObject(convObj);
    element.publicSchemaId = element.id;
    const createdBy = await this.getCreatedBy(element.createdBy);
    element.createdBy = createdBy;
    await this.createNewEquipment(element, newProject);
    if (index < equipmentList.length - 1) {
      this.createEquipment(equipmentList, index + 1, newProject);
    } else {
      return { status: true };
    }
  },
  async createDeliveryWork(deliverWorkList, index, newProject) {
    const element = deliverWorkList[index];
    const newDeliver = await db.DynamicDeliverDefineWork.findOne({
      where: { DFOW: element.DFOW },
    });
    if (!newDeliver) {
      element.ProjectId = newProject.id;
      element.publicSchemaId = element.id;
      const convObj = await this.convertObject(element);
      delete convObj.id;
      await db.DynamicDeliverDefineWork.createInstance(convObj);
    }
    if (index < deliverWorkList.length - 1) {
      this.createDeliveryWork(deliverWorkList, index + 1, newProject);
    } else {
      return { status: true };
    }
  },
  async createNewGate(newElement, newProject) {
    const element = newElement;
    const newGate = await db.DynamicGates.findOne({
      where: {
        publicSchemaId: element.id,
      },
    });
    if (!newGate) {
      element.ProjectId = newProject.id;
      const existElement = await this.convertObject(element);
      delete existElement.id;
      await db.DynamicGates.create(existElement);
    }
    return true;
  },
  async createGate(gateList, index, newProject) {
    const element = gateList[index];
    const createdBy = await this.getCreatedBy(element.createdBy);
    element.createdBy = createdBy;
    element.publicSchemaId = element.id;
    await this.createNewGate(element, newProject);
    if (index < gateList.length - 1) {
      this.createGate(gateList, index + 1, newProject);
    } else {
      return { status: true };
    }
  },
  async createCompany(companyList, index, newParent, newUser, newProject) {
    const element = companyList[index];
    const companyDet = element;
    companyDet.publicSchemaId = companyDet.id;
    const convObj = await this.convertObject(companyDet);
    const newResult = await this.getCreatedBy(convObj.createdBy);
    element.createdBy = newResult;
    await this.createNewCompany(element.id, newParent, element.createdBy, newProject);
    if (index < companyList.length - 1) {
      this.createCompany(companyList, index + 1, newParent, newUser, newProject);
    } else {
      return { status: true };
    }
  },
  async createNewCompany(CompanyId, newParent, createdBy, newProject) {
    const companyData = await Company.findOne({
      where: { id: CompanyId },
    });
    const existCompany = await db.DynamicCompany.findOne({
      where: {
        publicSchemaId: CompanyId,
      },
    });
    if (existCompany) {
      return existCompany;
    }
    const companyDet = await this.convertObject(companyData);
    companyDet.publicSchemaId = companyDet.id;
    delete companyDet.id;
    companyDet.ParentCompanyId = newParent.id;
    companyDet.createdBy = createdBy;
    companyDet.ProjectId = newProject.id;
    const convertCompany = await this.convertObject(companyDet);
    delete convertCompany.id;
    const newCompany = await db.DynamicCompany.createInstance(convertCompany);
    return newCompany;
  },
  async createUser(element) {
    let id = element.UserId;
    if (!id) {
      id = element.id;
    }
    const publicUser = await User.findOne({ where: { id } });
    const existUser = await db.DynamicUser.findOne({ where: { email: publicUser.email } });
    let UserId;
    if (existUser) {
      UserId = existUser;
    } else {
      const convObj = await this.convertObject(publicUser);
      convObj.publicSchemaId = convObj.id;
      const password = generatePassword();
      convObj.password = password;
      if (!convObj.isAccount) {
        convObj.isAccount = false;
      }
      delete convObj.id;
      UserId = await db.DynamicUser.createInstance(convObj);
      if (convObj.userType !== 'super admin' && convObj.userType !== 'folloit admin') {
        dynamicUserData.push({ email: convObj.email, firstName: convObj.firstName, password });
      }
    }
    return UserId;
  },
  async createNewMember(UserId, newProject, element) {
    const objElement = await this.convertObject(element);
    // if (element.publicSchemaId) {
    let existMember = await db.DynamicMember.findOne({
      where: { UserId: objElement.UserId, ProjectId: newProject.id, isDeleted: false },
    });
    // }
    if (!existMember) {
      objElement.publicSchemaId = objElement.id;
      delete objElement.id;
      existMember = await db.DynamicMember.findOne({
        where: { publicSchemaId: objElement.publicSchemaId },
      });
      if (!existMember) {
        existMember = await db.DynamicMember.createInstance(objElement);
      }
    }
    return existMember;
  },
  async createMemberHistory(MemberId, newProject) {
    const newMemberData = await Member.findOne({ where: { id: MemberId } });
    const memberData = await this.convertObject(newMemberData);
    let newMember = await db.DynamicMember.findOne({
      where: { publicSchemaId: memberData.id },
    });
    if (!newMember) {
      newMember = await this.createNewMember('', newProject, memberData);
    }
    return newMember.id;
  },
  async createMember(
    memberList,
    index,
    newParent,
    newUser,
    newProject,
    createdBy,
    enterPriseValue,
  ) {
    if (memberList[index]) {
      const element = await this.convertObject(memberList[index]);
      let newCompany;
      if (element.CompanyId) {
        newCompany = await this.createNewCompany(
          element.CompanyId,
          newParent,
          createdBy,
          newProject,
        );
      }
      const oldProjectId = element.ProjectId;
      if (newCompany) {
        element.CompanyId = newCompany.id;
      }
      element.createdBy = createdBy;
      element.ParentCompanyId = newParent.id;
      element.ProjectId = newProject.id;
      const newUserData = await this.createUser(element);
      element.UserId = newUserData.id;
      element.ProjectId = newProject.id;
      const existRole = await db.DynamicRole.findOne({ where: { roleName: 'Account Admin' } });
      const existRoleId = element.RoleId;
      const existFirstName = element.firstName;
      const RoleId = existRole.id;
      if (newUserData.id === newUser.id) {
        element.RoleId = RoleId;
        element.firstName = newUser.firstName;
      }
      const currentId = element.id;
      const enterpriseMember = element;
      const newOne = enterpriseMember;
      newOne.RoleId = RoleId;
      newOne.UserId = newUser.id;
      const existEnterprise = await db.DynamicMember.findOne({
        where: { UserId: newUser.id, ProjectId: newProject.id },
      });
      if (!existEnterprise) {
        const oldUserData = await User.findOne({ where: { email: newUser.email } });
        const userData = await this.convertObject(oldUserData);
        let oldMemberData = await Member.findOne({
          where: { UserId: userData.id, ProjectId: oldProjectId },
        });
        let finalMemberData = await this.convertObject(oldMemberData);
        const oldMemberDetailData = await this.convertObject(memberList[index]);
        if (!finalMemberData) {
          const oldData = {
            UserId: userData.id,
            ProjectId: oldProjectId,
            phoneCode: userData.phoneCode,
            phoneNumber: userData.phoneNumber,
            password: userData.password,
            CompanyId: oldMemberDetailData.CompanyId,
            ParentCompanyId: oldMemberDetailData.ParentCompanyId,
            createdBy: oldMemberDetailData.createdBy,
            firstName: oldMemberDetailData.firstName,
            isAccount: true,
            EnterpriseId: enterPriseValue.id,
            RoleId: 1,
          };
          oldMemberData = await Member.create(oldData);
          finalMemberData = await this.convertObject(oldMemberData);
        }
        newOne.id = finalMemberData.id;
        await this.createNewMember(newUser.id, newProject, newOne);
      }
      const existMember = await db.DynamicMember.findOne({
        where: { UserId: newUserData.id, ProjectId: newProject.id },
      });
      if (!existMember) {
        element.UserId = newUserData.id;
        element.RoleId = existRoleId;
        element.firstName = existFirstName;
        element.id = currentId;
        await this.createNewMember(newUserData.id, newProject, element);
      }
    }
    if (index < memberList.length - 1) {
      this.createMember(
        memberList,
        index + 1,
        newParent,
        newUser,
        newProject,
        createdBy,
        enterPriseValue,
      );
    } else {
      return { status: true };
    }
  },
};

module.exports = accountCornService;
