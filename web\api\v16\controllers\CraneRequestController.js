const status = require('http-status');
const { craneRequestService } = require('../services');
const { Member, ProjectSettings } = require('../models');

const CraneRequestController = {
  async createCraneRequest(req, res, next) {
    try {
      await craneRequestService.newCraneRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Crane Booking Created Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async editCraneRequest(req, res, next) {
    try {
      await craneRequestService.editCraneRequest(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Crane Booking Updated Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getCraneRequestList(req, res, next) {
    try {
      await craneRequestService.listCraneRequest(req, async (response, error) => {
        if (error) {
          next(error);
        } else {
          let response1;
          if (req.params.ProjectId !== '') {
            response1 = await ProjectSettings.getCalendarStatusColor(req.params.ProjectId);
          }
          craneRequestService.lastCraneRequest(req, (lastDetail, error1) => {
            if (!error1) {
              res.status(status.OK).json({
                message: 'Crane Booking listed Successfully.',
                data: response,
                lastId: lastDetail,
                statusData: response1,
              });
            } else {
              next(error1);
            }
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getCraneRequestData(req, res, next) {
    try {
      craneRequestService.getCraneRequestData(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Crane Booking listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async updateCraneRequestStatus(req, res, next) {
    try {
      await craneRequestService.updateCraneRequestStatus(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          const statusMessage = req.body.status;
          res.status(status.OK).json({
            message: `${statusMessage} Successfully.`,
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getLastCraneRequestId(req, res, next) {
    try {
      await craneRequestService.lastCraneRequest(req, (lastDetail, error1) => {
        if (!error1) {
          res.status(status.OK).json({
            message: 'Delivery Booking Last Id Viewed Successfully.',
            lastId: lastDetail,
          });
        } else {
          next(error1);
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getSingleCraneRequest(req, res, next) {
    try {
      await craneRequestService.getSingleCraneRequest(req, (response, error1) => {
        if (!error1) {
          res.status(status.OK).json({
            message: 'Crane Booking Viewed Successfully.',
            data: response,
          });
        } else {
          next(error1);
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async upcomingRequestListForMobile(req, res, next) {
    try {
      const craneRequestData = await craneRequestService.upcomingRequestListForMobile(req);
      if (craneRequestData.status === 200) {
        res.status(status.OK).json({
          message: 'Upcoming Crane Booking Viewed Successfully.',
          data: craneRequestData.data,
        });
      } else {
        res.status(status.UNPROCESSABLE_ENTITY).json({
          message: craneRequestData.msg,
          data: [],
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async upcomingRequestList(req, res, next) {
    try {
      const craneRequestData = await craneRequestService.upcomingRequestList(req);
      if (craneRequestData.status === 200) {
        res.status(status.OK).json({
          message: 'Upcoming Crane Booking Viewed Successfully.',
          data: craneRequestData.data,
        });
      } else {
        res.status(status.UNPROCESSABLE_ENTITY).json({
          message: craneRequestData.msg,
          data: [],
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async editMultipleDeliveryRequest(req, res, next) {
    try {
      const editDeliveryRequests = await craneRequestService.editMultipleDeliveryRequest(req);
      if (editDeliveryRequests) {
        res.status(status.OK).json({
          status: 200,
          message: 'Crane Booking Updated Successfully.',
        });
      } else {
        res.status(status.UNPROCESSABLE_ENTITY).json({
          status: 422,
          message: 'Cannot able to update crane booking.',
        });
      }
    } catch (e) {
      next(e);
    }
  },
};

module.exports = CraneRequestController;
