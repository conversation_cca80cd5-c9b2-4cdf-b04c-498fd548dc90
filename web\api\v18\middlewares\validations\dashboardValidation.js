const Joi = require('joi');

const dashboardValidation = {
  upcomingDelivery: {
    params: Joi.object({
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  getPADashboardData: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  getGraphDelivery: {
    params: Joi.object({
      ParentCompanyId: Joi.any(),
    }),
    body: Joi.object({
      ProjectId: Joi.any(),
    }),
  },
  getDashboardData: {
    body: Joi.object({
      ProjectId: Joi.number(),
      RoleId: Joi.number(),
      ParentCompanyId: Joi.number(),
    }),
  },
};
module.exports = dashboardValidation;
