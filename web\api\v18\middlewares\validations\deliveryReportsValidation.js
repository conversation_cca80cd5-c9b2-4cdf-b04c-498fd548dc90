const Joi = require('joi');

const deliveryReportsValidation = {
  DeliveryReports: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
      void: Joi.number().required(),
    }),
    body: Joi.object({
      companyFilter: Joi.number(),
      queuedNdr: Joi.boolean().required(),
      descriptionFilter: Joi.optional().allow(''),
      memberFilter: Joi.number(),
      gateFilter: Joi.number(),
      ParentCompanyId: Joi.number(),
      upcoming: Joi.boolean(),
      equipmentFilter: Joi.number(),
      assignedFilter: Joi.boolean(),
      startdate: Joi.optional().allow(''),
      statusFilter: Joi.optional().allow(''),
      search: Joi.optional().allow(''),
      sort: Joi.any().optional().allow('', null),
      sortByField: Joi.any().optional().allow('', null),
      pickFrom: Joi.optional().allow('', null),
      pickTo: Joi.optional().allow('', null),
      defineFilter: Joi.number(),
      enddate: Joi.optional().allow(''),
      idFilter: Joi.number(),
    }),
  },
  ScheduleReports: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
    }),
    body: Joi.object({
      reportName: Joi.string().min(3).required(),
      reportType: Joi.string()
        .valid('Heat Map', 'Delivery', 'Crane', 'Concrete', 'Weekly Calendar')
        .required(),
      outputFormat: Joi.string().valid('PDF', 'EXCEL', 'CSV').required(),
      runReportAt: Joi.string().required(),
      recurrence: Joi.string()
        .valid('Does Not Repeat', 'Daily', 'Weekly', 'Monthly', 'Yearly')
        .required(),
      repeatEvery: Joi.alternatives().conditional('recurrence', [
        { is: 'Does Not Repeat', then: Joi.optional().allow('') },
        {
          is: 'Daily',
          then: Joi.object().keys({
            day: Joi.number().required(),
            recurrenceEndDate: Joi.string().required(),
          }),
        },
        {
          is: 'Weekly',
          then: Joi.object().keys({
            day: Joi.number().required(),
            recurrenceEndDate: Joi.string().required(),
            specificDays: Joi.array()
              .min(1)
              .max(7)
              .items(Joi.string().valid('SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'))
              .required(),
          }),
        },
        {
          is: 'Monthly',
          then: Joi.object().keys({
            day: Joi.number().required(),
            recurrenceEndDate: Joi.string().required(),
            options: Joi.string().valid('on_day', 'on_the').required(),
            specificDays: Joi.alternatives().conditional('options', [
              { is: 'on_day', then: Joi.object().keys({ day: Joi.number().required() }) },
              {
                is: 'on_the',
                then: Joi.object().keys({
                  order: Joi.string()
                    .valid('first', 'second', 'third', 'fourth', 'last')
                    .required(),
                  specificDay: Joi.string()
                    .valid('SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT')
                    .required(),
                }),
              },
            ]),
          }),
        },
        {
          is: 'Yearly',
          then: Joi.object().keys({
            recurrenceEndDate: Joi.string().required(),
            options: Joi.string().valid('on_day', 'on_the').required(),
            specificDays: Joi.alternatives().conditional('options', [
              {
                is: 'on_day',
                then: Joi.object().keys({
                  day: Joi.number().required(),
                  month: Joi.number().required(),
                }),
              },
              {
                is: 'on_the',
                then: Joi.object().keys({
                  order: Joi.string()
                    .valid('first', 'second', 'third', 'fourth', 'last')
                    .required(),
                  specificDay: Joi.string()
                    .valid('SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT')
                    .required(),
                  month: Joi.number().required(),
                }),
              },
            ]),
          }),
          otherwise: Joi.optional().allow(''),
        },
      ]),
      sendTo: Joi.array().items(Joi.string().email()).required(),
      subject: Joi.string(),
      message: Joi.string(),
      startDate: Joi.optional().allow(''),
      endDate: Joi.optional().allow(''),
      startTime: Joi.optional().allow(''),
      endTime: Joi.optional().allow(''),
      status: Joi.optional().allow(''),
      parentCompanyId: Joi.number().required(),
      defineId: Joi.number().required(),
      companyId: Joi.number().required(),
      equipmentId: Joi.alternatives().try(Joi.string(), Joi.number()).required(),
      companyFilter: Joi.string().optional().allow(''),
      equipmentFilter: Joi.string().optional().allow(''),
      gateId: Joi.number().required(),
      memberId: Joi.number().required(),
      templateType: Joi.array()
        .items(
          Joi.object().keys({
            id: Joi.number().required(),
            name: Joi.string().required(),
          }),
        )
        .optional(),
      timezone: Joi.string().required(),
      locationFilter: Joi.string().optional().allow(''),
      descriptionFilter: Joi.string().optional().allow(''),
      orderNumberFilter: Joi.string().optional().allow(''),
      mixDesignFilter: Joi.string().optional().allow(''),
      truckspacingFilter: Joi.string().optional().allow(''),
      slumpFilter: Joi.string().optional().allow(''),
      primerFilter: Joi.string().optional().allow(''),
      quantityFilter: Joi.string().optional().allow(''),
      pickFrom: Joi.string().optional().allow(''),
      pickTo: Joi.string().optional().allow(''),
      idFilter: Joi.number().optional().allow(''),
      sort: Joi.string().optional().allow(''),
      sortByField: Joi.string().optional().allow(''),
      queuedNdr: Joi.boolean().optional(),
      selectedHeaders: Joi.array()
        .items(
          Joi.object().keys({
            isActive: Joi.boolean().required(),
            key: Joi.string().required(),
            title: Joi.string().required(),
          }),
        )
        .optional()
        .allow(''),
      dateRangeId: Joi.number().required(),
      customStartDate: Joi.string().required(),
      customEndDate: Joi.string().required(),
    }),
  },
  SavedReports: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      pageSize: Joi.number().optional(),
      pageNo: Joi.number().optional(),
      void: Joi.number().optional(),
      sortOrder: Joi.string().optional(),
    }),
    body: Joi.object({
      reportName: Joi.string().min(3).required(),
      exportType: Joi.string().valid('PDF', 'EXCEL', 'CSV').required(),
      start: Joi.string().optional().allow(''),
      end: Joi.string().optional().allow(''),
      startDate: Joi.string().optional().allow(''),
      endDate: Joi.string().optional().allow(''),
      startTime: Joi.string().optional().allow(''),
      endTime: Joi.string().optional().allow(''),
      ParentCompanyId: Joi.number().required(),
      defineFilter: Joi.alternatives().try(Joi.string(), Joi.number()).required().allow(''),
      companyFilter: Joi.alternatives().try(Joi.string(), Joi.number()).required().allow(''),
      equipmentFilter: Joi.alternatives().try(Joi.string(), Joi.number()).required().allow(''),
      gateFilter: Joi.alternatives().try(Joi.string(), Joi.number()).required().allow(''),
      statusFilter: Joi.string().optional().allow(''),
      memberFilter: Joi.alternatives().try(Joi.string(), Joi.number()).required().allow(''),
      generatedDate: Joi.string().optional().allow(''),
      templateType: Joi.array()
        .items(
          Joi.object().keys({
            id: Joi.number().required(),
            name: Joi.string().required(),
          }),
        )
        .optional(),
      timezone: Joi.string().required(),
      locationFilter: Joi.string().optional().allow(''),
      descriptionFilter: Joi.string().optional().allow(''),
      orderNumberFilter: Joi.string().optional().allow(''),
      mixDesignFilter: Joi.string().optional().allow(''),
      truckspacingFilter: Joi.string().optional().allow(''),
      slumpFilter: Joi.string().optional().allow(''),
      primerFilter: Joi.string().optional().allow(''),
      quantityFilter: Joi.string().optional().allow(''),
      pickFrom: Joi.string().optional().allow(''),
      pickTo: Joi.string().optional().allow(''),
      idFilter: Joi.number().optional().allow(''),
      sort: Joi.string().optional().allow(''),
      sortByField: Joi.string().optional().allow(''),
      queuedNdr: Joi.boolean().optional(),
      selectedHeaders: Joi.array()
        .items(
          Joi.object().keys({
            isActive: Joi.boolean().required(),
            key: Joi.string().required(),
            title: Joi.string().required(),
          }),
        )
        .optional()
        .allow(''),
      saved: Joi.boolean().optional(),
      isDST: Joi.boolean().optional().allow(''),
      currentViewMonth: Joi.string().optional().allow(''),
      eventEndTime: Joi.string().optional().allow(''),
      eventStartTime: Joi.string().optional().allow(''),
      typeFormat: Joi.string().optional().allow(''),
    }),
  },
  GetScheduleReports: {
    body: Joi.object({
      saved: Joi.boolean().optional(),
    }),
    query: Joi.object({
      ProjectId: Joi.number().required(),
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
      createdUserId: Joi.number().optional().allow('', null),
      reportName: Joi.string().optional().allow('', null),
      templateType: Joi.string().optional().allow('', null),
      lastRun: Joi.string().optional().allow('', null),
      nextRun: Joi.string().optional().allow('', null),
      timezone: Joi.string().optional().allow('', null),
      sort: Joi.string().required(),
      sortByField: Joi.string().required(),
      search: Joi.string().optional().allow('', null),
      isSaved: Joi.boolean().optional(),
    }),
  },
  HeatMapDeliveryReports: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
      void: Joi.number().required(),
      sortOrder: Joi.string().required(),
    }),
    body: Joi.object({
      // templateType: Joi.object().keys({
      //   id: Joi.number().required(),
      //   name: Joi.string().required(),
      // }),
      templateType: Joi.array()
        .min(1)
        .max(3)
        .items(
          Joi.object().keys({
            id: Joi.number().required(),
            name: Joi.string().required(),
          }),
        )
        .required(),
      startDate: Joi.string().optional().allow('', null),
      endDate: Joi.string().optional().allow('', null),
      statusFilter: Joi.string().optional().allow('', null),
      equipmentFilter: Joi.number().optional().allow('', null),
      defineFilter: Joi.number().optional().allow('', null),
      gateFilter: Joi.number().optional().allow('', null),
      memberFilter: Joi.number().optional().allow('', null),
      companyFilter: Joi.number().optional().allow('', null),
      startTime: Joi.string().optional().allow('', null),
      endTime: Joi.string().optional().allow('', null),
      ParentCompanyId: Joi.number().required(),
      timezone: Joi.string().required(),
      eventStartTime: Joi.string().optional().allow('', null),
      eventEndTime: Joi.string().optional().allow('', null),
    }),
  },
  deleteSchedulerReport: {
    query: Joi.object({
      id: Joi.number().required(),
      ProjectId: Joi.number().required(),
    }),
  },
  rerunSchedulerReport: {
    query: Joi.object({
      id: Joi.number().required(),
      ProjectId: Joi.number().required(),
    }),
  },
};
module.exports = deliveryReportsValidation;
