module.exports = (sequelize, DataTypes) => {
  const ConcreteRequestAttachment = sequelize.define(
    'ConcreteRequestAttachment',
    {
      ProjectId: DataTypes.INTEGER,
      MemberId: DataTypes.INTEGER,
      ConcreteRequestId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      attachment: DataTypes.STRING,
      filename: DataTypes.STRING,
      extension: DataTypes.STRING,
    },
    {},
  );

  ConcreteRequestAttachment.associate = (models) => {
    ConcreteRequestAttachment.belongsTo(models.ConcreteRequest);
  };

  ConcreteRequestAttachment.getAll = async (attr) => {
    const newConcreteRequestAttachement = await ConcreteRequestAttachment.findAll({
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return newConcreteRequestAttachement;
  };
  ConcreteRequestAttachment.createMultipleInstance = async (paramData) => {
    const newConcreteRequestAttachement = await ConcreteRequestAttachment.bulkCreate(paramData);
    return newConcreteRequestAttachement;
  };
  ConcreteRequestAttachment.createInstance = async (paramData) => {
    const newConcreteRequestAttachement = await ConcreteRequestAttachment.create(paramData);
    return newConcreteRequestAttachement;
  };
  return ConcreteRequestAttachment;
};
