const status = require('http-status');
const { accountCornService } = require('../services');
const { accountService } = require('../services');

const AccountCornController = {
  async createSchemas() {
    await accountCornService.createSchemas();
  },
  /**
   *
   * @param {super admin token} req
   * @param {enterprise array list,status,message} res
   * @param {return if any exception} next
   */
  async getEnterpriseAccounts(req, res, next) {
    try {
      const enterpriseAccounts = await accountService.getEnterpriseAccounts(req);
      if (enterpriseAccounts) {
        res.status(status.OK).json({
          status: 200,
          message: 'Enterprise Accounts Listed Successfully.',
          data: enterpriseAccounts,
        });
      } else {
        res.status(status.OK).json({
          status: 200,
          message: 'Enterprise Accounts Listed Successfully.',
          data: [],
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {super admin token} req
   * @param {non enterprise array list,status,message} res
   * @param {return if any exception} next
   */
  async getNonEnterpriseAccounts(req, res, next) {
    try {
      const nonEnterpriseAccounts = await accountService.getEnterpriseAccounts(req);
      if (nonEnterpriseAccounts) {
        res.status(status.OK).json({
          status: 200,
          message: 'Non Enterprise Accounts Listed Successfully.',
          data: nonEnterpriseAccounts,
        });
      } else {
        res.status(status.OK).json({
          status: 200,
          message: 'Non Enterprise Accounts Listed Successfully.',
          data: [],
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {super admin token,companyId} req
   * @param {non enterprise company project array list,status,message} res
   * @param {return if any exception} next
   */
  async getNonEnterpriseAccountProjects(req, res, next) {
    try {
      const nonEnterpriseAccountProjectList = await accountService.getNonEnterpriseAccountProjects(
        req,
      );
      if (nonEnterpriseAccountProjectList) {
        res.status(status.OK).json({
          status: 200,
          message: 'Non Enterprise Accounts Listed Successfully.',
          data: nonEnterpriseAccountProjectList,
        });
      } else {
        res.status(status.OK).json({
          status: 200,
          message: 'Non Enterprise Accounts Listed Successfully.',
          data: [],
        });
      }
    } catch (e) {
      next(e);
    }
  },
};
module.exports = AccountCornController;
