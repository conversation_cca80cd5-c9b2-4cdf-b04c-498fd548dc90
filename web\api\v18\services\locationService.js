const status = require('http-status');
const ExcelJS = require('exceljs');
const { Sequelize, Enterprise, Member, LocationNotificationPreferences } = require('../models');
let {
  Locations,
  User,
  Project,
  DeliveryRequest,
  CraneRequest,
  ConcreteRequest,
} = require('../models');
const helper = require('../helpers/domainHelper');
// const mixpanelService = require('./mixpanelService');

let publicUser;
let publicMember;
const { Op } = Sequelize;

const ApiError = require('../helpers/apiError');

const locationService = {
  async addLocation(req) {
    try {
      await this.getDynamicModel(req);
      const loginUser = req.user;
      const { query } = req;
      const payload = req.body;
      const ProjectId = +query.ProjectId;
      const ParentCompanyId = +query.ParentCompanyId;
      const memberDetails = await Member.findOne({
        where: {
          UserId: loginUser.id,
          ProjectId,
          isDeleted: false,
          isActive: true,
        },
      });
      if (!memberDetails) {
        const err = new ApiError('Member does not exist.', status.BAD_REQUEST);
        throw Error(err);
      }
      const projectDetails = await Project.getProject({ id: ProjectId });
      if (projectDetails) {
        const locationObject = {
          ProjectId,
          ParentCompanyId,
          notes: payload.notes,
          MemberId: memberDetails.MemberId,
          createdBy: loginUser.id,
          platform: query.platform,
        };
        locationObject.locationName = payload.mainCategory;
        if (payload.mainCategory) {
          locationObject.locationPath = payload.mainCategory;
          const location = await Locations.create(locationObject);
          this.setLocationNotificationPreferenceForAMember(location.id, ProjectId);
          if (location) {
            delete locationObject.locationName;
            locationObject.LocationId = location.id;
            if (payload.paths && payload.paths.length > 0) {
              payload.paths.forEach(async (element) => {
                if (element.subCategory) {
                  locationObject.locationName = element.subCategory;
                  locationObject.locationPath = `${payload.mainCategory} > ${element.subCategory}`;
                  const subLocation = await Locations.create(locationObject);
                  this.setLocationNotificationPreferenceForAMember(subLocation.id, ProjectId);
                  delete locationObject.LocationId;
                  locationObject.LocationId = subLocation.id;
                  if (element.tier && element.tier.length > 0) {
                    element.tier.forEach(async (element1) => {
                      if (element1.tier) {
                        locationObject.locationName = element1.tier;
                        locationObject.locationPath = `${payload.mainCategory} > ${element.subCategory} > ${element1.tier}`;
                        const tierLocation = await Locations.create(locationObject);
                        this.setLocationNotificationPreferenceForAMember(
                          tierLocation.id,
                          ProjectId,
                        );
                      }
                    });
                  }
                }
              });
            }
          }
          return location;
        }
      }
      const err = new ApiError('Project does not exist.', status.BAD_REQUEST);
      throw Error(err);
    } catch (e) {
      throw (null, e);
    }
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    let enterpriseValue;
    let ProjectId;
    const incomeData = inputData;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.query.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    Locations = modelObj.Locations;
    Project = modelObj.Project;
    User = modelObj.User;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return ProjectId;
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicUser = modelData.User;
    publicMember = modelData.Member;
  },
  async listLocation(req) {
    try {
      await this.getDynamicModel(req);
      const { query } = req;
      const { sort } = query;
      const { sortByField } = query;
      const { search } = query;
      const ProjectId = +query.ProjectId;
      const ParentCompanyId = +query.ParentCompanyId;
      const pageNumber = +query.pageNo;
      const pageSize = +query.pageSize;
      const offset = (pageNumber - 1) * pageSize;
      const condition = {
        ProjectId,
        ParentCompanyId,
        LocationId: null,
        isDeleted: false,
      };
      const locations = await Locations.get(condition, sort, sortByField, search);
      const result = {};
      result.count = locations.getLocations.length;
      result.rows = locations.getLocations.slice(offset, offset + pageSize);
      result.defaultLocation = locations.defaultLocation;
      return result;
    } catch (e) {
      throw (null, e);
    }
  },
  async editLocation(req) {
    try {
      await this.getDynamicModel(req);
      const loginUser = req.user;
      const { query } = req;
      const payload = req.body;
      const ProjectId = +query.ProjectId;
      const ParentCompanyId = +query.ParentCompanyId;
      const memberDetails = await Member.findOne({
        where: {
          UserId: loginUser.id,
          ProjectId,
          isDeleted: false,
          isActive: true,
        },
      });
      if (!memberDetails) {
        const err = new ApiError('Member does not exist.', status.BAD_REQUEST);
        throw Error(err);
      }
      const projectDetails = await Project.getProject({ id: ProjectId });
      if (projectDetails) {
        const locationObject = {
          ProjectId,
          ParentCompanyId,
          notes: payload.notes,
          MemberId: memberDetails.MemberId,
          createdBy: loginUser.id,
          platform: query.platform,
        };
        locationObject.locationName = payload.mainCategory;
        locationObject.locationPath = payload.mainCategory;
        const location = await Locations.update(locationObject, {
          where: {
            id: payload.id,
          },
        });
        if (location) {
          delete locationObject.locationName;
          locationObject.LocationId = payload.id;
          if (payload.paths && payload.paths.length > 0) {
            payload.paths.forEach(async (element) => {
              locationObject.locationName = element.subCategory;
              if (element.id && element.subCategory === '') {
                await Locations.update(
                  {
                    isDeleted: true,
                  },
                  {
                    where: {
                      id: element.id,
                    },
                  },
                );
                await Locations.update(
                  {
                    isDeleted: true,
                  },
                  {
                    where: {
                      LocationId: element.id,
                    },
                  },
                );
                await LocationNotificationPreferences.update(
                  { isDeleted: true },
                  { where: { LocationId: element.id } },
                );
              } else if (
                (element.id && element.subCategory) ||
                (element.id === null && element.subCategory)
              ) {
                let subLocationId;
                locationObject.locationPath = `${payload.mainCategory} > ${element.subCategory}`;
                if (element.id === null) {
                  const subLocation = await Locations.create(locationObject);
                  this.setLocationNotificationPreferenceForAMember(subLocation.id, ProjectId);
                  subLocationId = subLocation.id;
                } else if (element.id && element.isDeleted === 0) {
                  subLocationId = element.id;
                  await Locations.update(locationObject, {
                    where: {
                      id: element.id,
                    },
                  });
                } else if (element.id && element.isDeleted === 1) {
                  await Locations.update(
                    {
                      isDeleted: true,
                    },
                    {
                      where: {
                        id: element.id,
                      },
                    },
                  );
                  await LocationNotificationPreferences.update(
                    { isDeleted: true },
                    { where: { LocationId: element.id } },
                  );
                }
                delete locationObject.LocationId;
                locationObject.LocationId = subLocationId;
                if (element.tier && element.tier.length > 0) {
                  element.tier.forEach(async (element1) => {
                    const tierObject = element1;
                    // locationObject.locationName = tierObject.tier;
                    if (tierObject.tier) {
                      locationObject.locationName = tierObject.tier;
                      locationObject.locationPath = `${payload.mainCategory} > ${element.subCategory} > ${element1.tier}`;
                      if (!tierObject.tier && tierObject.isDeleted === 0) {
                        delete tierObject.isDeleted;
                        tierObject.isDeleted = 1;
                      }
                      if (tierObject.id && tierObject.tier && tierObject.isDeleted === 1) {
                        await Locations.update(
                          {
                            isDeleted: true,
                          },
                          {
                            where: {
                              id: tierObject.id,
                            },
                          },
                        );
                        await LocationNotificationPreferences.update(
                          { isDeleted: true },
                          { where: { LocationId: tierObject.id } },
                        );
                      } else if (tierObject.id && tierObject.tier && tierObject.isDeleted === 0) {
                        await Locations.update(locationObject, {
                          where: {
                            id: tierObject.id,
                          },
                        });
                      } else if (
                        tierObject.id === null &&
                        tierObject.tier &&
                        tierObject.isDeleted === 0
                      ) {
                        const tierLocation = await Locations.create(locationObject);
                        this.setLocationNotificationPreferenceForAMember(
                          tierLocation.id,
                          ProjectId,
                        );
                      }
                    }
                  });
                }
              }
            });
            return location;
          }
          return location;
        }
      }
      const err = new ApiError('Project does not exist.', status.BAD_REQUEST);
      throw Error(err);
    } catch (e) {
      throw (null, e);
    }
  },
  async deleteLocation(req) {
    try {
      const { query } = req;
      const ProjectId = +query.ProjectId;
      const ParentCompanyId = +query.ParentCompanyId;
      const condition = {
        ProjectId,
        ParentCompanyId,
        LocationId: null,
        id: query.id,
        isDeleted: false,
      };
      const projectDetails = await Project.getProject({ id: ProjectId });
      if (projectDetails) {
        const getLocation = await Locations.getOne(condition);
        const location = await Locations.update(
          {
            isDeleted: true,
          },
          {
            where: {
              id: query.id,
              ProjectId,
              ParentCompanyId,
            },
          },
        );
        await LocationNotificationPreferences.update(
          { isDeleted: true },
          { where: { LocationId: query.id } },
        );
        const subLocationsId = [];
        if (getLocation.paths && getLocation.paths.length > 0) {
          getLocation.paths.forEach((subLocationObject) => {
            subLocationsId.push(subLocationObject.id);
            if (subLocationObject.tier && subLocationObject.tier.length > 0) {
              subLocationObject.tier.forEach((tierObject) => {
                subLocationsId.push(tierObject.id);
              });
            }
          });
        }
        if (subLocationsId && subLocationsId.length > 0) {
          await Locations.update(
            { isDeleted: true },
            { where: { id: { [Op.in]: subLocationsId } } },
          );
          await LocationNotificationPreferences.update(
            { isDeleted: true },
            { where: { LocationId: { [Op.in]: subLocationsId } } },
          );
        }
        return location;
      }
    } catch (e) {
      throw (null, e);
    }
  },
  async getLocation(req) {
    try {
      const { query } = req;
      const ProjectId = +query.ProjectId;
      const ParentCompanyId = +query.ParentCompanyId;
      const condition = {
        ProjectId,
        ParentCompanyId,
        LocationId: null,
        id: query.id,
        isDeleted: false,
      };
      const location = await Locations.getOne(condition);
      return location;
    } catch (e) {
      throw (null, e);
    }
  },
  async getLocations(req) {
    try {
      const { query } = req;
      const ProjectId = +query.ProjectId;
      const ParentCompanyId = +query.ParentCompanyId;
      const condition = {
        ProjectId,
        ParentCompanyId,
        isDeleted: false,
        isActive: true,
      };
      const locations = await Locations.getLocations(condition);
      return locations;
    } catch (e) {
      throw (null, e);
    }
  },
  async bulkUploadLocation(req) {
    try {
      await this.getDynamicModel(req);
      const { file } = req;
      const { ParentCompanyId } = req.query;
      const { ProjectId } = req.query;
      const loginUser = req.user;
      const { platform } = req.query;
      const memberDetail = await Member.findOne({
        where: [
          Sequelize.and(
            {
              UserId: loginUser.id,
              ProjectId,
              isDeleted: false,
            },
            Sequelize.or({ RoleId: [1, 2, 3] }),
          ),
        ],
      });
      if (memberDetail) {
        if (file && file.originalname) {
          const projectDetails = await Project.findOne({
            where: {
              id: ProjectId,
              ParentCompanyId,
            },
            attributes: ['projectName'],
          });
          const fileName = file.originalname;
          const splitFileName = fileName.split('_');
          if (
            ProjectId !== splitFileName[1] ||
            projectDetails.dataValues.projectName !== splitFileName[0]
          ) {
            return { error: true, message: 'Please choose valid file' };
          }
          const splitValue = file.originalname.split('.');
          const extension = splitValue[splitValue.length - 1];
          if (extension === 'xlsx') {
            const newWorkbook = new ExcelJS.Workbook();
            await newWorkbook.xlsx.readFile(file.path);
            const worksheet = newWorkbook.getWorksheet('Location');
            const locationRecords = [];
            worksheet.eachRow(async (rowData, rowNumber) => {
              const singleRowData = rowData.values;
              if (rowNumber >= 3) {
                if (
                  singleRowData[1] !== undefined &&
                  singleRowData[2] !== undefined &&
                  singleRowData[3] !== undefined
                ) {
                  locationRecords.push(singleRowData);
                }
              }
            });
            const groupedData = {};
            for (let i = 0; i < locationRecords.length; i++) {
              const item = locationRecords[i];
              const category = item[1];
              const subCategory = item[2];
              const tier = item[3];
              if (!groupedData[category]) {
                groupedData[category] = { category, paths: [] };
              }
              const mainCategoryData = groupedData[category];
              if (subCategory) {
                let subCategoryData = mainCategoryData.paths.find(
                  (path) => path.subCategory === subCategory,
                );
                if (!subCategoryData) {
                  subCategoryData = { subCategory, tier: [] };
                  mainCategoryData.paths.push(subCategoryData);
                }
                if (tier) {
                  subCategoryData.tier.push({ tier });
                }
              }
            }
            const groupedArray = Object.values(groupedData);
            this.addBulkUploadLocations(
              groupedArray,
              ParentCompanyId,
              ProjectId,
              memberDetail,
              loginUser,
              platform,
            );
          } else {
            return { error: true, message: 'Please choose valid file' };
          }
        } else {
          return { error: true, message: 'Please select a file.' };
        }
      } else {
        return { error: true, message: 'Project Does not exist or you are not a valid member.' };
      }

      return { error: false };
    } catch (e) {
      throw (null, e);
    }
  },
  async addBulkUploadLocations(
    result,
    ParentCompanyId,
    ProjectId,
    memberDetails,
    loginUser,
    platform,
  ) {
    const projectDetails = await Project.getProject({ id: ProjectId });
    if (projectDetails) {
      if (result.length > 0) {
        for (let i = 0; i <= result.length - 1; i++) {
          let locationData = [];
          locationData = result[i];
          const locationObject = {
            ProjectId,
            ParentCompanyId,
            notes: null,
            MemberId: memberDetails.MemberId,
            createdBy: loginUser.id,
            platform,
          };
          if (locationData.category && locationData.category !== '') {
            locationObject.locationName = locationData.category;
            locationObject.locationPath = locationData.category;
            const location = await Locations.create(locationObject);
            this.setLocationNotificationPreferenceForAMember(location.id, ProjectId);
            if (location) {
              const locationObject2 = {
                ProjectId,
                ParentCompanyId,
                notes: null,
                MemberId: memberDetails.MemberId,
                createdBy: loginUser.id,
                platform,
              };
              // delete locationObject.locationName;
              locationObject2.LocationId = location.id;
              if (locationData.paths && locationData.paths.length > 0) {
                for (let j = 0; j <= locationData.paths.length - 1; j++) {
                  let element = [];
                  element = locationData.paths[j];
                  if (element.subCategory && element.subCategory !== '') {
                    locationObject2.locationName = element.subCategory;
                    locationObject2.locationPath = `${locationData.category} > ${element.subCategory}`;
                    const subLocation = await Locations.create(locationObject2);
                    this.setLocationNotificationPreferenceForAMember(subLocation.id, ProjectId);
                    // delete locationObject.LocationId;
                    const locationObject3 = {
                      ProjectId,
                      ParentCompanyId,
                      notes: null,
                      MemberId: memberDetails.MemberId,
                      createdBy: loginUser.id,
                      platform,
                    };
                    locationObject3.LocationId = subLocation.id;
                    if (element.tier && element.tier.length > 0) {
                      for (let k = 0; k <= element.tier.length - 1; k++) {
                        let element1 = [];
                        element1 = element.tier[k];
                        if (element1.tier !== '') {
                          locationObject3.locationName = element1.tier;
                          locationObject3.locationPath = `${locationData.category} > ${element.subCategory} > ${element1.tier}`;
                          const tierLocation = await Locations.create(locationObject3);
                          this.setLocationNotificationPreferenceForAMember(
                            tierLocation.id,
                            ProjectId,
                          );
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  },
  async getDropdownValuesForLocation(req) {
    try {
      const { params } = req;
      const ProjectId = +params.ProjectId;
      const ParentCompanyId = +params.ParentCompanyId;
      const condition = {
        ProjectId,
        ParentCompanyId,
        isDeleted: false,
        isActive: true,
      };
      const locations = await Locations.getLocations(condition);
      return locations;
    } catch (e) {
      throw (null, e);
    }
  },
  async listLocations(req) {
    try {
      await this.getDynamicModel(req);
      const { query } = req;
      const { sort } = query;
      const { sortByField } = query;
      const { search } = query;
      const loginUser = req.user;
      const ProjectId = +query.ProjectId;
      const ParentCompanyId = +query.ParentCompanyId;
      const pageNumber = +query.pageNo;
      const pageSize = +query.pageSize;
      const offset = (pageNumber - 1) * pageSize;
      const condition = {
        ProjectId,
        ParentCompanyId,
        LocationId: null,
        isDeleted: false,
      };
      const memberDetails = await Member.findOne({
        where: {
          UserId: loginUser.id,
          ProjectId,
          isDeleted: false,
          isActive: true,
        },
      });
      const locations = await Locations.getLocationList(
        memberDetails.id,
        ProjectId,
        condition,
        sort,
        sortByField,
        search,
        // pageSize,
        // offset,
      );
      const result = {};
      result.count = locations.getLocations.length;
      result.rows = locations.getLocations.slice(offset, offset + pageSize);
      result.defaultLocation = locations.defaultLocation;
      return result;
      // return locations;
    } catch (e) {
      throw (null, e);
    }
  },
  async setLocationNotificationPreference() {
    try {
      const getLocationsList = await Locations.findAll({
        where: { isDeleted: false },
      });
      const projectList = await Project.findAll({
        where: { isDeleted: false },
      });
      for (let index = 0; index < projectList.length; index += 1) {
        const project = projectList[index];
        const membersList = await Member.findAll({
          where: { isDeleted: false, ProjectId: +project.id, status: 'completed' },
        });
        for (let index1 = 0; index1 < membersList.length; index1 += 1) {
          const member = membersList[index1];
          for (let index2 = 0; index2 < getLocationsList.length; index2 += 1) {
            const location = getLocationsList[index2];
            const object = {
              MemberId: member.id,
              ProjectId: project.id,
              LocationId: location.id,
              instant: false,
              dailyDigest: false,
              ParentCompanyId: member.ParentCompanyId,
              isDeleted: false,
            };
            await LocationNotificationPreferences.createInstance(object);
          }
        }
        if (+projectList.length === index + 1) {
          return {
            error: false,
            status: 'done',
          };
        }
      }
    } catch (e) {
      throw (null, e);
    }
  },
  async setLocationNotificationPreferenceForAMember(LocationId, ProjectId) {
    try {
      // const projectList = await Project.findAll({
      //   where: { id: ProjectId, isDeleted: false },
      // });
      // for (let index = 0; index < projectList.length; index += 1) {
      // const project = projectList[index];
      const membersList = await Member.findAll({
        where: { isDeleted: false, ProjectId: +ProjectId, status: 'completed' },
      });
      for (let index1 = 0; index1 < membersList.length; index1 += 1) {
        const member = membersList[index1];
        const object = {
          MemberId: member.id,
          ProjectId,
          LocationId,
          follow: false,
          ParentCompanyId: member.ParentCompanyId,
          isDeleted: false,
        };
        const findDefaultLocationPath = await Locations.findOne({
          where: {
            ProjectId,
            isDefault: true,
            isDeleted: false,
          },
        });
        const checkMemberFollowDefaultLocationOrNot = await LocationNotificationPreferences.findOne(
          {
            where: {
              MemberId: member.id,
              ProjectId,
              follow: true,
              LocationId: findDefaultLocationPath.id,
            },
          },
        );
        if (checkMemberFollowDefaultLocationOrNot) {
          object.follow = true;
        }
        await LocationNotificationPreferences.createInstance(object);
        if (+membersList.length === index1 + 1) {
          return {
            error: false,
            status: 'done',
          };
        }
      }
      // }
    } catch (e) {
      throw (null, e);
    }
  },
  async updateMemberLocationPreference(req) {
    try {
      const memberLocationPreference = req.body.chosenMemberPreference;
      let location;
      for (let index = 0; index < memberLocationPreference.length; index += 1) {
        // eslint-disable-next-line no-await-in-loop
        location = await LocationNotificationPreferences.update(memberLocationPreference[index], {
          where: {
            id: memberLocationPreference[index].id,
          },
        });
        // if (+memberLocationPreference.length === index + 1) {
        //   return location;
        // }
      }
      const findDefaultLocationPath = await Locations.findOne({
        where: {
          ProjectId: memberLocationPreference[0].ProjectId,
          isDefault: true,
          isDeleted: false,
        },
      });
      const checkAllLocationFollowOrNot = await LocationNotificationPreferences.findOne({
        where: {
          LocationId: { [Op.ne]: findDefaultLocationPath.id },
          MemberId: memberLocationPreference[0].MemberId,
          ProjectId: memberLocationPreference[0].ProjectId,
          follow: false,
          isDeleted: false,
        },
      });
      if (!checkAllLocationFollowOrNot) {
        await LocationNotificationPreferences.update(
          { follow: true },
          {
            where: {
              MemberId: memberLocationPreference[0].MemberId,
              ProjectId: memberLocationPreference[0].ProjectId,
              LocationId: findDefaultLocationPath.id,
              isDeleted: false,
            },
          },
        );
      } else {
        await LocationNotificationPreferences.update(
          { follow: false },
          {
            where: {
              MemberId: memberLocationPreference[0].MemberId,
              ProjectId: memberLocationPreference[0].ProjectId,
              LocationId: findDefaultLocationPath.id,
              isDeleted: false,
            },
          },
        );
      }
      return location;
    } catch (e) {
      console.log(e);
      throw (null, e);
    }
  },
  async createDefaultLocationPathForExistingProject() {
    try {
      const projectList = await Project.findAll({
        where: { isDeleted: false },
      });
      for (let projectIndex = 0; projectIndex < projectList.length; projectIndex += 1) {
        const project = projectList[projectIndex];
        const memberDetails = await Member.findOne({
          where: {
            UserId: +project.createdBy,
            ProjectId: +project.id,
            isDeleted: false,
            isActive: true,
          },
        });
        if (memberDetails) {
          const locationObject = {
            ProjectId: +project.id,
            ParentCompanyId: +project.ParentCompanyId,
            notes: null,
            MemberId: memberDetails.id,
            createdBy: +project.createdBy,
            platform: 'web',
            locationName: project.projectName,
            locationPath: project.projectName,
            isDefault: true,
          };
          const location = await Locations.create(locationObject);
          const membersList = await Member.findAll({
            where: { isDeleted: false, ProjectId: +project.id, status: 'completed' },
          });
          for (let index1 = 0; index1 < membersList.length; index1 += 1) {
            const member = membersList[index1];
            const object = {
              MemberId: +member.id,
              ProjectId: +project.id,
              LocationId: location.id,
              follow: false,
              ParentCompanyId: +member.ParentCompanyId,
              isDeleted: false,
            };
            if (+member.RoleId === 2) {
              object.follow = true;
            }
            await LocationNotificationPreferences.createInstance(object);
          }
          if (+projectList.length === projectIndex + 1) {
            return {
              error: false,
              status: 'done',
            };
          }
        }
      }
    } catch (e) {
      throw (null, e);
    }
  },
  async createDefaultLocationIDForExistingBookings() {
    try {
      const projectList = await Project.findAll({
        where: {
          isDeleted: false,
        },
      });
      for (let projectIndex = 0; projectIndex < projectList.length; projectIndex += 1) {
        const project = projectList[projectIndex];
        const condition = {
          ProjectId: project.id,
          LocationId: null,
        };
        const locations = await Locations.getDefaultLocation(condition);
        const locationId = locations.id;
        await DeliveryRequest.update(
          { LocationId: +locationId },
          {
            where: condition,
          },
        );

        await CraneRequest.update(
          { LocationId: +locationId },
          {
            where: condition,
          },
        );

        await ConcreteRequest.update(
          { LocationId: +locationId },
          {
            where: condition,
          },
        );
        if (+projectList.length === projectIndex + 1) {
          return {
            error: false,
            status: 'done',
          };
        }
      }
    } catch (e) {
      throw (null, e);
    }
  },
};
module.exports = locationService;
