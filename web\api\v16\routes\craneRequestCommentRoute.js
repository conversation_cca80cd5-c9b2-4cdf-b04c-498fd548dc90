const { Router } = require('express');
const { validate } = require('express-validation');
const passportConfig = require('../config/passport');
const { craneRequestCommentController } = require('../controllers');
const { craneRequestCommentValidation } = require('../middlewares/validations');

const craneRequestCommentRoute = {
  get router() {
    const router = Router();
    router.post(
      '/create_crane_request_comment',
      validate(
        craneRequestCommentValidation.createCraneRequestComment,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      craneRequestCommentController.createCraneRequestComment,
    );
    router.get(
      '/get_crane_request_comments/:CraneRequestId/:ParentCompanyId/:ProjectId',
      validate(
        craneRequestCommentValidation.getCraneRequestComments,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      craneRequestCommentController.getCraneRequestComments,
    );
    return router;
  },
};
module.exports = craneRequestCommentRoute;
