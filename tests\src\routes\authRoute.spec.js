const faker = require('faker');
const uuid = require('uuid');
const Helper = require('../../helpers/helper');
const { User } = require('../../../src/api/v3/models');
const UserFactory = require('../../factories/user.factory');

let helper = new Helper();

let trailPlanData;
let trailPlanId;
let registerData;

const AUTH_ENDPOINT = '/api/v3/auth';
const PLAN_ENDPOINT = '/api/v3/payment/create_plan';

beforeAll((done) => {
  helper.server(done);
  // avoid jest open handle error
});
afterAll((done) => {
  helper.close(done);
  // avoid jest open handle error
});
/**
 * @test {authRoutes.js}
 */
describe(`${AUTH_ENDPOINT}`, () => {
  const REGISTER_ENDPOINT = `${AUTH_ENDPOINT}/register`;
  const LOGIN_ENDPOINT = `${AUTH_ENDPOINT}/login`;
  const FORGOT_PASSWORD_ENDPOINT = `${AUTH_ENDPOINT}/forgot_password`;
  let RESET_PASSWORD_EMAIL_EP = `${AUTH_ENDPOINT}/reset_password_email`;
  let VERIFY_BY_EMAIL = `${AUTH_ENDPOINT}/check_reset_token`;

  describe(`POST ${REGISTER_ENDPOINT}`, () => {
    beforeAll(async () => {
      const res = await helper.apiServer.post(`/api/v3/admin/admin_login`).send({
        email: '<EMAIL>',
        password: 'test@123',
      });
      helper = await new Helper(res.body.token);
    });

    describe('Create with status 201', () => {
      it('should return the user', async () => {
        const trialPlan = {
          product: {
            name: 'Trial Plan',
          },
          plan: {
            amount: 0,
            currency: 'usd',
            nickName: 'free',
            interval: 'month',
          },
        };
        trailPlanData = await helper.apiServer.post(PLAN_ENDPOINT).send(trialPlan);
        trailPlanId = trailPlanData.body.id;
        registerData = {
          basicDetails: {
            email: '<EMAIL>',
            phoneNumber: uuid.v1(),
            phoneCode: '91',
          },
          companyDetails: {
            companyName: 'mahindra',
            fullName: 'project',
            lastName: 'admin',
            scope: 'site',
            country: 'India',
            isParent: true,
            address: 'sellur',
            city: 'madurai',
            state: 'tamilnadu',
            website: 'https://mahindra.com',
            zipCode: '625002',
          },
          projectDetails: {
            projectName: 'Building444',
            projectLocation: 'Madurai',
          },
          planData: {
            id: trailPlanId,
          },
          cardDetails: {
            number: ****************,
            exp_month: 5,
            exp_year: 2021,
            cvc: 987,
          },
        };
        console.info('---------------------registerData', registerData);
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(registerData);
        const { status, body } = res;
        expect(status).toEqual(201);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Registered Successfully.');
      });
    });
    describe('Create with status different email 201', () => {
      it('should return the user different email', async () => {
        const testData = registerData;
        testData.basicDetails.email = '<EMAIL>';
        testData.basicDetails.phoneNumber = uuid.v1() + 2;
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(201);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Registered Successfully.');
      });
    });
    describe('should return an restricted email error', () => {
      it('Check User enter Restricted mail', async () => {
        const testData = registerData;
        testData.basicDetails.email = '<EMAIL>';
        testData.basicDetails.phoneNumber = '3453434323343';
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(500);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('This Email domain is restricted.');
      });
    });
    describe('should return an card details error', () => {
      it('Check card details error', async () => {
        const testData = registerData;
        testData.basicDetails.email = faker.internet.email();
        testData.basicDetails.phoneNumber = faker.phone.phoneNumber();
        testData.cardDetails = {
          number: 12345,
          exp_month: 5,
          exp_year: 2021,
          cvc: 987,
        };
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(500);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Invalid Card.');
      });
    });
    describe('Error with email', () => {
      it('should return an error if email is already take', async () => {
        const testData1 = registerData;
        testData1.basicDetails.email = '<EMAIL>';
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(testData1);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Email/Mobile Number already exist');
      });
    });
    describe('should return an error if email is not empty', () => {
      it('Email not empty', async () => {
        const testData = registerData;
        testData.basicDetails.email = '';
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].email).toEqual(
          '"basicDetails.email" is not allowed to be empty',
        );
      });
    });
    describe('Error with status 400', () => {
      it('Phone number not empty', async () => {
        const testData = registerData;
        testData.basicDetails.phoneNumber = '';
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].phoneNumber).toEqual(
          '"basicDetails.phoneNumber" is not allowed to be empty',
        );
      });
    });
    describe('should return an error if email is not valid', () => {
      it('Check valid Email', async () => {
        const testData = registerData;
        testData.basicDetails.email = 'test';
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].email).toEqual('"basicDetails.email" must be a valid email');
      });
    });
    describe('should return an error if companyname is not provided', () => {
      it('Check Empty Company Name', async () => {
        const testData = registerData;
        testData.companyDetails.companyName = '';
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].companyName).toEqual(
          '"companyDetails.companyName" is not allowed to be empty',
        );
      });
    });
    describe('should return an error if firstname is not provided', () => {
      it('Check Empty First Name', async () => {
        const testData = registerData;
        testData.companyDetails.fullName = '';
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].fullName).toEqual(
          '"companyDetails.fullName" is not allowed to be empty',
        );
      });
    });
    describe('should return an error if address is not provided', () => {
      it('Check Empty Address', async () => {
        const testData = registerData;
        testData.companyDetails.address = '';
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].address).toEqual(
          '"companyDetails.address" is not allowed to be empty',
        );
      });
    });
    describe('should return an error if country is not provided', () => {
      it('Check Empty country', async () => {
        const testData = registerData;
        testData.companyDetails.country = '';
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].country).toEqual(
          '"companyDetails.country" is not allowed to be empty',
        );
      });
    });
    describe('should return an error if city is not provided', () => {
      it('Check Empty city', async () => {
        const testData = registerData;
        testData.companyDetails.city = '';
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].city).toEqual(
          '"companyDetails.city" is not allowed to be empty',
        );
      });
    });
    describe('should return an error if state is not provided', () => {
      it('Check Empty state', async () => {
        const testData = registerData;
        testData.companyDetails.state = '';
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].state).toEqual(
          '"companyDetails.state" is not allowed to be empty',
        );
      });
    });
    describe('should return an error if zipCode is not provided', () => {
      it('Check Empty zipCode', async () => {
        const testData = registerData;
        testData.companyDetails.zipCode = '';
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].zipCode).toEqual(
          '"companyDetails.zipCode" is not allowed to be empty',
        );
      });
    });
    describe('should return an error if projectName is not provided', () => {
      it('Check Empty projec Name', async () => {
        const testData = registerData;
        testData.projectDetails.projectName = '';
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].projectName).toEqual(
          '"projectDetails.projectName" is not allowed to be empty',
        );
      });
    });
    describe('should return an error if projectLocation is not provided', () => {
      it('Check Empty Company Name', async () => {
        const testData = registerData;
        testData.projectDetails.projectLocation = '';
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].projectLocation).toEqual(
          '"projectDetails.projectLocation" is not allowed to be empty',
        );
      });
    });
    describe('should return an error if planData is not provided', () => {
      it('Check Empty planData id', async () => {
        const testData = registerData;
        testData.planData.id = '';
        const res = await helper.apiServer.post(REGISTER_ENDPOINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].id).toEqual('"planData.id" must be a number');
      });
    });
  });
  describe(`POST ${LOGIN_ENDPOINT}`, () => {
    const loginData = {
      email: '<EMAIL>',
      password: 'Test@123',
    };
    describe('Login with status 200', () => {
      it('should return the JWT token 1', async () => {
        const testData = loginData;
        const res = await helper.apiServer.post(LOGIN_ENDPOINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(200);
        expect(body).toHaveProperty('token');
      });
      describe('Error with status 400 & 404', () => {
        it('should return an error if email/password is not available', async () => {
          const testData = loginData;
          testData.password = '';
          const res = await helper.apiServer.post(LOGIN_ENDPOINT).send(testData);
          const { status, body } = res;
          expect(status).toEqual(400);
          expect(body).toHaveProperty('message');
          expect(body.message.name).toEqual('ValidationError');
          expect(body.message.details[0].password).toEqual('"password" is not allowed to be empty');
        });

        it('should return an error if email is not found on db', async () => {
          const testData = loginData;
          testData.email = '<EMAIL>';
          testData.password = 'Test@123';
          const res = await helper.apiServer.post(LOGIN_ENDPOINT).send(testData);
          const { status, body } = res;
          expect(status).toEqual(404);
          expect(body).toHaveProperty('message');
          expect(body.message).toEqual('User not found');
        });
      });
    });
  });
  describe(`POST ${FORGOT_PASSWORD_ENDPOINT}`, () => {
    beforeAll(async () => {
      await User.create(UserFactory.generate({ isVerified: true }));
    });

    describe('Forgot Password with status 200', () => {
      it('should return the success message', async () => {
        const res = await helper.apiServer.post(FORGOT_PASSWORD_ENDPOINT).send({
          email: '<EMAIL>',
        });
        const { status, body } = res;
        expect(status).toEqual(200);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Reset password email sent successfully');
      });
    });
    describe('Error with status 400 & 404', () => {
      it('should return an error if email is empty', async () => {
        const res = await helper.apiServer.post(FORGOT_PASSWORD_ENDPOINT).send({
          email: '',
        });
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.name).toEqual('ValidationError');
        expect(body.message.details[0].email).toEqual('"email" is not allowed to be empty');
      });

      it('should return an error if email is not found on db', async () => {
        const res = await helper.apiServer.post(FORGOT_PASSWORD_ENDPOINT).send({
          email: '<EMAIL>',
        });
        const { status, body } = res;
        expect(status).toEqual(404);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual("Email doesn't exist");
      });
    });
  });
  describe(`POST ${RESET_PASSWORD_EMAIL_EP}`, () => {
    beforeAll(async () => {
      let dummyUser = await User.create(UserFactory.generate({ isVerified: true }));
      await helper.apiServer.post(FORGOT_PASSWORD_ENDPOINT).send({
        email: dummyUser.email,
      });
      dummyUser = await User.getBy({ email: dummyUser.email });
      RESET_PASSWORD_EMAIL_EP = `${RESET_PASSWORD_EMAIL_EP}/${dummyUser.resetPasswordToken}`;
    });
    describe('Error with status 400 & 404', () => {
      it('should return an error if password is empty', async () => {
        const res = await helper.apiServer.post(RESET_PASSWORD_EMAIL_EP).send({
          password: '',
        });
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.name).toEqual('ValidationError');
        expect(body.message.details[0].password).toEqual('"password" is not allowed to be empty');
      });

      it('should return an error if token is invalid', async () => {
        const res = await helper.apiServer.post(`${RESET_PASSWORD_EMAIL_EP}`).send({
          password: 'passpass',
        });
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.name).toEqual('ValidationError');
        expect(body.message.details[0].password).toEqual(
          'password must be eight characters including one uppercase letter, one special character and alphanumeric characters',
        );
      });
    });
    it('should return an error if token is invalid', async () => {
      const res = await helper.apiServer.post(`${RESET_PASSWORD_EMAIL_EP}123`).send({
        password: 'Password1!',
      });
      const { status, body } = res;
      expect(status).toEqual(400);
      expect(body).toHaveProperty('message');
      expect(body.message).toEqual('Invalid Token');
    });
    describe('Reset Password with status 200', () => {
      it('should return the success message', async () => {
        const res = await helper.apiServer.post(RESET_PASSWORD_EMAIL_EP).send({
          password: 'Test@123',
        });
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Password updated Successfully.');
      });
    });
  });
  describe(`POST ${VERIFY_BY_EMAIL}`, () => {
    beforeAll(async () => {});
    describe('Verified Successfully with 200', () => {
      it('should return the success message', async () => {
        const dummyUser = await User.create(UserFactory.generate());
        VERIFY_BY_EMAIL = `${VERIFY_BY_EMAIL}/${dummyUser.registrationToken}`;
        const res = await helper.apiServer.get(`${VERIFY_BY_EMAIL}`);
        const { status, body } = res;
        expect(status).toEqual(200);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('User verified Successfully.');
      });
    });
    describe('Error with status 400 & 404', () => {
      it('should return an error if token is invalid', async () => {
        const dummyUser = await User.create(UserFactory.generate());
        const res = await helper.apiServer.get(
          `${VERIFY_BY_EMAIL}/${dummyUser.registrationToken}123`,
        );
        const { status, body } = res;
        expect(status).toEqual(404);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('API not found');
      });
      it('should return an error if token is not available', async () => {
        const res = await helper.apiServer.get(VERIFY_BY_EMAIL);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Reset Token expired');
      });
    });
  });
});
