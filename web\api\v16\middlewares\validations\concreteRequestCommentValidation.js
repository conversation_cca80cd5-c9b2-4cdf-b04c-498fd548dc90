const Joi = require('joi');

const concreteRequestCommentValidation = {
  createConcreteRequestComment: {
    body: Joi.object({
      comment: Joi.string().required(),
      ConcreteRequestId: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  getConcreteRequestComments: {
    params: Joi.object({
      ConcreteRequestId: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
};
module.exports = concreteRequestCommentValidation;
