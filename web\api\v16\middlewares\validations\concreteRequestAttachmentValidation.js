const Joi = require('joi');

const concreteRequestAttachmentValidation = {
  createConcreteRequestAttachment: {
    params: Joi.object({
      ConcreteRequestId: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
  },
  getConcreteRequestAttachment: {
    params: Joi.object({
      ConcreteRequestId: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
  },
  deleteConcreteRequestAttachment: {
    params: Joi.object({
      id: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
  },
};
module.exports = concreteRequestAttachmentValidation;
