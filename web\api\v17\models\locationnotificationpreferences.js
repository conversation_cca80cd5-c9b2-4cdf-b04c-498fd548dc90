module.exports = (sequelize, DataTypes) => {
  const LocationNotificationPreferences = sequelize.define(
    'LocationNotificationPreferences',
    {
      MemberId: DataTypes.INTEGER,
      ProjectId: DataTypes.INTEGER,
      ParentCompanyId: DataTypes.INTEGER,
      LocationId: DataTypes.INTEGER,
      follow: DataTypes.BOOLEAN,
      isDeleted: DataTypes.BOOLEAN,
    },
    {},
  );
  LocationNotificationPreferences.associate = (models) => {
    // NotificationPreference.belongsTo(models.Member);
    LocationNotificationPreferences.belongsTo(models.Locations);
    LocationNotificationPreferences.belongsTo(models.Member);
  };
  LocationNotificationPreferences.getAll = async (attr) => {
    const result = await LocationNotificationPreferences.findAll({
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return result;
  };
  LocationNotificationPreferences.createInstance = async (attr) => {
    const getItems = await LocationNotificationPreferences.create(attr);
    return getItems;
  };
  return LocationNotificationPreferences;
};
