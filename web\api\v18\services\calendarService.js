const moment = require('moment');
const Moment = require('moment');
const MomentRange = require('moment-range');

const momentRange = MomentRange.extendMoment(Moment);
const { Sequelize, Enterprise } = require('../models');
let {
  DeliveryRequest,
  Member,
  VoidList,
  DeliverCompany,
  DeliverGate,
  User,
  DeliveryPerson,
  CraneRequest,
} = require('../models');
const {
  CraneRequestCompany,
  CraneRequestResponsiblePerson,
  ConcreteRequest,
} = require('../models');
const helper = require('../helpers/domainHelper');

const { Op } = Sequelize;
let publicUser;
let publicMember;
const deliveryService = {
  async getEventNDR(inputData, done) {
    try {
      const { timezoneoffset } = inputData.headers;
      await this.getDynamicModel(inputData);
      const order = 'DESC';
      const { params } = inputData;
      const incomeData = inputData.body;
      let searchCondition = {};
      const loginUser = inputData.user;
      const memberDetails = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: params.ProjectId,
          isDeleted: false,
          isActive: true,
        }),
      });
      const startDateTime = moment(incomeData.start, 'YYYY-MM-DD')
        .startOf('day')
        .utcOffset(Number(timezoneoffset), true);
      const endDateTime = moment(incomeData.end, 'YYYY-MM-DD')
        .endOf('day')
        .utcOffset(Number(timezoneoffset), true);
      const condition = {
        ProjectId: params.ProjectId,
        isQueued: false,
        deliveryStart: {
          [Op.between]: [moment(startDateTime), moment(endDateTime)],
        },
      };
      if (memberDetails) {
        const voidDelivery = [];
        const voidList = await VoidList.findAll({
          where: {
            ProjectId: params.ProjectId,
            isDeliveryRequest: true,
            DeliveryRequestId: { [Op.ne]: null },
          },
        });
        voidList.forEach(async (element) => {
          voidDelivery.push(element.DeliveryRequestId);
        });
        if (params.void === '0' || params.void === 0) {
          condition['$DeliveryRequest.id$'] = {
            [Op.and]: [{ [Op.notIn]: voidDelivery }],
          };
        } else {
          condition['$DeliveryRequest.id$'] = {
            [Op.and]: [{ [Op.in]: voidDelivery }],
          };
        }
      }
      if (incomeData.descriptionFilter) {
        condition.description = {
          [Sequelize.Op.iLike]: `%${incomeData.descriptionFilter}%`,
        };
      }
      if (incomeData.pickFrom) {
        condition.cranePickUpLocation = {
          [Sequelize.Op.iLike]: `%${incomeData.pickFrom}%`,
        };
      }
      if (incomeData.pickTo) {
        condition.craneDropOffLocation = {
          [Sequelize.Op.iLike]: `%${incomeData.pickTo}%`,
        };
      }
      if (incomeData.equipmentFilter) {
        condition['$equipmentDetails.Equipment.id$'] = incomeData.equipmentFilter;
      }
      if (incomeData.memberFilter > 0) {
        condition['$memberDetails.Member.id$'] = +incomeData.memberFilter;
      }
      if (incomeData.locationFilter) {
        condition['$location.locationPath$'] = incomeData.locationFilter;
      }
      if (incomeData.statusFilter) {
        condition.status = incomeData.statusFilter;
      }
      if (incomeData.search) {
        const searchDefault = [
          {
            '$approverDetails.User.firstName$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$equipmentDetails.Equipment.equipmentName$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            description: {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            cranePickUpLocation: {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            craneDropOffLocation: {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$location.locationPath$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
        ];
        if (!Number.isNaN(+incomeData.search)) {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: [
                  searchDefault,
                  {
                    [Op.and]: [
                      {
                        DeliveryId: +incomeData.search,
                        isDeleted: false,
                        ProjectId: +params.ProjectId,
                      },
                    ],
                  },
                ],
              },
            ],
          };
        } else {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: searchDefault,
              },
            ],
          };
        }
      }
      const roleId = memberDetails.RoleId;
      const memberId = memberDetails.id;
      const deliveryList = await DeliveryRequest.getCalendarData(
        condition,
        roleId,
        memberId,
        searchCondition,
        order,
      );
      const result = { count: 0, rows: [] };
      if (deliveryList && deliveryList.rows.length > 0) {
        if (
          (incomeData.companyFilter && incomeData.companyFilter > 0) ||
          (incomeData.gateFilter && incomeData.gateFilter > 0) ||
          (incomeData.memberFilter && incomeData.memberFilter > 0) ||
          incomeData.dateFilter
        ) {
          this.getSearchData(
            incomeData,
            deliveryList.rows,
            0,
            [],
            memberDetails,
            timezoneoffset,
            async (checkResponse, checkError) => {
              if (!checkError) {
                result.rows = checkResponse;
                result.count = checkResponse.length;
                done(result, false);
              }
            },
          );
        } else {
          done(deliveryList, false);
        }
      } else {
        done(deliveryList, false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getDeliveryRequestWithCrane(inputData, done) {
    try {
      const { timezoneoffset } = inputData.headers;
      await this.getDynamicModel(inputData);
      const order = 'DESC';
      const { params } = inputData;
      const incomeData = inputData.body;
      const { sort } = inputData.body;
      const { sortByField } = inputData.body;
      const loginUser = inputData.user;
      const memberDetails = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: +params.ProjectId,
          isDeleted: false,
          isActive: true,
        }),
      });
      const startDateTime = moment(incomeData.start, 'YYYY-MM-DD')
        .startOf('day')
        .utcOffset(Number(timezoneoffset), true);
      const endDateTime = moment(incomeData.end, 'YYYY-MM-DD')
        .endOf('day')
        .utcOffset(Number(timezoneoffset), true);
      const DeliveryRequestCondition = {
        ProjectId: +params.ProjectId,
        isQueued: false,
        isAssociatedWithCraneRequest: true,
        deliveryStart: {
          [Op.between]: [moment(startDateTime), moment(endDateTime)],
        },
      };
      const craneDeliveryRequestCondition = {
        ProjectId: +params.ProjectId,
        craneDeliveryStart: {
          [Op.between]: [moment(startDateTime), moment(endDateTime)],
        },
      };
      if (memberDetails) {
        const voidDelivery = [];
        const voidCraneDelivery = [];
        const voidList = await VoidList.findAll({
          where: {
            ProjectId: +params.ProjectId,
            isDeliveryRequest: true,
            DeliveryRequestId: { [Op.ne]: null },
          },
        });
        voidList.forEach(async (element) => {
          voidDelivery.push(element.DeliveryRequestId);
        });
        const voidCraneRequestList = await VoidList.findAll({
          where: {
            ProjectId: +params.ProjectId,
            isDeliveryRequest: false,
            CraneRequestId: { [Op.ne]: null },
          },
        });
        voidCraneRequestList.forEach(async (element) => {
          voidCraneDelivery.push(element.CraneRequestId);
        });
        if (params.void === '0' || params.void === 0) {
          DeliveryRequestCondition['$DeliveryRequest.id$'] = {
            [Op.and]: [{ [Op.notIn]: voidDelivery }],
          };
        } else {
          DeliveryRequestCondition['$DeliveryRequest.id$'] = {
            [Op.and]: [{ [Op.in]: voidDelivery }],
          };
        }
        if (params.void === '0' || params.void === 0) {
          craneDeliveryRequestCondition['$CraneRequest.id$'] = {
            [Op.and]: [{ [Op.notIn]: voidCraneDelivery }],
          };
        } else {
          craneDeliveryRequestCondition['$CraneRequest.id$'] = {
            [Op.and]: [{ [Op.in]: voidCraneDelivery }],
          };
        }
      }
      const roleId = memberDetails.RoleId;
      const memberId = memberDetails.id;
      let craneRequestArray = [];
      let craneRequestList;
      if (
        (incomeData.gateFilter && incomeData.gateFilter > 0) ||
        (incomeData.statusFilter && incomeData.statusFilter === 'Delivered')
      ) {
        craneRequestList = [];
      } else {
        craneRequestList = await CraneRequest.getAll(
          inputData,
          roleId,
          memberId,
          craneDeliveryRequestCondition,
          incomeData.descriptionFilter,
          incomeData.startdate,
          incomeData.enddate,
          incomeData.companyFilter,
          incomeData.memberFilter,
          incomeData.equipmentFilter,
          incomeData.statusFilter,
          incomeData.idFilter,
          incomeData.pickFrom,
          incomeData.pickTo,
          incomeData.search,
          order,
          sort,
          sortByField,
          incomeData.dateFilter,
        );
      }
      if (craneRequestList.length > 0) {
        craneRequestArray = craneRequestList;
      }
      let deliveryList = [];
      if (incomeData.statusFilter && incomeData.statusFilter === 'Completed') {
        deliveryList = [];
      } else {
        deliveryList = await DeliveryRequest.getCraneAssociatedRequest(
          inputData,
          roleId,
          memberId,
          DeliveryRequestCondition,
          incomeData.descriptionFilter,
          incomeData.startdate,
          incomeData.enddate,
          incomeData.companyFilter,
          incomeData.memberFilter,
          incomeData.equipmentFilter,
          incomeData.statusFilter,
          incomeData.idFilter,
          incomeData.pickFrom,
          incomeData.pickTo,
          incomeData.search,
          incomeData.gateFilter,
          order,
          sort,
          sortByField,
          params.void,
          incomeData.dateFilter,
        );
      }
      if (
        (incomeData.companyFilter && incomeData.companyFilter > 0) ||
        (incomeData.gateFilter && incomeData.gateFilter > 0) ||
        (incomeData.memberFilter && incomeData.memberFilter > 0) ||
        incomeData.dateFilter
      ) {
        this.getSearchCraneRequestCalendarData(
          incomeData,
          craneRequestArray,
          0,
          [],
          memberDetails,
          timezoneoffset,
          async (checkResponse, checkError) => {
            craneRequestArray = checkResponse;
            if (!checkError) {
              this.getSearchData(
                incomeData,
                deliveryList,
                0,
                [],
                memberDetails,
                timezoneoffset,
                async (checkResponse1, checkError1) => {
                  if (!checkError1) {
                    if (checkResponse1.length > 0) {
                      craneRequestArray.push(...checkResponse1);
                      return done(craneRequestArray, false);
                    }
                    return done(craneRequestArray, false);
                  }
                },
              );
            } else {
              return done(null, { message: 'Something went wrong' });
            }
          },
        );
      } else {
        if (deliveryList.length > 0) {
          craneRequestArray.push(...deliveryList);
          return done(craneRequestArray, false);
        }
        return done(craneRequestArray, false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicUser = modelData.User;
    publicMember = modelData.Member;
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let domainName = inputData.user && inputData.user.domainName ? inputData.user.domainName : '';
    let enterpriseValue;
    let ProjectId;
    const incomeData = inputData;
    const ParentCompanyId =
      inputData.body && inputData.body.ParentCompanyId
        ? inputData.body && inputData.body.ParentCompanyId
        : inputData.params && inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    DeliveryRequest = modelObj.DeliveryRequest;
    Member = modelObj.Member;
    VoidList = modelObj.VoidList;
    DeliverCompany = modelObj.DeliverCompany;
    DeliverGate = modelObj.DeliverGate;
    DeliveryPerson = modelObj.DeliveryPerson;
    CraneRequest = modelObj.CraneRequest;
    User = modelObj.User;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return ProjectId;
  },
  async getSearchData(
    incomeData,
    deliveryList,
    index,
    result,
    memberDetails,
    timezoneoffset,
    done,
  ) {
    if (deliveryList.length > 0) {
      await this.getDynamicModel(incomeData);
      const elementValue = deliveryList[index];
      const element = JSON.parse(JSON.stringify(elementValue));
      const status = { companyCondition: true, gateCondition: true, memberCondition: true };
      if (incomeData.companyFilter > 0) {
        const data = await DeliverCompany.findOne({
          where: {
            DeliveryId: element.id,
            CompanyId: +incomeData.companyFilter,
            isDeleted: false,
          },
        });
        if (!data) {
          status.companyCondition = false;
        }
      }

      if (incomeData.gateFilter > 0) {
        const data = await DeliverGate.findOne({
          where: {
            DeliveryId: element.id,
            GateId: +incomeData.gateFilter,
            isDeleted: false,
          },
        });
        if (!data) {
          status.gateCondition = false;
        }
      }
      // if (incomeData.memberFilter > 0) {
      //   const data = await DeliveryPerson.findOne({
      //     where: {
      //       DeliveryId: element.id,
      //       MemberId: +incomeData.memberFilter,
      //       isDeleted: false,
      //     },
      //   });
      //   if (!data) {
      //     status.memberCondition = false;
      //   }
      // }
      if (status.companyCondition && status.gateCondition && status.memberCondition) {
        if (incomeData.dateFilter) {
          const startDateTime = moment(incomeData.dateFilter, 'YYYY-MM-DD')
            .startOf('day')
            .utcOffset(Number(timezoneoffset), true);
          const endDateTime = moment(incomeData.dateFilter, 'YYYY-MM-DD')
            .endOf('day')
            .utcOffset(Number(timezoneoffset), true);
          if (
            element &&
            moment(element.deliveryStart)
              .utcOffset(Number(timezoneoffset))
              .isBetween(startDateTime, endDateTime, undefined, '()')
          ) {
            result.push(element);
          }
        } else {
          result.push(element);
        }
      }
      if (index < deliveryList.length - 1) {
        this.getSearchData(
          incomeData,
          deliveryList,
          index + 1,
          result,
          memberDetails,
          timezoneoffset,
          (response, err) => {
            if (!err) {
              done(response, false);
            } else {
              done(null, err);
            }
          },
        );
      } else {
        done(result, false);
      }
    } else {
      done(deliveryList, false);
    }
  },
  async getSearchCraneRequestCalendarData(
    incomeData,
    deliveryList,
    index,
    result,
    memberDetails,
    timezoneoffset,
    done,
  ) {
    if (deliveryList.length > 0) {
      await this.getDynamicModel(incomeData);
      const elementValue = deliveryList[index];
      const element = JSON.parse(JSON.stringify(elementValue));
      const status = { companyCondition: true, memberCondition: true };
      if (incomeData.companyFilter > 0) {
        const data = await CraneRequestCompany.findOne({
          where: {
            CraneRequestId: element.id,
            CompanyId: +incomeData.companyFilter,
            isDeleted: false,
          },
        });
        if (!data) {
          status.companyCondition = false;
        }
      }
      if (incomeData.memberFilter > 0) {
        const data = await CraneRequestResponsiblePerson.findOne({
          where: {
            CraneRequestId: element.id,
            MemberId: incomeData.memberFilter,
            isDeleted: false,
          },
        });
        if (!data) {
          status.memberCondition = false;
        }
      }
      if (status.companyCondition && status.memberCondition) {
        result.push(element);
      }
      if (index < deliveryList.length - 1) {
        this.getSearchCraneRequestCalendarData(
          incomeData,
          deliveryList,
          index + 1,
          result,
          memberDetails,
          timezoneoffset,
          (response, err) => {
            if (!err) {
              done(response, false);
            } else {
              done(null, err);
            }
          },
        );
      } else {
        done(result, false);
      }
    } else {
      done(deliveryList, false);
    }
  },
  async getConcreteRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { timezoneoffset } = inputData.headers;
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      let order;
      const startDateTime = moment(incomeData.start, 'YYYY-MM-DD')
        .startOf('day')
        .utcOffset(Number(timezoneoffset), true);
      const endDateTime = moment(incomeData.end, 'YYYY-MM-DD')
        .endOf('day')
        .utcOffset(Number(timezoneoffset), true);
      if (params.void >= 1 && params.void <= 0) {
        done(null, { message: 'Please enter void as 1 or 0' });
      } else {
        const memberDetails = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: params.ProjectId,
            isDeleted: false,
            isActive: true,
          }),
        });
        if (memberDetails) {
          const voidConcreteDelivery = [];
          const voidConcreteRequestList = await VoidList.findAll({
            where: {
              ProjectId: params.ProjectId,
              isDeliveryRequest: false,
              ConcreteRequestId: { [Op.ne]: null },
            },
          });
          voidConcreteRequestList.forEach(async (element) => {
            voidConcreteDelivery.push(element.ConcreteRequestId);
          });
          const concreteCondition = {
            ProjectId: +params.ProjectId,
            isDeleted: false,
            concretePlacementStart: {
              [Op.between]: [moment(startDateTime), moment(endDateTime)],
            },
          };
          if (params.void === '0' || params.void === 0) {
            concreteCondition['$ConcreteRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidConcreteDelivery }],
            };
          } else {
            concreteCondition['$ConcreteRequest.id$'] = {
              [Op.and]: [{ [Op.in]: voidConcreteDelivery }],
            };
          }
          const getConcreteRequest = await ConcreteRequest.getAll(
            inputData,
            concreteCondition,
            '',
            '',
            incomeData.descriptionFilter,
            incomeData.locationFilter,
            incomeData.concreteSupplierFilter,
            incomeData.orderNumberFilter,
            incomeData.statusFilter,
            incomeData.mixDesignFilter,
            '',
            '',
            incomeData.memberFilter,
            incomeData.search,
            order,
            incomeData.sort,
            incomeData.sortByField,
          );
          done(getConcreteRequest, false);
        } else {
          done(null, { message: 'Project Id/Member does not exist' });
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
};
module.exports = deliveryService;
