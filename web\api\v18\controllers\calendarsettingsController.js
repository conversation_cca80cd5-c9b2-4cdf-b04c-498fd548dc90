const status = require('http-status');
const moment = require('moment');
const { calendarSettingsService } = require('../services');
const { CalendarSetting } = require('../models');
const ApiError = require('../helpers/apiError');

const calendarsettingsController = {
  async getCalendarEvents(req, res, next) {
    try {
      const getAllEvents = await calendarSettingsService.getAll(req, next);
      res.status(status.OK).json({ events: getAllEvents, message: 'Events listed successfully' });
    } catch (err) {
      next(err);
    }
  },
  async getCalendarMonthEvents(req, res, next) {
    try {
      const getAllEvents = await calendarSettingsService.getAll(req, next);
      res.status(status.OK).json({ events: getAllEvents, message: 'Events listed successfully' });
    } catch (err) {
      next(err);
    }
  },
  async addCalendarEvent(req, res, next) {
    try {
      const event = await calendarSettingsService.addEvent(req);
      if (event) {
        res.status(status.CREATED).json({ message: 'Event added' });
      } else {
        const newError = new ApiError('Cannot add an event', status.UNPROCESSABLE_ENTITY);
        next(newError);
      }
    } catch (err) {
      next(err);
    }
  },
  async editCalendarEvent(req, res, next) {
    try {
      const { id } = req.params;
      const isEventExists = await CalendarSetting.isExits(id);
      if (isEventExists) {
        const updateEvent = await calendarSettingsService.updateEvent(req, next);
        if (updateEvent) {
          res.status(status.OK).json({ message: 'Event updated' });
        } else {
          res.status(status.UNPROCESSABLE_ENTITY).json({ message: 'Cannot update an event' });
        }
      } else {
        res.status(status.NOT_FOUND).json({ message: 'No event found' });
      }
    } catch (err) {
      next(err);
    }
  },
  async deleteCalendarEvent(req, res, next) {
    try {
      const { id } = req.params;
      const isEventExists = await CalendarSetting.isExits(id);
      if (isEventExists) {
        const deleteEvent = await CalendarSetting.updateInstance(id, { isDeleted: true });
        if (deleteEvent) {
          res.status(status.OK).json({ message: 'Event Series deleted' });
        } else {
          res.status(status.UNPROCESSABLE_ENTITY).json({ message: 'Cannot delete an event' });
        }
      } else {
        res.status(status.NOT_FOUND).json({ message: 'No event found' });
      }
    } catch (err) {
      next(err);
    }
  },
  async getCalendarEvent(req, res, next) {
    try {
      const getCalendarEvent = await calendarSettingsService.getCalendarEvent(req, next);
      res.status(status.OK).json({ event: getCalendarEvent, message: 'Event viewed successfully' });
    } catch (err) {
      next(err);
    }
  },
};

module.exports = calendarsettingsController;
