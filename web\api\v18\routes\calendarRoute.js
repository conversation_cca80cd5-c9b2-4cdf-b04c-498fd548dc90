const { Router } = require('express');
const { validate } = require('express-validation');
const passportConfig = require('../config/passport');
const { CalendarController } = require('../controllers');
const { calendarValidation } = require('../middlewares/validations');

const deliveryRoute = {
  get router() {
    const router = Router();
    router.post(
      '/event_NDR/:ProjectId/:void',
      validate(calendarValidation.getEventNDR, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      CalendarController.getEventNDR,
    );
    router.post(
      '/get_crane_associated_request/:ProjectId/:void',
      validate(
        calendarValidation.getDeliveryRequestWithCrane,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      CalendarController.getDeliveryRequestWithCrane,
    );
    router.post(
      '/get_concrete_request/:ProjectId/:void',
      passportConfig.isAuthenticated,
      CalendarController.getConcreteRequest,
    );
    return router;
  },
};
module.exports = deliveryRoute;
