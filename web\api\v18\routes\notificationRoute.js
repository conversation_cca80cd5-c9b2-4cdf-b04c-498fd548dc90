const { Router } = require('express');
const { validate } = require('express-validation');
const passportConfig = require('../config/passport');
const { NotificationController } = require('../controllers');
const { notificationValidation } = require('../middlewares/validations');

const deliveryRoute = {
  get router() {
    const router = Router();
    router.post(
      '/list_notification/:pageSize/:pageNo',
      validate(
        notificationValidation.listNotification,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      NotificationController.listNotification,
    );
    router.get(
      '/delete_notification',
      passportConfig.isAuthenticated,
      NotificationController.deleteNotification,
    );
    router.get(
      '/read_notification',
      passportConfig.isAuthenticated,
      NotificationController.setReadNotification,
    );
    router.get(
      '/unread_count',
      passportConfig.isAuthenticated,
      NotificationController.getNotificationCount,
    );
    router.post(
      '/version_update',
      passportConfig.isAuthenticated,
      NotificationController.versionUpdate,
    );
    router.get(
      '/current_version',
      passportConfig.isAuthenticated,
      NotificationController.getVersion,
    );
    return router;
  },
};
module.exports = deliveryRoute;
