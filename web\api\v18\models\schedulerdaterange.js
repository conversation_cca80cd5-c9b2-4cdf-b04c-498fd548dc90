module.exports = (sequelize, DataTypes) => {
  const SchedulerDateRange = sequelize.define(
    'SchedulerDateRange',
    {
      timelineName: DataTypes.STRING,
      order: DataTypes.INTEGER,
    },
    {},
  );
  SchedulerDateRange.associate = (models) => {
    // associations can be defined here
    SchedulerDateRange.hasOne(models.SchedulerReport, {
      as: 'dateRange',
      foreignKey: 'dateRangeId',
    });
  };
  return SchedulerDateRange;
};
