const axios = require('axios');
const Cryptr = require('cryptr');

// Create Deeplink for forgot password
exports.getForgotPasswordDeepLink = async (user) => {
  const userData = user;
  let redirectToWebBrowser;
  if (userData.userType === 'super admin') {
    redirectToWebBrowser = `${process.env.ADMIN_URL}/reset-password/${userData.resetPasswordToken}`;
  } else {
    redirectToWebBrowser = `${process.env.BASE_URL}/reset-password/${userData.resetPasswordToken}`;
  }
  const data = {
    type: 'reset_password',
    resetPasswordToken: userData.resetPasswordToken,
    $desktop_url: redirectToWebBrowser,
  };
  const options = {
    url: process.env.DEEPLINK_REQUEST_URL,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      branch_key: process.env.DEEPLINK_BRANCH_KEY,
      data,
    },
  };
  const deeplink = await axios.request(options);
  userData.link = deeplink.data.url;
  return userData;
};

// Create deeplink for member onboarding(invite member)
exports.getInviteMemberDeepLink = async (userData) => {
  const user = userData;
  let domain;
  if (user.domainName) {
    domain = user.domainName;
  } else {
    domain = 'public';
  }
  const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
  const encryptedEmail = cryptr.encrypt(user.email);
  const encryptedDomainName = cryptr.encrypt(domain);
  const redirectToWebBrowser = `${user.link}/invite-member/${user.id}/${user.ParentCompanyId}/${encryptedEmail}/${encryptedDomainName}`;
  const data = {
    memberId: user.id,
    ParentCompanyId: user.ParentCompanyId,
    domainName: domain,
    email: user.email,
    type: 'invite_member',
    $desktop_url: redirectToWebBrowser,
  };
  const options = {
    url: process.env.DEEPLINK_REQUEST_URL,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      branch_key: process.env.DEEPLINK_BRANCH_KEY,
      data,
    },
  };
  const deeplink = await axios.request(options);
  user.link = deeplink.data.url;
  return user;
};

// Create deeplink for signIn after registration
exports.getRegistrationDeepLink = async (user) => {
  const userData = user;
  const redirectToWebBrowser = `${process.env.BASE_URL}/login`;
  const data = {
    type: 'register',
    email: userData.email,
    password: userData.generatedPassword,
    $desktop_url: redirectToWebBrowser,
  };
  const options = {
    url: process.env.DEEPLINK_REQUEST_URL,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      branch_key: process.env.DEEPLINK_BRANCH_KEY,
      data,
    },
  };
  const deeplink = await axios.request(options);
  userData.link = deeplink.data.url;
  return userData;
};

// Create deeplink for Guest User Flow
exports.getGuestUserDeepLink = async (uniqueString) => {
  const publicDeepLinkUrl = {
    link: null,
  };
  const redirectToWebBrowser = `${process.env.BASE_URL}/project-detail/${uniqueString}`;
  const data = {
    type: 'guestUser',
    $fallback_url: redirectToWebBrowser,
    $web_only: true,
  };
  const options = {
    url: process.env.DEEPLINK_REQUEST_URL,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      branch_key: process.env.DEEPLINK_BRANCH_KEY,
      data,
    },
  };
  const deeplink = await axios.request(options);
  publicDeepLinkUrl.link = deeplink.data.url;
  return publicDeepLinkUrl;
};
