const Joi = require('joi');

const restrictMailValidation = {
  addRestrictMail: {
    body: Joi.object({
      domainName: Joi.string().min(2).required(),
    }),
  },
  updateRestrictMail: {
    body: Joi.object({
      id: Joi.number().required(),
      domainName: Joi.string().min(2).required(),
      isActive: Joi.boolean().required(),
    }),
  },
  restrictMailList: {
    params: Joi.object({
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
    }),
  },
  deleteRestrictMail: {
    body: Joi.object({
      id: Joi.number().required(),
    }),
  },
};
module.exports = restrictMailValidation;
