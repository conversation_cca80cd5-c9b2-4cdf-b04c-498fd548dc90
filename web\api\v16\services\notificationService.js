const moment = require('moment');
const { Sequelize, Enterprise, VersionUpdates } = require('../models');

let { Member, User, DeliveryPersonNotification, Notification } = require('../models');
const helper = require('../helpers/domainHelper');

const { Op } = Sequelize;
let publicUser;
let publicMember;
const notificationService = {
  async listNotification(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const incomeData = inputData.body;
      const { params } = inputData;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const offset = (pageNumber - 1) * pageSize;
      let searchCondition = {};
      const loginUser = inputData.user;
      const subCondition = { isDeleted: false, isActive: true };
      const memberDelivery = [];
      const memberDetails = await Member.findAll({
        where: Sequelize.and({ UserId: loginUser.id, isActive: true, isDeleted: false }),
      });
      memberDetails.forEach(async (element) => {
        memberDelivery.push(element.id);
      });
      const condition = {
        isDeleted: false,
        MemberId: { [Op.not]: Sequelize.col('Project.memberDetails.id') },
      };
      if (incomeData.ProjectId) {
        condition.ProjectId = +incomeData.ProjectId;
      }
      if (memberDetails) {
        if (incomeData.descriptionFilter) {
          condition.description = {
            [Sequelize.Op.iLike]: `%${incomeData.descriptionFilter}%`,
          };
        }
        if (memberDetails.RoleId === 4 || memberDetails.RoleId === 3) {
          subCondition.MemberId = {
            [Op.and]: [{ [Op.in]: memberDelivery }],
          };
        }
        if (incomeData.dateFilter) {
          let endDate = moment(incomeData.dateFilter).add(1, 'days');
          endDate = endDate.format('YYYY-MM-DD');
          condition.createdAt = {
            [Op.between]: [new Date(`${incomeData.dateFilter.split('T')[0]} 00:00:00`), endDate],
          };
        }
        if (incomeData.statusFilter) {
          condition.type = incomeData.statusFilter;
        }
        if (incomeData.projectNameFilter) {
          condition.ProjectId = incomeData.projectNameFilter;
        }
        if (incomeData.descriptionFilter) {
          condition.description = {
            [Sequelize.Op.iLike]: `%${incomeData.descriptionFilter}%`,
          };
        }
        if (incomeData.search && Object.keys(incomeData.search).length !== 0) {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: [
                  {
                    '$Project.projectName$': {
                      [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                    },
                  },
                  {
                    description: {
                      [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                    },
                  },
                  {
                    title: {
                      [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                    },
                  },
                  {
                    type: {
                      [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                    },
                  },
                ],
              },
            ],
          };
        }
        const notificationList = await Notification.getAll(
          condition,
          searchCondition,
          loginUser,
          subCondition,
        );
        const result = { count: 0, rows: [] };
        this.getLimitData(
          notificationList.rows,
          offset,
          +params.pageSize,
          0,
          [],
          async (newResponse, newError) => {
            if (!newError) {
              result.rows = newResponse;
              result.count = notificationList.rows.length;
              condition.seen = false;
              result.unSeenCount = await Notification.getUnSeenCount(
                condition,
                loginUser,
                subCondition,
              );
              done(result, false);
            } else {
              done(null, { message: 'Something went wrong' });
            }
          },
        );
      } else {
        done(null, { message: 'Member Does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async setReadNotification(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const newNot = await DeliveryPersonNotification.update(
        { seen: true },
        { where: { id: inputData.query.id } },
        // { where: { ProjectId: 239 } },
      );
      done(newNot, false);
    } catch (e) {
      done(null, e);
    }
  },
  async setReadAllNotification(inputData, done) {
    try {
      // return false;
      await this.getDynamicModel(inputData);

      const newNot = await DeliveryPersonNotification.findAll(
        { where: { ProjectId: inputData.ProjectId } },
        // { where: { ProjectId: 239 } },
      );
      done(newNot, false);
    } catch (e) {
      done(null, e);
    }
  },
  async getNotificationCount(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const loginUser = inputData.user;
      const subCondition = { isDeleted: false };
      const memberDelivery = [];
      const memberDetails = await Member.findAll({
        where: Sequelize.and({ UserId: loginUser.id, isActive: true, isDeleted: false }),
      });
      memberDetails.forEach(async (element) => {
        memberDelivery.push(element.id);
      });
      const condition = {
        seen: false,
      };
      if (inputData.query.ProjectId !== 'undefined' && inputData.query.ProjectId !== undefined) {
        condition.ProjectId = inputData.query.ProjectId;
      }
      if (memberDetails) {
        if (memberDetails.RoleId === 4 || memberDetails.RoleId === 3) {
          subCondition.MemberId = {
            [Op.and]: [{ [Op.in]: memberDelivery }],
          };
        }
        const count = await DeliveryPersonNotification.getUnSeenCount(condition, loginUser);
        done(count.length, false);
      } else {
        done(null, { message: 'Member Does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    let enterpriseValue;
    let ProjectId;
    const incomeData = inputData;
    let ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    if (!ParentCompanyId) {
      if (inputData.query) {
        ParentCompanyId = +inputData.query.ParentCompanyId;
      }
    }
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    Notification = modelObj.Notification;
    Member = modelObj.Member;
    User = modelObj.User;
    DeliveryPersonNotification = modelObj.DeliveryPersonNotification;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return ProjectId;
  },
  async getLimitData(result, index, limit, count, finalResult, done) {
    const element = result[index];
    if (index < result.length && count < limit) {
      finalResult.push(element);
      let i = count;
      i += 1;
      this.getLimitData(result, index + 1, limit, i, finalResult, (response, err) => {
        if (!err) {
          done(response, false);
        } else {
          done(null, err);
        }
      });
    } else {
      done(finalResult, false);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    // publicProject = modelData.Project;
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async deleteNotification(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { query } = inputData;
      const { id } = query;
      const notDetails = await Notification.findByPk(id);
      if (notDetails) {
        const memberData = await Member.findOne({
          where: { UserId: inputData.user.id, ProjectId: notDetails.ProjectId, isDeleted: false },
        });
        const overAllData = await DeliveryPersonNotification.findOne({
          where: { NotificationId: notDetails.id, MemberId: memberData.id },
        });
        const updateNotification = await overAllData.destroy();
        done(updateNotification, false);
      } else {
        done(null, { message: 'Notification Does not Exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async versionUpdation(inputData) {
    try {
      const versionDetails = await VersionUpdates.update(
        {
          version: inputData.body.version,
          iosversion: inputData.body.iosversion,
          releasenote: JSON.stringify(inputData.body.releasenote),
        },
        {
          where: { id: 1 },
        },
      );
      if (versionDetails) {
        return { status: 200, message: 'version updated successfully.' };
      }
      return { status: 500, message: 'cannot update the version' };
    } catch (e) {
      console.log(e);
    }
  },
  async getVersion() {
    try {
      const versionDetails = await VersionUpdates.findOne({
        where: { id: 1 },
      });
      return {
        status: 200,
        data: {
          version: versionDetails.version,
          releasenote: JSON.parse(versionDetails.releasenote),
          iosversion: versionDetails.iosversion,
        },
      };
    } catch (e) {
      console.log(e);
    }
  },
};
module.exports = notificationService;
