const { Sequelize } = require('sequelize');
const { bcryptPassword } = require('../services/password');

const { Op } = Sequelize;

module.exports = (sequelize, DataTypes) => {
  const Member = sequelize.define(
    'Member',
    {
      UserId: DataTypes.INTEGER,
      firstName: DataTypes.STRING,
      password: DataTypes.STRING,
      isDeleted: DataTypes.BOOLEAN,
      createdBy: DataTypes.INTEGER,
      memberId: DataTypes.INTEGER,
      phoneNumber: DataTypes.STRING,
      EnterpriseId: {
        type: DataTypes.INTEGER,
      },
      isAccount: {
        type: DataTypes.BOOLEAN,
      },
      phoneCode: DataTypes.STRING,
      CompanyId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ProjectId: DataTypes.INTEGER,
      RoleId: DataTypes.INTEGER,
      ParentCompanyId: DataTypes.INTEGER,
      status: DataTypes.STRING,
      memberProjectStatus: DataTypes.STRING,
      time: DataTypes.STRING,
      timeFormat: DataTypes.STRING,
      TimeZoneId: DataTypes.INTEGER,
      isActive: DataTypes.BOOLEAN,
      isAutoApproveEnabled: DataTypes.BOOLEAN,
      isGuestUser: DataTypes.BOOLEAN,
      isRequestedToBeAMember: DataTypes.BOOLEAN,
    },
    {},
  );
  Member.associate = (models) => {
    // associations can be defined here
    Member.belongsTo(models.User);
    Member.belongsTo(models.Project);
    Member.belongsTo(models.Role);
    Member.belongsTo(models.Company);
    Member.belongsTo(models.ParentCompany);
    Member.belongsTo(models.Enterprise);
    // Member.hasMany(models.NotificationPreference);
    // Member.hasMany(models.DigestNotification);
  };
  Member.createInstance = async (paramData) => {
    const memberData = paramData;
    if (memberData && memberData.password) {
      await bcryptPassword(memberData.password, (encPassword) => {
        memberData.password = encPassword;
      });
    }
    const newMember = await Member.create(memberData);
    return newMember;
  };
  Member.createMultipleInstance = async (paramData) => {
    const newMember = await Member.bulkCreate(paramData);
    return newMember;
  };
  Member.getBy = async (attr) => {
    const member = await Member.findOne({
      include: [
        {
          association: 'User',
          attributes: ['email', 'isAccount', 'firstName', 'lastName', 'versionFlag'],
        },
      ],
      where: { ...attr, isDeleted: false },
      order: [['id', 'DESC']],
    });
    return member;
  };
  Member.getAll = async (
    attr,
    nameFilter,
    companyFilter,
    roleFilter,
    statusFilter,
    searchText,
    limit,
    offset,
    sort,
    sortColumn,
  ) => {
    let commonSearch = attr;
    if (searchText !== null && searchText !== undefined && searchText !== '') {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              { '$User.firstName$': { [Sequelize.Op.iLike]: `%${searchText}%` } },
              { '$User.lastName$': { [Sequelize.Op.iLike]: `%${searchText}%` } },
              { status: { [Sequelize.Op.iLike]: `%${searchText}%` } },
              { '$User.email$': { [Sequelize.Op.iLike]: `%${searchText}%` } },
              { '$Role.roleName$': { [Sequelize.Op.iLike]: `%${searchText}%` } },
              {
                '$Company.companyName$': {
                  [Sequelize.Op.iLike]: `%${searchText}%`,
                },
              },
            ],
          },
        ],
      };
    }
    if (nameFilter !== null && nameFilter !== undefined && nameFilter !== '') {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              { '$User.firstName$': { [Sequelize.Op.iLike]: `%${nameFilter}%` } },
              { '$User.lastName$': { [Sequelize.Op.iLike]: `%${nameFilter}%` } },
            ],
          },
        ],
      };
    }
    if (
      companyFilter !== null &&
      companyFilter !== undefined &&
      companyFilter !== '' &&
      companyFilter !== 0
    ) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$Company.companyName$': {
                  [Sequelize.Op.iLike]: `%${companyFilter}%`,
                },
              },
            ],
          },
        ],
      };
    }
    if (roleFilter !== null && roleFilter !== undefined && roleFilter !== '' && roleFilter !== 0) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ '$Role.id$': roleFilter }],
          },
        ],
      };
    }
    if (statusFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ status: statusFilter }],
          },
        ],
      };
    }
    const sortByFieldName = sortColumn || 'id';
    const sortByColumnType = sort || 'DESC';
    let orderQuery;
    if (sortByFieldName === 'Company') {
      orderQuery = [['Company', 'companyName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'status' || sortByFieldName === 'id') {
      orderQuery = [[`${sortByFieldName}`, `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'roleName') {
      orderQuery = [['Role', 'roleName', `${sortByColumnType}`]];
    }
    if (
      sortByFieldName === 'firstName' ||
      sortByFieldName === 'email' ||
      sortByFieldName === 'phoneNumber'
    ) {
      orderQuery = [['User', `${sortByFieldName}`, `${sortByColumnType}`]];
    }
    const member = await Member.findAndCountAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'User',
          attributes: ['firstName', 'lastName', 'email', 'phoneNumber', 'phoneCode', 'profilePic'],
        },
        { association: 'Role', attributes: ['roleName'] },
        {
          association: 'Company',
          attributes: ['companyName', 'website', 'address', 'secondAddress'],
        },
      ],
      where: commonSearch,
      limit,
      offset,
      order: orderQuery,
    });
    return member;
  };
  Member.searchMemberNDR = async (attr, params) => {
    const member = await Member.findAll({
      subQuery: false,
      include: [
        {
          association: 'User',
          where: {
            [Op.or]: [
              {
                email: {
                  [Sequelize.Op.iLike]: `%${params.search}%`,
                },
              },
              {
                firstName: {
                  [Sequelize.Op.iLike]: `%${params.search}%`,
                },
              },
            ],
          },
          required: true,
          attributes: ['firstName', 'lastName', 'email', 'id'],
        },
      ],
      where: { ...attr },
      attributes: ['id', 'firstName', 'isGuestUser'],
    });
    return member;
  };
  Member.getAllEmail = async (attr) => {
    const member = await Member.findAll({
      include: [
        {
          association: 'User',
          attributes: ['email', 'firstName', 'lastName'],
        },
      ],
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return member;
  };
  Member.updateInstance = async (id, args) => {
    const member = await Member.update(args, { where: { id } });
    return member;
  };

  Member.getMembersProject = async (attr, limit, offset) => {
    const MembersProject = await Member.findAll({
      subQuery: false,
      include: [
        {
          association: 'User',
          attributes: [
            'firstName',
            'lastName',
            'email',
            'phoneNumber',
            'phoneCode',
            'profilePic',
            'isActive',
            'id',
          ],
        },
        { association: 'Role', attributes: ['roleName'] },
        {
          association: 'Project',
          attributes: ['id', 'projectName'],
        },
        {
          association: 'Company',
          attributes: ['id', 'companyName'],
        },
      ],
      where: { ...attr },
      attributes: ['id', 'memberId', 'UserId', 'firstName', 'memberProjectStatus'],
      limit,
      offset,
      order: [['id', 'DESC']],
    });
    return MembersProject;
  };
  return Member;
};
