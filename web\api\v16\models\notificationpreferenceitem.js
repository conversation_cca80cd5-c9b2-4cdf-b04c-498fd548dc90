module.exports = (sequelize, DataTypes) => {
  const NotificationPreferenceItem = sequelize.define(
    'NotificationPreferenceItem',
    {
      description: DataTypes.STRING,
      inappNotification: DataTypes.BOOLEAN,
      emailNotification: DataTypes.BOOLEAN,
      itemId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
    },
    {},
  );
  NotificationPreferenceItem.associate = (models) => {
    NotificationPreferenceItem.hasMany(models.NotificationPreference);
  };
  NotificationPreferenceItem.getAll = async () => {
    const notificationPreferenceItem = await NotificationPreferenceItem.findAll({
      where: { isDeleted: false },
      order: [['id', 'DESC']],
    });
    return notificationPreferenceItem;
  };
  NotificationPreferenceItem.createInstance = async () => {
    const getItems = await NotificationPreferenceItem.create();
    return getItems;
  };
  return NotificationPreferenceItem;
};
