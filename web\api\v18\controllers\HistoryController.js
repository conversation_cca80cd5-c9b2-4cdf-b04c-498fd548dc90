const status = require('http-status');
const { historyService } = require('../services');

const DefineController = {
  async createHistory(req, res, next) {
    try {
      historyService.createHistory(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'History created successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getHistory(req, res, next) {
    try {
      await historyService.getHistory(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'History Viewed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = DefineController;
