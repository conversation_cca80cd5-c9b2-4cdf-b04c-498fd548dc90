const { User, Member, Enterprise, Sequelize } = require('../models');

const { Op } = Sequelize;

const enterpriseCheck = {
  async checkEnterPrise(inputData) {
    let { domainName } = inputData.user;
    let enterpriseValue;
    let ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    if (!ParentCompanyId) {
      ParentCompanyId = inputData.params.companyId;
    }
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await User.findOne({
          where: {
            [Op.and]: [
              {
                isDeleted: false,
                [Op.and]: Sequelize.and(
                  Sequelize.where(
                    Sequelize.fn('lower', Sequelize.col('email')),
                    Sequelize.fn('lower', email),
                  ),
                ),
              },
            ],
          },
        });
        if (userData) {
          const memberData = await Member.findOne({
            where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
          });
          if (memberData) {
            if (memberData.isAccount) {
              enterpriseValue = await Enterprise.findOne({
                where: { id: memberData.EnterpriseId, status: 'completed' },
              });
              if (enterpriseValue) {
                domainName = enterpriseValue.name.toLowerCase();
              }
            } else {
              enterpriseValue = await Enterprise.findOne({
                where: { ParentCompanyId, status: 'completed' },
              });
              if (enterpriseValue) {
                domainName = enterpriseValue.name.toLowerCase();
              }
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        }
      }
    }
    return domainName;
  },
};
module.exports = enterpriseCheck;
