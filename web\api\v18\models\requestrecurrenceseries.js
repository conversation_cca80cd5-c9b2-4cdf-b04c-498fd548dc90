module.exports = (sequelize, DataTypes) => {
  const requestRecurrenceSeries = sequelize.define(
    'RequestRecurrenceSeries',
    {
      ProjectId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      ParentCompanyId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      recurrence: {
        type: DataTypes.ENUM,
        values: ['Does Not Repeat', 'Daily', 'Weekly', 'Monthly', 'Yearly'],
        allowNull: false,
      },
      recurrenceStartDate: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      recurrenceEndDate: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      dateOfMonth: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      monthlyRepeatType: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      repeatEveryCount: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      days: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true,
      },
      requestType: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      repeatEveryType: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      chosenDateOfMonth: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      isDeleted: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      createdBy: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
    },
    {},
  );
  requestRecurrenceSeries.associate = (models) => {
    requestRecurrenceSeries.belongsTo(models.Project);
    requestRecurrenceSeries.hasMany(models.DeliveryRequest, {
      as: 'DeliveryRecurrence',
      foreignKey: 'recurrenceId',
    });
    requestRecurrenceSeries.hasMany(models.CraneRequest, {
      as: 'CraneRecurrence',
      foreignKey: 'recurrenceId',
    });
    requestRecurrenceSeries.hasMany(models.ConcreteRequest, {
      as: 'ConcreteRecurrence',
      foreignKey: 'recurrenceId',
    });
    requestRecurrenceSeries.belongsTo(models.User, {
      as: 'createdUser',
      foreignKey: 'createdBy',
    });
    return requestRecurrenceSeries;
  };

  requestRecurrenceSeries.createInstance = async (paramData) => {
    const recurrenceSeries = await requestRecurrenceSeries.create(paramData);
    return recurrenceSeries;
  };

  return requestRecurrenceSeries;
};
