/* eslint-disable global-require */
const path = require('path');
const fs = require('fs');
const Sequelize = require('sequelize');
const db = require('../models');

const modelsFolder = path.join(__dirname, '../models');
const { dynamicModels } = require('../config/common');

const env = process.env.NODE_ENV || 'development';
const config = require('../../../config/db')[env];

// function getDomain(req) {
//   req.domainName = 'amega';
//   return 'amega';

// const { origin } = req.headers;
// const origin = 'http://ameganow.com';
// if (origin.indexOf('//') >= 0) {
//   const splittedOrigin = origin.split('//');
//   const splittedDomainOrigin = splittedOrigin[1].split(':');
//   if (splittedDomainOrigin[0] === '*************' || splittedDomainOrigin[0] === 'localhost') {
//     req.domainName = 'amega';
//     return 'amega';
//   }
// }

// if (origin.indexOf('//') >= 0) {
//   const splittedOrigin = origin.split('//');
//   if (splittedOrigin[1]) {
//     const originLength = splittedOrigin[1].split('.');
//     if (originLength.length === 3) {
//       if (splittedOrigin[1].indexOf('.') >= 0) {
//         const splittedDomain = splittedOrigin[1].split('.');
//         const domain = splittedDomain[0];
//         req.domainName = domain;
//         return splittedDomain[0];
//       }
//     }
//   }
// }
// }

const domain = async (domainName) => {
  const options = { ...config, schema: domainName };
  const sequelize = new Sequelize(config.database, config.username, config.password, options);
  const models = {};

  fs.readdirSync(modelsFolder)
    .filter((file) => {
      return file.indexOf('.') !== 0 && file !== 'index.js' && file.slice(-3) === '.js';
    })
    .forEach((file) => {
      // eslint-disable-next-line import/no-dynamic-require
      const model = require(path.join(modelsFolder, file))(sequelize, Sequelize.DataTypes);
      if (dynamicModels.includes(model.name)) models[`${model.name}`] = model;
    });

  Object.keys(models).forEach((modelName) => {
    if (models[modelName].associate) {
      models[modelName].associate(models);
    }
    db[`Dynamic${modelName}`] = models[modelName];
    db.dynamicSequelize = sequelize;
  });
};

module.exports = {
  domain,
};
