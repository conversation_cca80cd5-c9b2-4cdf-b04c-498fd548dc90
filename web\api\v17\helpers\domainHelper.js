const axios = require('axios');

const url = `${process.env.GODADDY_API_URL}`;
const db = require('../models');
const {
  User,
  Member,
  Company,
  Role,
  Project,
  CompanyDefine,
  ParentCompany,
  Gates,
  DeliverEquipment,
  Equipments,
  DeliverAttachement,
  DeliverDefineWork,
  DeliverDefine,
  DeliveryRequest,
  VoidList,
  DeliverComment,
  DeliverCompany,
  DeliverGate,
  DeliveryPerson,
  DeliverHistory,
  Notification,
  DeviceToken,
  DeliveryPersonNotification,
  ProjectBillingHistories,
  CraneRequest,
  CraneRequestCompany,
  CraneRequestDefinableFeatureOfWork,
  CraneRequestEquipment,
  CraneRequestResponsiblePerson,
  PresetEquipmentType,
  CraneRequestAttachment,
  CraneRequestComment,
  CraneRequestHistory,
  CalendarSetting,
  TimeZone,
  NotificationPreferenceItem,
  NotificationPreference,
  DigestNotification,
  ConcreteRequestResponsiblePerson,
  ConcreteRequestAttachment,
  ConcreteRequestComment,
  ConcreteRequestHistory,
  ConcreteRequest,
  ConcreteRequestCompany,
  ConcreteLocation,
  ConcreteMixDesign,
  ConcretePumpSize,
  ConcreteRequestLocation,
  ConcreteRequestMixDesign,
  ConcreteRequestPumpSize,
  SchedulerReport,
  ProjectSettings,
  SchedulerDateRange,
  RequestRecurrenceSeries,
  Locations,
  LocationNotificationPreferences,
} = require('../models');

let UserDynamic;
let MemberDynamic;
let CompanyDynamic;
let RoleDynamic;
let ProjectDynamic;
let DeliverDynamic;
let CompanyDefineDynamic;
let ParentCompanyDynamic;
let GatesDynamic;
let EquipmentDynamic;
let DeliveryRequestDynamic;
let VoidListDynamic;
let DeliverCompanyDynamic;
let DeliverGateDynamic;
let DeliveryPersonDynamic;
let NotificationDynamic;
let DeliverEquipmentDynamic;
let DeliverDefineDynamic;
let DeliverHistoryDynamic;
let DeliverAttachementDynamic;
let DeliverCommentDynamic;
let DeviceTokenDynamic;
let DeliveryPersonNotificationDynamic;
let ProjectBillingHistoriesDynamic;
let CraneRequestDynamic;
let CraneRequestCompanyDynamic;
let CraneRequestDefinableFeatureOfWorkDynamic;
let CraneRequestEquipmentDynamic;
let CraneRequestResponsiblePersonDynamic;
let PresetEquipmentTypeDynamic;
let CraneRequestAttachmentDynamic;
let CraneRequestCommentDynamic;
let CraneRequestHistoryDynamic;
let CalendarSettingDynamic;
let TimeZoneDynamic;
let NotificationPreferenceItemDynamic;
let NotificationPreferenceDynamic;
let DigestNotificationDynamic;
let ConcreteRequestResponsiblePersonDynamic;
let ConcreteRequestAttachmentDynamic;
let ConcreteRequestCommentDynamic;
let ConcreteRequestHistoryDynamic;
let ConcreteRequestDynamic;
let ConcreteRequestCompanyDynamic;
let ConcreteLocationDynamic;
let ConcreteMixDesignDynamic;
let ConcretePumpSizeDynamic;
let ConcreteRequestLocationDynamic;
let ConcreteRequestMixDesignDynamic;
let ConcreteRequestPumpSizeDynamic;
let SchedulerReportDynamic;
let ProjectSettingsDynamic;
let SchedulerDateRangeDynamic;
let RequestRecurrenceSeriesDynamic;
let LocationsDynamic;
let LocationNotificationPreferencesDynamic;

const dynamicModels = require('./dynamicSchemaModels');

const domainHelper = {
  async domainCreation(domainName) {
    try {
      const name = `${domainName}-${process.env.NODE_ENV}`;
      const data = [
        {
          data: process.env.SERVER_IP,
          name,
          ttl: 3600,
          type: 'A',
        },
      ];
      const options = {
        url,
        method: 'PATCH',
        headers: {
          accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `sso-key ${process.env.SSO_KEY}`,
        },
        data,
      };
      const response = await axios.request(options);
      return response.data;
    } catch (er) {
      console.error(er);
    }
  },
  async checkCurrentDomain(req, res, next) {
    this.currentDomain(req, async (domain) => {
      req.domainName = domain;
      if (domain === 'public') {
        next();
      } else {
        await db.syncToSchema(domain);
        await dynamicModels.domain(domain);
        next();
      }
    });
  },
  async getDynamicModel(domainName) {
    const dbResult = await this.checkDomain(domainName);
    if (dbResult !== null) {
      ProjectDynamic = dbResult.DynamicProject;
      MemberDynamic = dbResult.DynamicMember;
      CompanyDynamic = dbResult.DynamicCompany;
      UserDynamic = dbResult.DynamicUser;
      RoleDynamic = dbResult.DynamicRole;
      DeliverDynamic = dbResult.DynamicDeliverDefineWork;
      CompanyDefineDynamic = dbResult.DynamicCompanyDefine;
      ParentCompanyDynamic = dbResult.DynamicParentCompany;
      GatesDynamic = dbResult.DynamicGates;
      EquipmentDynamic = dbResult.DynamicEquipments;
      DeliveryRequestDynamic = dbResult.DynamicDeliveryRequest;
      VoidListDynamic = dbResult.DynamicVoidList;
      DeliverCompanyDynamic = dbResult.DynamicDeliverCompany;
      DeliverGateDynamic = dbResult.DynamicDeliverGate;
      DeliveryPersonDynamic = dbResult.DynamicDeliveryPerson;
      NotificationDynamic = dbResult.DynamicNotification;
      DeliverEquipmentDynamic = dbResult.DynamicDeliverEquipment;
      DeliverDefineDynamic = dbResult.DynamicDeliverDefine;
      DeliverHistoryDynamic = dbResult.DynamicDeliverHistory;
      DeliverAttachementDynamic = dbResult.DynamicDeliverAttachement;
      DeliverCommentDynamic = dbResult.DynamicDeliverComment;
      DeviceTokenDynamic = dbResult.DynamicDeviceToken;
      DeliveryPersonNotificationDynamic = dbResult.DynamicDeliveryPersonNotification;
      ProjectBillingHistoriesDynamic = dbResult.DynamicProjectBillingHistories;
      CraneRequestDynamic = dbResult.DynamicCraneRequest;
      CraneRequestCompanyDynamic = dbResult.CraneRequestCompany;
      CraneRequestDefinableFeatureOfWorkDynamic = dbResult.CraneRequestDefinableFeatureOfWork;
      CraneRequestEquipmentDynamic = dbResult.CraneRequestEquipment;
      CraneRequestResponsiblePersonDynamic = dbResult.CraneRequestResponsiblePerson;
      PresetEquipmentTypeDynamic = dbResult.PresetEquipmentType;
      CraneRequestAttachmentDynamic = dbResult.CraneRequestAttachment;
      CraneRequestCommentDynamic = dbResult.CraneRequestComment;
      CraneRequestHistoryDynamic = dbResult.CraneRequestHistory;
      CalendarSettingDynamic = dbResult.CalendarSetting;
      TimeZoneDynamic = dbResult.TimeZone;
      NotificationPreferenceItemDynamic = dbResult.NotificationPreferenceItem;
      NotificationPreferenceDynamic = dbResult.NotificationPreference;
      DigestNotificationDynamic = dbResult.DigestNotification;
      ConcreteRequestResponsiblePersonDynamic = dbResult.ConcreteRequestResponsiblePerson;
      ConcreteRequestAttachmentDynamic = dbResult.ConcreteRequestAttachment;
      ConcreteRequestCommentDynamic = dbResult.ConcreteRequestComment;
      ConcreteRequestHistoryDynamic = dbResult.ConcreteRequestHistory;
      ConcreteRequestDynamic = dbResult.ConcreteRequest;
      ConcreteRequestCompanyDynamic = dbResult.ConcreteRequestCompany;
      ConcreteLocationDynamic = dbResult.ConcreteLocation;
      ConcreteMixDesignDynamic = dbResult.ConcreteMixDesign;
      ConcretePumpSizeDynamic = dbResult.ConcretePumpSize;
      ConcreteRequestLocationDynamic = dbResult.ConcreteRequestLocation;
      ConcreteRequestMixDesignDynamic = dbResult.ConcreteRequestMixDesign;
      ConcreteRequestPumpSizeDynamic = dbResult.ConcreteRequestPumpSize;
      SchedulerReportDynamic = dbResult.SchedulerReport;
      ProjectSettingsDynamic = dbResult.ProjectSettings;
      SchedulerDateRangeDynamic = dbResult.SchedulerDateRange;
      RequestRecurrenceSeriesDynamic = dbResult.RequestRecurrenceSeries;
      LocationsDynamic = dbResult.Locations;
      LocationNotificationPreferencesDynamic = dbResult.LocationNotificationPreferences;
    } else {
      ProjectDynamic = Project;
      MemberDynamic = Member;
      CompanyDynamic = Company;
      UserDynamic = User;
      CompanyDefineDynamic = CompanyDefine;
      RoleDynamic = Role;
      DeliverDynamic = DeliverDefineWork;
      ParentCompanyDynamic = ParentCompany;
      GatesDynamic = Gates;
      EquipmentDynamic = Equipments;
      DeliveryRequestDynamic = DeliveryRequest;
      VoidListDynamic = VoidList;
      DeliverCompanyDynamic = DeliverCompany;
      DeliverGateDynamic = DeliverGate;
      DeliveryPersonDynamic = DeliveryPerson;
      NotificationDynamic = Notification;
      DeliverEquipmentDynamic = DeliverEquipment;
      DeliverDefineDynamic = DeliverDefine;
      DeliverHistoryDynamic = DeliverHistory;
      DeliverAttachementDynamic = DeliverAttachement;
      DeliverCommentDynamic = DeliverComment;
      DeviceTokenDynamic = DeviceToken;
      DeliveryPersonNotificationDynamic = DeliveryPersonNotification;
      ProjectBillingHistoriesDynamic = ProjectBillingHistories;
      CraneRequestDynamic = CraneRequest;
      CraneRequestCompanyDynamic = CraneRequestCompany;
      CraneRequestDefinableFeatureOfWorkDynamic = CraneRequestDefinableFeatureOfWork;
      CraneRequestEquipmentDynamic = CraneRequestEquipment;
      CraneRequestResponsiblePersonDynamic = CraneRequestResponsiblePerson;
      PresetEquipmentTypeDynamic = PresetEquipmentType;
      CraneRequestAttachmentDynamic = CraneRequestAttachment;
      CraneRequestCommentDynamic = CraneRequestComment;
      CraneRequestHistoryDynamic = CraneRequestHistory;
      CalendarSettingDynamic = CalendarSetting;
      TimeZoneDynamic = TimeZone;
      NotificationPreferenceItemDynamic = NotificationPreferenceItem;
      NotificationPreferenceDynamic = NotificationPreference;
      DigestNotificationDynamic = DigestNotification;
      ConcreteRequestResponsiblePersonDynamic = ConcreteRequestResponsiblePerson;
      ConcreteRequestAttachmentDynamic = ConcreteRequestAttachment;
      ConcreteRequestCommentDynamic = ConcreteRequestComment;
      ConcreteRequestHistoryDynamic = ConcreteRequestHistory;
      ConcreteRequestDynamic = ConcreteRequest;
      ConcreteRequestCompanyDynamic = ConcreteRequestCompany;
      ConcreteLocationDynamic = ConcreteLocation;
      ConcreteMixDesignDynamic = ConcreteMixDesign;
      ConcretePumpSizeDynamic = ConcretePumpSize;
      ConcreteRequestLocationDynamic = ConcreteRequestLocation;
      ConcreteRequestMixDesignDynamic = ConcreteRequestMixDesign;
      ConcreteRequestPumpSizeDynamic = ConcreteRequestPumpSize;
      SchedulerReportDynamic = SchedulerReport;
      ProjectSettingsDynamic = ProjectSettings;
      SchedulerDateRangeDynamic = SchedulerDateRange;
      RequestRecurrenceSeriesDynamic = RequestRecurrenceSeries;
      LocationsDynamic = Locations;
      LocationNotificationPreferencesDynamic = LocationNotificationPreferences;
    }
    return {
      Project: ProjectDynamic,
      Member: MemberDynamic,
      Company: CompanyDynamic,
      User: UserDynamic,
      Role: RoleDynamic,
      CompanyDefine: CompanyDefineDynamic,
      Gates: GatesDynamic,
      DeliverDefineWork: DeliverDynamic,
      DeliveryRequest: DeliveryRequestDynamic,
      VoidList: VoidListDynamic,
      DeliverCompany: DeliverCompanyDynamic,
      DeliverGate: DeliverGateDynamic,
      DeliveryPerson: DeliveryPersonDynamic,
      Equipments: EquipmentDynamic,
      Notification: NotificationDynamic,
      ParentCompany: ParentCompanyDynamic,
      DeliverEquipment: DeliverEquipmentDynamic,
      DeliverDefine: DeliverDefineDynamic,
      DeliverHistory: DeliverHistoryDynamic,
      DeliverAttachement: DeliverAttachementDynamic,
      DeliverComment: DeliverCommentDynamic,
      DeviceToken: DeviceTokenDynamic,
      DeliveryPersonNotification: DeliveryPersonNotificationDynamic,
      ProjectBillingHistories: ProjectBillingHistoriesDynamic,
      CraneRequest: CraneRequestDynamic,
      CraneRequestCompany: CraneRequestCompanyDynamic,
      CraneRequestDefinableFeatureOfWork: CraneRequestDefinableFeatureOfWorkDynamic,
      CraneRequestEquipment: CraneRequestEquipmentDynamic,
      CraneRequestResponsiblePerson: CraneRequestResponsiblePersonDynamic,
      PresetEquipmentType: PresetEquipmentTypeDynamic,
      CraneRequestAttachment: CraneRequestAttachmentDynamic,
      CraneRequestComment: CraneRequestCommentDynamic,
      CraneRequestHistory: CraneRequestHistoryDynamic,
      CalendarSetting: CalendarSettingDynamic,
      TimeZone: TimeZoneDynamic,
      NotificationItems: NotificationPreferenceItemDynamic,
      NotificationPreference: NotificationPreferenceDynamic,
      DigestNotification: DigestNotificationDynamic,
      ConcreteRequestResponsiblePerson: ConcreteRequestResponsiblePersonDynamic,
      ConcreteRequestAttachment: ConcreteRequestAttachmentDynamic,
      ConcreteRequestComment: ConcreteRequestCommentDynamic,
      ConcreteRequestHistory: ConcreteRequestHistoryDynamic,
      ConcreteRequest: ConcreteRequestDynamic,
      ConcreteRequestCompany: ConcreteRequestCompanyDynamic,
      ConcreteLocation: ConcreteLocationDynamic,
      ConcreteMixDesign: ConcreteMixDesignDynamic,
      ConcretePumpSize: ConcretePumpSizeDynamic,
      ConcreteRequestLocation: ConcreteRequestLocationDynamic,
      ConcreteRequestMixDesign: ConcreteRequestMixDesignDynamic,
      ConcreteRequestPumpSize: ConcreteRequestPumpSizeDynamic,
      SchedulerReport: SchedulerReportDynamic,
      ProjectSettings: ProjectSettingsDynamic,
      SchedulerDateRange: SchedulerDateRangeDynamic,
      RequestRecurrenceSeries: RequestRecurrenceSeriesDynamic,
      Locations: LocationsDynamic,
      LocationNotificationPreferences: LocationNotificationPreferencesDynamic,
    };
  },
  async returnProjectModel() {
    const modelValue = {
      Project,
      User,
      Member,
      Company,
      ParentCompany,
    };

    return modelValue;
  },
  async checkDomain(domainName) {
    if (domainName !== null && domainName !== undefined) {
      await dynamicModels.domain(domainName);
      return db;
    }
    return null;
  },
  async currentDomain(req, res, next) {
    const origin = req.get('origin');
    let domain;
    if (origin && origin.indexOf('//') >= 0) {
      const splittedOrigin = origin.split('//');
      let splittedDomainOrigin = splittedOrigin[1].split('.')[0];
      if (splittedDomainOrigin) splittedDomainOrigin = splittedDomainOrigin.split('-');
      if (splittedDomainOrigin.length === 2) {
        const d = splittedDomainOrigin[0];
        req.domainName = d;
        domain = req.domainName;
        await dynamicModels.domain(domain);
        next();
      } else {
        next();
      }
    } else {
      next();
    }
  },
};

module.exports = domainHelper;
