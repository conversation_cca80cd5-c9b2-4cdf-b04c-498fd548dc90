const moment = require('moment');
const _ = require('lodash');
const { Sequelize, Enterprise } = require('../models');
let { Project, Company, DeliveryRequest, Member, User } = require('../models');
const { Equipments, CraneRequest, ConcreteRequest, VoidList } = require('../models');
const helper = require('../helpers/domainHelper');

let publicProject;
let publicUser;
let publicMember;
const { Op } = Sequelize;
const dashboardService = {
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicProject = modelData.Project;
    publicUser = modelData.User;
    publicMember = modelData.Member;
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const incomeData = inputData;
    const loginUser = inputData.user;

    let enterpriseValue;
    let ProjectId;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }

    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    DeliveryRequest = modelObj.DeliveryRequest;
    Member = modelObj.Member;
    Company = modelObj.Company;
    Project = modelObj.Project;
    User = modelObj.User;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return ProjectId;
  },
  async getDashboardData(inputData, done) {
    try {
      const incomeData = inputData.body;
      let totalNewProject = [];
      let modelName;
      if (incomeData.RoleId !== 4) {
        await this.getDynamicModel(inputData);
        modelName = Project;
      } else {
        await this.returnProjectModel();
        modelName = publicProject;
      }
      totalNewProject = await modelName.getUserProjects(inputData.user);
      const projectCount = totalNewProject.length;
      const dashData = {
        projects: projectCount,
        deliveryRequest: 0,
        member: 0,
        company: 0,
        equipment: 0,
        diffEquipment: 0,
        diffCompany: 0,
        diffMember: 0,
        diffDeliveryRequest: 0,
        diffProjects: 0,
      };
      const date = new Date();
      const y = date.getFullYear();
      const m = date.getMonth();
      const currentFirstDay = new Date(y, m, 1);
      const currentLastDay = new Date(y, m + 1, 0);
      const previousMonth = new Date(y, m, 1);
      let newMonth = previousMonth.getMonth() - 1;
      if (newMonth < 0) {
        newMonth += 12;
        previousMonth.setYear(previousMonth.getYear() - 1);
      }
      previousMonth.setMonth(newMonth);
      const prevDate = new Date(previousMonth);
      const prevYear = prevDate.getFullYear();
      const prevMonth = prevDate.getMonth();
      const previousFirstDay = new Date(prevYear, prevMonth, 1);
      const previousLastDay = new Date(prevYear, prevMonth + 1, 0);
      let ProjectId;
      if (inputData.params) {
        if (
          inputData.params.ProjectId !== undefined &&
          inputData.params.ProjectId !== 'undefined'
        ) {
          ProjectId = inputData.params.ProjectId;
        } else {
          ProjectId = incomeData.ProjectId;
        }
      } else {
        ProjectId = incomeData.ProjectId;
      }
      const currentMonCondition = {
        createdAt: {
          [Op.between]: [new Date(currentFirstDay), new Date(currentLastDay)],
        },
      };
      const prevMonCondition = {
        createdAt: {
          [Op.between]: [new Date(previousFirstDay), new Date(previousLastDay)],
        },
      };
      const currentMonthProject = await modelName.getProjectCount(
        inputData.user,
        currentMonCondition,
      );
      let totalProject;
      const prevMonthProject = await modelName.getProjectCount(inputData.user, prevMonCondition);
      dashData.diffProjects = currentMonthProject.length - prevMonthProject.length;
      if (inputData.PAadmin || (ProjectId !== undefined && ProjectId !== 'undefined')) {
        const newProject = [];
        if (incomeData.RoleId === 4) {
          await this.getDynamicModel(inputData);
        }
        const data = await Project.findByPk(ProjectId);
        newProject.push(data);
        totalProject = newProject;
      } else {
        totalProject = totalNewProject;
      }

      if (totalProject.length > 0) {
        await this.getTotalCompany(
          totalProject,
          0,
          0,
          currentFirstDay,
          currentLastDay,
          previousFirstDay,
          previousLastDay,
          0,
          async (result, err) => {
            if (!err) {
              dashData.company = result.totalCompany;
              dashData.diffCompany = result.diffCompany;
              await this.getTotalMember(
                totalProject,
                0,
                0,
                currentFirstDay,
                currentLastDay,
                previousFirstDay,
                previousLastDay,
                0,
                async (memberResult, memberErr) => {
                  if (!memberErr) {
                    dashData.member = memberResult.totalMember;
                    dashData.diffMember = memberResult.diffMember;
                    await this.getTotalEquipments(
                      totalProject,
                      0,
                      0,
                      currentFirstDay,
                      currentLastDay,
                      previousFirstDay,
                      previousLastDay,
                      0,
                      async (equipmentResult, equipmentErr) => {
                        if (!equipmentErr) {
                          dashData.equipment = equipmentResult.totalEquipments;
                          dashData.diffEquipment = equipmentResult.diffEquipment;
                          await this.getTotalDeliveryRequest(
                            totalProject,
                            0,
                            0,
                            currentFirstDay,
                            currentLastDay,
                            previousFirstDay,
                            previousLastDay,
                            0,
                            async (deliveryResult, deliveryErr) => {
                              if (!deliveryErr) {
                                dashData.deliveryRequest = deliveryResult.totalDelivery;
                                dashData.diffDeliveryRequest = deliveryResult.diffDeliveryRequest;
                                done(dashData, false);
                              }
                            },
                          );
                        }
                      },
                    );
                  }
                },
              );
            }
          },
        );
      } else {
        done(dashData, false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getTotalCompany(
    totalProject,
    index,
    totalCompany,
    currentFirstDay,
    currentLastDay,
    previousFirstDay,
    previousLastDay,
    totalDiff,
    done,
  ) {
    try {
      const element = totalProject[index];
      let newDiff = totalDiff;
      let companyCount = totalCompany;
      if (element) {
        let condition = {};
        let parentCount = 0;
        if (totalProject.length === 1) {
          condition = { isParent: { [Op.ne]: true } };
          parentCount = 1;
        }
        let companyDet = await Company.count({
          where: { ProjectId: element.id, isDeleted: false, ...condition },
        });

        let currentMonthCompany = await Company.count({
          where: {
            createdAt: {
              [Op.between]: [new Date(currentFirstDay), new Date(currentLastDay)],
            },
            ProjectId: element.id,
            ...condition,
          },
        });
        let previousMonthCompany = await Company.count({
          where: {
            createdAt: {
              [Op.between]: [new Date(previousFirstDay), new Date(previousLastDay)],
            },
            ProjectId: element.id,
            ...condition,
          },
        });
        if (parentCount === 1) {
          const parentCompany = await Member.findOne({
            where: Sequelize.and({ ProjectId: element.id, isDeleted: false }),
            include: [
              {
                association: 'Company',
                required: true,
                where: { isParent: true },
                attributes: ['id', 'createdAt'],
              },
            ],
            attributes: ['id'],
          });
          if (parentCompany) {
            if (parentCompany.Company) {
              const createdDate = new Date(parentCompany.Company.createdAt).getTime();
              const currentFirstDate = new Date(currentFirstDay).getTime();
              const currentLastDate = new Date(currentLastDay).getTime();
              const previousFirstDate = new Date(previousFirstDay).getTime();
              const previousLastDate = new Date(previousLastDay).getTime();
              if (currentFirstDate <= createdDate <= currentLastDate) {
                currentMonthCompany += 1;
              }
              if (previousFirstDate <= createdDate <= previousLastDate) {
                previousMonthCompany += 1;
              }
              companyDet += 1;
            }
          }
        }
        companyCount += companyDet;

        newDiff += currentMonthCompany - previousMonthCompany;
        if (index < totalProject.length - 1) {
          this.getTotalCompany(
            totalProject,
            index + 1,
            companyCount,
            currentFirstDay,
            currentLastDay,
            previousFirstDay,
            previousLastDay,
            newDiff,
            (response, err) => {
              if (!err) {
                done(response, false);
              } else {
                done(null, err);
              }
            },
          );
        } else {
          done({ totalCompany: companyCount, diffCompany: newDiff }, false);
        }
      } else {
        done({ totalCompany: companyCount, diffCompany: newDiff }, false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getTotalMember(
    totalProject,
    index,
    totalMember,
    currentFirstDay,
    currentLastDay,
    previousFirstDay,
    previousLastDay,
    totalDiff,
    done,
  ) {
    try {
      const element = totalProject[index];
      let newDiff = totalDiff;
      let totalNewMember = totalMember;
      if (element) {
        const returnMember = await Member.count({
          where: { ProjectId: element.id, isDeleted: false, RoleId: { [Op.ne]: 1 }, isGuestUser: false },
        });
        totalNewMember += returnMember;
        const currentMonthMember = await Member.count({
          where: {
            createdAt: {
              [Op.between]: [new Date(currentFirstDay), new Date(currentLastDay)],
            },
            ProjectId: element.id,
            RoleId: { [Op.ne]: 1 },
            isDeleted: false,
            isGuestUser: false,
          },
        });
        const previousMonthMember = await Member.count({
          where: {
            createdAt: {
              [Op.between]: [new Date(previousFirstDay), new Date(previousLastDay)],
            },
            ProjectId: element.id,
            RoleId: { [Op.ne]: 1 },
            isDeleted: false,
            isGuestUser: false
          },
        });
        newDiff += currentMonthMember - previousMonthMember;
        if (index < totalProject.length - 1) {
          this.getTotalMember(
            totalProject,
            index + 1,
            totalNewMember,
            currentFirstDay,
            currentLastDay,
            previousFirstDay,
            previousLastDay,
            newDiff,
            (response, err) => {
              if (!err) {
                done(response, false);
              } else {
                done(null, err);
              }
            },
          );
        } else {
          done({ totalMember: totalNewMember, diffMember: newDiff }, false);
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getTotalEquipments(
    totalProject,
    index,
    totalEquipment,
    currentFirstDay,
    currentLastDay,
    previousFirstDay,
    previousLastDay,
    totalDiff,
    done,
  ) {
    try {
      const element = totalProject[index];
      let newDiff = totalDiff;
      let totalNewEquipment = totalEquipment;
      if (element) {
        const returnEquipment = await Equipments.count({
          where: { ProjectId: element.id, isDeleted: false },
        });
        totalNewEquipment += returnEquipment;
        const currentMonthEquipment = await Equipments.count({
          where: {
            createdAt: {
              [Op.between]: [new Date(currentFirstDay), new Date(currentLastDay)],
            },
            ProjectId: element.id,
            isDeleted: false,
          },
        });
        const previousMonthEquipment = await Equipments.count({
          where: {
            createdAt: {
              [Op.between]: [new Date(previousFirstDay), new Date(previousLastDay)],
            },
            ProjectId: element.id,
            isDeleted: false,
          },
        });
        newDiff += currentMonthEquipment - previousMonthEquipment;
        if (index < totalProject.length - 1) {
          this.getTotalEquipments(
            totalProject,
            index + 1,
            totalNewEquipment,
            currentFirstDay,
            currentLastDay,
            previousFirstDay,
            previousLastDay,
            newDiff,
            (response, err) => {
              if (!err) {
                done(response, false);
              } else {
                done(null, err);
              }
            },
          );
        } else {
          done({ totalEquipments: totalNewEquipment, diffEquipment: newDiff }, false);
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getTotalDeliveryRequest(
    totalProject,
    index,
    totalDelivery,
    currentFirstDay,
    currentLastDay,
    previousFirstDay,
    previousLastDay,
    totalDiff,
    done,
  ) {
    try {
      const element = totalProject[index];
      let newDiff = totalDiff;
      let deliveryCount = totalDelivery;
      if (element) {
        const voidDelivery = [];
        const deliveryCondition = {
          isDeleted: false,
        };
        const voidDeliveryRequestList = await VoidList.findAll({
          where: {
            ProjectId: +element.id,
            isDeliveryRequest: true,
            DeliveryRequestId: { [Op.ne]: null },
          },
        });
        voidDeliveryRequestList.forEach(async (element1) => {
          voidDelivery.push(element1.DeliveryRequestId);
        });
        deliveryCondition['$DeliveryRequest.id$'] = {
          [Op.and]: [{ [Op.notIn]: voidDelivery }],
        };
        const newDelivery = await DeliveryRequest.count({
          where: { ProjectId: element.id, isDeleted: false, ...deliveryCondition },
        });
        deliveryCount += newDelivery;
        const currentMonthDelivery = await DeliveryRequest.count({
          where: {
            createdAt: {
              [Op.between]: [new Date(currentFirstDay), new Date(currentLastDay)],
            },
            ProjectId: element.id,
            ...deliveryCondition,
          },
        });
        const previousMonthDelivery = await DeliveryRequest.count({
          where: {
            createdAt: {
              [Op.between]: [new Date(previousFirstDay), new Date(previousLastDay)],
            },
            ProjectId: element.id,
            ...deliveryCondition,
          },
        });
        newDiff += currentMonthDelivery - previousMonthDelivery;
        if (index < totalProject.length - 1) {
          this.getTotalDeliveryRequest(
            totalProject,
            index + 1,
            deliveryCount,
            currentFirstDay,
            currentLastDay,
            previousFirstDay,
            previousLastDay,
            newDiff,
            (response, err) => {
              if (!err) {
                done(response, false);
              } else {
                done(null, err);
              }
            },
          );
        } else {
          done({ totalDelivery: deliveryCount, diffDeliveryRequest: newDiff }, false);
        }
      } else if (index < totalProject.length - 1) {
        this.getTotalDeliveryRequest(
          totalProject,
          index + 1,
          deliveryCount,
          currentFirstDay,
          currentLastDay,
          previousFirstDay,
          previousLastDay,
          newDiff,
          (response, err) => {
            if (!err) {
              done(response, false);
            } else {
              done(null, err);
            }
          },
        );
      } else {
        done({ totalDelivery: deliveryCount, diffDeliveryRequest: newDiff }, false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getOverAllDelivery(inputData, newCondition, limit, offset) {
    try {
      await this.getDynamicModel(inputData);
      let projectData = [];
      let projectList;
      let condition = {
        isDeleted: false,
      };
      if (newCondition.ProjectId) {
        projectData = [];
        projectData.push(newCondition.ProjectId);
      } else {
        projectList = await Project.getUserProjects(inputData.user);
        projectList.forEach(async (element) => {
          projectData.push(element.id);
        });
      }
      if (newCondition.upcoming) {
        condition = {
          deliveryStart: {
            [Op.gt]: new Date(),
          },
          isDeleted: false,
        };
      }
      const voidDelivery = [];
      const voidDeliveryRequestList = await VoidList.findAll({
        where: {
          ProjectId: +newCondition.ProjectId,
          isDeliveryRequest: true,
          DeliveryRequestId: { [Op.ne]: null },
        },
      });
      voidDeliveryRequestList.forEach(async (element) => {
        voidDelivery.push(element.DeliveryRequestId);
      });
      condition['$DeliveryRequest.id$'] = {
        [Op.and]: [{ [Op.notIn]: voidDelivery }],
      };
      const deliveryList = await DeliveryRequest.findAndCountAll({
        include: [
          {
            association: 'Project',
            where: { isDeleted: false },
            attributes: ['id', 'projectName', 'publicSchemaId'],
          },
        ],
        where: {
          ProjectId: { [Op.in]: projectData },
          ...condition,
          isQueued: false,
        },
        attributes: ['id', 'DeliveryId', 'description', 'deliveryStart'],
        limit,
        offset,
        order: [['deliveryStart', 'ASC']],
      });
      return deliveryList;
    } catch (e) {
      return e;
    }
  },
  async getOverAllConcreteRequest(inputData, newCondition, limit, offset) {
    try {
      await this.getDynamicModel(inputData);
      let projectData = [];
      let projectList;
      let condition = {
        isDeleted: false,
      };
      if (newCondition.ProjectId) {
        projectData = [];
        projectData.push(newCondition.ProjectId);
      } else {
        projectList = await Project.getUserProjects(inputData.user);
        projectList.forEach(async (element) => {
          projectData.push(element.id);
        });
      }
      if (newCondition.upcoming) {
        condition = {
          concretePlacementStart: {
            [Op.gt]: new Date(),
          },
          isDeleted: false,
        };
      }
      const voidConcrete = [];
      const voidConcreteRequestList = await VoidList.findAll({
        where: {
          ProjectId: +newCondition.ProjectId,
          isDeliveryRequest: false,
          ConcreteRequestId: { [Op.ne]: null },
        },
      });
      voidConcreteRequestList.forEach(async (element) => {
        voidConcrete.push(element.ConcreteRequestId);
      });
      condition['$ConcreteRequest.id$'] = {
        [Op.and]: [{ [Op.notIn]: voidConcrete }],
      };
      const deliveryList = await ConcreteRequest.findAndCountAll({
        include: [
          {
            association: 'Project',
            where: { isDeleted: false },
            attributes: ['id', 'projectName', 'publicSchemaId'],
          },
        ],
        where: {
          ProjectId: { [Op.in]: projectData },
          ...condition,
        },
        attributes: ['id', 'ConcreteRequestId', 'description', 'concretePlacementStart'],
        limit,
        offset,
        order: [['concretePlacementStart', 'ASC']],
      });
      return deliveryList;
    } catch (e) {
      return e;
    }
  },
  async getGraphDelivery(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const incomeData = inputData.body;
      let newCondition = {};
      if (incomeData.ProjectId) {
        newCondition = { ProjectId: incomeData.ProjectId };
      }
      const deliveryListData = await this.getOverAllDelivery(inputData, newCondition);
      const deliveryList = deliveryListData.rows;
      deliveryList.forEach((item1) => {
        const item = item1;
        item.deliveryDate = moment(item.deliveryStart).format('MM-YYYY');
        item.shortmonth = moment(item.deliveryStart).format('MMM');
        item.month = moment(item.deliveryStart).format('MM');
        item.year = moment(item.deliveryStart).format('YYYY');
      });

      const results = _.sortBy(
        _.chain(deliveryList)
          .groupBy('deliveryDate')
          .map((value) => ({
            total: value.length,
            month: value && value[0] && value[0].month,
            year: value && value[0] && value[0].year,
            shortmonth: value && value[0] && value[0].shortmonth,
          }))
          .value(),
        ['year', 'month'],
      );
      done({ count: results }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async getOverAllCrane(inputData, newCondition) {
    try {
      await this.getDynamicModel(inputData);
      let projectData = [];
      let projectList;
      let condition = {};
      let condition1 = {};
      if (newCondition.ProjectId) {
        projectData = [];
        projectData.push(newCondition.ProjectId);
      } else {
        projectList = await Project.getUserProjects(inputData.user);
        projectList.forEach(async (element) => {
          projectData.push(element.id);
        });
      }
      condition = {
        isDeleted: false,
      };
      condition1 = {
        isDeleted: false,
      };
      const voidCraneDelivery = [];
      const voidDelivery = [];
      const voidDeliveryList = await VoidList.findAll({
        where: {
          ProjectId: newCondition.ProjectId,
          isDeliveryRequest: true,
          DeliveryRequestId: { [Op.ne]: null },
        },
      });
      voidDeliveryList.forEach(async (element) => {
        voidDelivery.push(element.DeliveryRequestId);
      });
      const voidCraneRequestList = await VoidList.findAll({
        where: {
          ProjectId: newCondition.ProjectId,
          isDeliveryRequest: false,
          CraneRequestId: { [Op.ne]: null },
        },
      });
      voidCraneRequestList.forEach(async (element) => {
        voidCraneDelivery.push(element.CraneRequestId);
      });
      condition['$DeliveryRequest.id$'] = {
        [Op.and]: [{ [Op.notIn]: voidDelivery }],
      };
      condition1['$CraneRequest.id$'] = {
        [Op.and]: [{ [Op.notIn]: voidCraneDelivery }],
      };
      const craneRequestList = await CraneRequest.findAll({
        include: [
          {
            association: 'Project',
            where: { isDeleted: false },
            attributes: ['id', 'projectName', 'publicSchemaId'],
          },
        ],
        where: {
          ProjectId: { [Op.in]: projectData },
          ...condition1,
        },
        attributes: [
          'id',
          'CraneRequestId',
          'description',
          'craneDeliveryStart',
          'requestType',
          'craneDeliveryEnd',
        ],
        order: [['craneDeliveryStart', 'ASC']],
      });
      const deliveryList = await DeliveryRequest.findAll({
        include: [
          {
            association: 'Project',
            where: { isDeleted: false },
            attributes: ['id', 'projectName', 'publicSchemaId'],
          },
        ],
        where: {
          ProjectId: { [Op.in]: projectData },
          ...condition,
          isQueued: false,
          requestType: 'deliveryRequestWithCrane',
        },
        attributes: ['id', 'DeliveryId', 'description', 'deliveryStart', 'requestType'],
        order: [['deliveryStart', 'ASC']],
      });
      const requestData = [];
      if (craneRequestList && craneRequestList.length > 0) {
        requestData.push(...craneRequestList);
      }
      if (deliveryList && deliveryList.length > 0) {
        requestData.push(...deliveryList);
      }
      return requestData;
    } catch (e) {
      return e;
    }
  },
  async getCraneGraphData(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const incomeData = inputData.body;
      let newCondition = {};
      if (incomeData.ProjectId) {
        newCondition = { ProjectId: incomeData.ProjectId };
      }
      const getCraneList = await this.getOverAllCrane(inputData, newCondition);
      getCraneList.forEach((item1) => {
        const item = item1;
        item.deliveryDate =
          item.requestType === 'craneRequest'
            ? moment(item.craneDeliveryStart).format('MM-YYYY')
            : moment(item.deliveryStart).format('MM-YYYY');
        item.shortmonth =
          item.requestType === 'craneRequest'
            ? moment(item.craneDeliveryStart).format('MMM')
            : moment(item.deliveryStart).format('MMM');
        item.month =
          item.requestType === 'craneRequest'
            ? moment(item.craneDeliveryStart).format('MM')
            : moment(item.deliveryStart).format('MM');
        item.year =
          item.requestType === 'craneRequest'
            ? moment(item.craneDeliveryStart).format('YYYY')
            : moment(item.deliveryStart).format('YYYY');
      });
      const results = _.sortBy(
        _.chain(getCraneList)
          .groupBy('deliveryDate')
          .map((value) => ({
            total: value.length,
            month: value && value[0] && value[0].month,
            year: value && value[0] && value[0].year,
            shortmonth: value && value[0] && value[0].shortmonth,
          }))
          .value(),
        ['year', 'month'],
      );
      done({ count: results }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async getConcreteGraphData(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const incomeData = inputData.body;
      let newCondition = {};
      if (incomeData.ProjectId) {
        newCondition = { ProjectId: incomeData.ProjectId };
      }
      const deliveryListData = await this.getOverAllConcreteRequest(inputData, newCondition);
      const deliveryList = deliveryListData.rows;
      deliveryList.forEach((item1) => {
        const item = item1;
        item.deliveryDate = moment(item.concretePlacementStart).format('MM-YYYY');
        item.shortmonth = moment(item.concretePlacementStart).format('MMM');
        item.month = moment(item.concretePlacementStart).format('MM');
        item.year = moment(item.concretePlacementStart).format('YYYY');
      });

      const results = _.sortBy(
        _.chain(deliveryList)
          .groupBy('deliveryDate')
          .map((value) => ({
            total: value.length,
            month: value && value[0] && value[0].month,
            year: value && value[0] && value[0].year,
            shortmonth: value && value[0] && value[0].shortmonth,
          }))
          .value(),
        ['year', 'month'],
      );
      done({ count: results }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async upcomingDelivery(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const offset = (pageNumber - 1) * pageSize;
      const deliveryList = await this.getOverAllDelivery(
        inputData,
        { upcoming: true },
        pageSize,
        offset,
      );
      done(deliveryList, false);
    } catch (e) {
      done(null, e);
    }
  },
  async getReleasenoteVersion(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const loginUser = inputData.user;
      const newNversionseen = await User.update(
        { versionFlag: true },
        {
          where: {
            id: loginUser.id,
          },
        },
      );
      done(newNversionseen, false);
    } catch (e) {
      done(null, e);
    }
  },
};
module.exports = dashboardService;
