module.exports = (sequelize, DataTypes) => {
  const CraneRequestEquipment = sequelize.define(
    'CraneRequestEquipment',
    {
      CraneRequestId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      CraneRequestCode: DataTypes.INTEGER,
      EquipmentId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ProjectId: DataTypes.INTEGER,
      isActive: DataTypes.BOOLEAN,
    },
    {},
  );
  CraneRequestEquipment.associate = (models) => {
    CraneRequestEquipment.belongsTo(models.CraneRequest, {
      as: 'craneRequest',
      foreignKey: 'CraneRequestId',
    });
    CraneRequestEquipment.belongsTo(models.Equipments, {
      as: 'Equipments',
      foreignKey: 'EquipmentId',
    });
    CraneRequestEquipment.belongsTo(models.Equipments);
  };
  CraneRequestEquipment.createInstance = async (paramData) => {
    const newCraneEquipment = await CraneRequestEquipment.create(paramData);
    return newCraneEquipment;
  };
  return CraneRequestEquipment;
};
