/* eslint-disable no-await-in-loop */
const { Project, ParentCompany } = require('../models');

const stripeService = {
  async getEnterpriseAccounts(req) {
    try {
      const { pageSize, pageNo, sortColumn, sortType } = req.query;
      let offset;
      if (pageNo) {
        offset = (pageNo - 1) * pageSize;
      }
      const parentCompaniesArray = [];
      const parentCompanies = await ParentCompany.findAll({
        where: { isDeleted: false },
        order: [[`${sortColumn}`, `${sortType}`]],
      });
      for (let index = 0; index < parentCompanies.length; index += 1) {
        const projectCount = await Project.findAndCountAll({
          where: { ParentCompanyId: parentCompanies[index].id },
        });
        if (+projectCount.count < 5) {
          parentCompaniesArray.push({
            company: parentCompanies[index],
            projectCount: +projectCount.count,
          });
        }
      }
      const companyArray = {};
      companyArray.count = parentCompaniesArray.length;
      companyArray.rows = parentCompaniesArray.slice(offset, offset + pageSize);
      return companyArray;
    } catch (e) {
      console.log(e);
    }
  },
  async getNonEnterpriseAccountProjects(req) {
    try {
      const parentCompanyId = req.params.id;
      const { search, pageSize, pageNo, sortColumn, sortType, projectName, companyName } =
        req.query;
      let offset;
      if (pageNo) {
        offset = (pageNo - 1) * pageSize;
      }
      const projects = await Project.getAllNonEnterprise(
        { ParentCompanyId: +parentCompanyId, isDeleted: false },
        pageSize,
        offset,
        search,
        sortColumn,
        sortType,
        projectName,
        companyName,
      );
      const projectArray = {};
      projectArray.count = projects.rows.length;
      projectArray.rows = projects.rows.slice(offset, offset + pageSize);
      return projectArray;
    } catch (e) {
      console.log(e);
    }
  },
};

module.exports = stripeService;
