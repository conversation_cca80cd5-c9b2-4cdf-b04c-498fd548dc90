const moment = require('moment');
const _ = require('lodash');

const mainQueryuilder = (qType, incomeData, deliveryInCrane = false) => {
  let mainQuery = ``;
  let whereClause = ``;

  // TODO: DST Fix
  const { timezone } = incomeData;
  const { startDate } = incomeData;
  const { endDate } = incomeData;
  const { startTime } = incomeData;
  const { endTime } = incomeData;
  const nextDay = moment(startDate).add(1, 'days');
  const queryStartDate = nextDay.format('YYYY-MM-DD');
  const finalFromTimeSeconds = timeToSeconds(startTime);
  const finalToTimeSeconds = timeToSeconds(endTime);

  function timeToSeconds(timeString) {
    const [hours, minutes, seconds] = timeString.split(':');
    return +hours * 60 * 60 + +minutes * 60 + +seconds;
  }

  let singleQuery = true;

  if (finalFromTimeSeconds > finalToTimeSeconds) {
    singleQuery = false;
  } else {
    singleQuery = true;
  }
  if (qType == 0) {
    mainQuery = ` select
                                (
                                date_trunc('hour', "DeliveryRequests"."deliveryStart" AT TIME ZONE '${timezone}' )::timestamp
                                ) AS "hour_start"
                            FROM
                                public."DeliveryRequests"
                            ${incomeData.memberFilter && incomeData.memberFilter != 0
        ? ' JOIN "DeliveryPeople" AS "memberDetails" ON "DeliveryRequests"."id" = "memberDetails"."DeliveryId" AND "memberDetails"."isDeleted" = false AND "memberDetails"."isActive" = true JOIN "Members" AS "memberDetails->Member" ON "memberDetails"."MemberId" = "memberDetails->Member"."id" JOIN "Users" AS "memberDetails->Member->User" ON "memberDetails->Member"."UserId" = "memberDetails->Member->User"."id"'
        : ''
      } 
                            ${incomeData.companyFilter && incomeData.companyFilter != 0
        ? ' JOIN "DeliverCompanies" AS "companyDetails" ON "DeliveryRequests"."id" = "companyDetails"."DeliveryId" AND "companyDetails"."isDeleted" = false  JOIN "Companies" AS "companyDetails->Company" ON "companyDetails"."CompanyId" = "companyDetails->Company"."id"'
        : ''
      } 
                            LEFT OUTER JOIN "Projects" AS "Project" ON "DeliveryRequests"."ProjectId" = "Project"."id" 
                            LEFT OUTER JOIN "Members" AS "createdUserDetails" ON "DeliveryRequests"."createdBy" = "createdUserDetails"."id" 
                            LEFT OUTER JOIN "Users" AS "createdUserDetails->User" ON "createdUserDetails"."UserId" = "createdUserDetails->User"."id" 
                            LEFT OUTER JOIN "Members" AS "approverDetails" ON "DeliveryRequests"."approvedBy" = "approverDetails"."id" 
                            LEFT OUTER JOIN "Users" AS "approverDetails->User" ON "approverDetails"."UserId" = "approverDetails->User"."id" 
                            ${incomeData.gateFilter && incomeData.gateFilter != 0
        ? ' JOIN "DeliverGates" AS "gateDetails" ON "DeliveryRequests"."id" = "gateDetails"."DeliveryId" AND "gateDetails"."isDeleted" = false AND "gateDetails"."isActive" = true JOIN "Gates" AS "gateDetails->Gate" ON "gateDetails"."GateId" = "gateDetails->Gate"."id"'
        : ''
      } 
                            ${incomeData.equipmentFilter && incomeData.equipmentFilter != 0
        ? ' JOIN "DeliverEquipments" AS "equipmentDetails" ON "DeliveryRequests"."id" = "equipmentDetails"."DeliveryId" AND "equipmentDetails"."isDeleted" = false AND "equipmentDetails"."isActive" = true JOIN "Equipments" AS "equipmentDetails->Equipment" ON "equipmentDetails"."EquipmentId" = "equipmentDetails->Equipment"."id"'
        : ''
      }  
                            ${incomeData.defineFilter && incomeData.defineFilter != 0
        ? ' JOIN "DeliverDefines" AS "defineWorkDetails" ON "DeliveryRequests"."id" = "defineWorkDetails"."DeliveryId" AND "defineWorkDetails"."isDeleted" = false JOIN "DeliverDefineWorks" AS "defineWorkDetails->DeliverDefineWork" ON "defineWorkDetails"."DeliverDefineWorkId" = "defineWorkDetails->DeliverDefineWork"."id" '
        : ''
      }
                           WHERE
                                "DeliveryRequests"."ProjectId" = :project_id 
                                AND "DeliveryRequests"."isQueued" = false 
                                AND "DeliveryRequests"."isDeleted" = false 
                                AND "DeliveryRequests"."id" not in (select "VoidLists"."DeliveryRequestId" from "VoidLists" WHERE "VoidLists"."DeliveryRequestId" is not NULL ) 
                                ${deliveryInCrane
        ? ' AND "DeliveryRequests"."requestType" = \'deliveryRequestWithCrane\''
        : ''
      } `;

    if (incomeData.startDate && incomeData.endDate) {
      if (singleQuery) {
        whereClause += ` and (DATE_TRUNC('day', "DeliveryRequests"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate}' AND '${endDate}'
        AND("DeliveryRequests"."deliveryStart" AT TIME ZONE '${timezone}'):: time >= '${startTime}'
        AND("DeliveryRequests"."deliveryStart" AT TIME ZONE '${timezone}'):: time <= '${endTime}') `;
      } else {
        whereClause += ` and (DATE_TRUNC('day', "DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate}' AND '${endDate}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time >= '${startTime}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time <= '23:59:59') or 
                                    (DATE_TRUNC('day', "DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${queryStartDate}' AND '${endDate}'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time >= '00:00:00'
                                    AND ("DeliveryRequest"."deliveryStart" AT TIME ZONE '${timezone}')::time <=  '${endTime}') `;
      }
    }

    if (incomeData.gateFilter && incomeData.gateFilter != 0) {
      whereClause += ` and "gateDetails->Gate"."id" = :gate `;
    }

    if (incomeData.equipmentFilter && incomeData.equipmentFilter != 0) {
      whereClause += ` and "equipmentDetails->Equipment"."id" = :equipment  `;
    }

    if (incomeData.defineFilter && incomeData.defineFilter != 0) {
      whereClause += ` and "defineWorkDetails->DeliverDefineWork"."id" = :define `;
    }

    if (incomeData.memberFilter && incomeData.memberFilter != 0) {
      whereClause += ` and "memberDetails->Member"."id" = :member `;
    }

    if (incomeData.companyFilter && incomeData.companyFilter != 0) {
      whereClause += ` and "companyDetails->Company"."id" = :company  `;
    }

    if (
      incomeData.statusFilter &&
      incomeData.statusFilter !== '' &&
      incomeData.statusFilter !== 0
    ) {
      whereClause += ` and "DeliveryRequests"."status" = :status `;
    }
  } else if (qType == 1) {
    const IDs = incomeData.templateType.map((d) => parseInt(d.id));
    const isDelivery_Crane_Combained = IDs.includes(0) && IDs.includes(1);
    if (isDelivery_Crane_Combained) {
      mainQuery = ` select
                                  (
                                  date_trunc('hour', "CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}' )::timestamp
                                  ) AS "hour_start"
                              FROM
                                  public."CraneRequests"
                                  ${incomeData.memberFilter && incomeData.memberFilter != 0
          ? ' JOIN "CraneRequestResponsiblePeople" AS "memberDetails" ON "CraneRequests"."id" = "memberDetails"."CraneRequestId" AND "memberDetails"."isDeleted" = false AND "memberDetails"."isActive" = true  JOIN "Members" AS "memberDetails->Member" ON "memberDetails"."MemberId" = "memberDetails->Member"."id" JOIN "Users" AS "memberDetails->Member->User" ON "memberDetails->Member"."UserId" = "memberDetails->Member->User"."id"'
          : ''
        } 
                                  ${incomeData.companyFilter && incomeData.companyFilter != 0
          ? ' JOIN "CraneRequestCompanies" AS "companyDetails" ON "CraneRequests"."id" = "companyDetails"."CraneRequestId" AND "companyDetails"."isDeleted" = false JOIN "Companies" AS "companyDetails->Company" ON "companyDetails"."CompanyId" = "companyDetails->Company"."id"'
          : ''
        } 
                                  LEFT OUTER JOIN "Projects" AS "Project" ON "CraneRequests"."ProjectId" = "Project"."id" 
                                  LEFT OUTER JOIN "Members" AS "createdUserDetails" ON "CraneRequests"."createdBy" = "createdUserDetails"."id" 
                                  LEFT OUTER JOIN "Users" AS "createdUserDetails->User" ON "createdUserDetails"."UserId" = "createdUserDetails->User"."id" 
                                  LEFT OUTER JOIN "Members" AS "approverDetails" ON "CraneRequests"."approvedBy" = "approverDetails"."id" 
                                  LEFT OUTER JOIN "Users" AS "approverDetails->User" ON "approverDetails"."UserId" = "approverDetails->User"."id"  
                                  ${incomeData.defineFilter && incomeData.defineFilter != 0
          ? ' JOIN "CraneRequestDefinableFeatureOfWorks" AS "defineWorkDetails" ON "CraneRequests"."id" = "defineWorkDetails"."CraneRequestId" AND "defineWorkDetails"."isDeleted" = false JOIN "DeliverDefineWorks" AS "defineWorkDetails->DeliverDefineWork" ON "defineWorkDetails"."DeliverDefineWorkId" = "defineWorkDetails->DeliverDefineWork"."id" '
          : ''
        }
                                  ${incomeData.equipmentFilter && incomeData.equipmentFilter != 0
          ? ' JOIN "CraneRequestEquipments" AS "equipmentDetails" ON "CraneRequests"."id" = "equipmentDetails"."CraneRequestId" AND "equipmentDetails"."isDeleted" = false AND "equipmentDetails"."isActive" = true JOIN (  "Equipments" AS "equipmentDetails->Equipment"   INNER JOIN "PresetEquipmentTypes" AS "equipmentDetails->Equipment->PresetEquipmentType" ON "equipmentDetails->Equipment"."PresetEquipmentTypeId" = "equipmentDetails->Equipment->PresetEquipmentType"."id" AND "equipmentDetails->Equipment->PresetEquipmentType"."isDeleted" = false   AND "equipmentDetails->Equipment->PresetEquipmentType"."isActive" = true   AND "equipmentDetails->Equipment->PresetEquipmentType"."isCraneType" = true) ON "equipmentDetails"."EquipmentId" = "equipmentDetails->Equipment"."id"'
          : ''
        } 
                                    WHERE 
                                  "CraneRequests"."ProjectId" = :project_id 
                                  AND "CraneRequests"."isDeleted" = false 
                                  AND "CraneRequests"."id" NOT IN (SELECT "VoidLists"."CraneRequestId" FROM "VoidLists" WHERE "VoidLists"."CraneRequestId" IS NOT NULL) `;

      if (incomeData.startDate && incomeData.endDate) {
        if (singleQuery) {
          whereClause += ` and (DATE_TRUNC('day', "CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate}' AND '${endDate}'
            AND("CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}'):: time >= '${startTime}'
            AND("CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}'):: time <= '${endTime}') `;
        } else {
          whereClause += ` and (DATE_TRUNC('day', "CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate}' AND '${endDate}'
                                      AND ("CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time >= '${startTime}'
                                      AND ("CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time <= '23:59:59') or 
                                      (DATE_TRUNC('day', "CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${queryStartDate}' AND '${endDate}'
                                      AND ("CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time >= '00:00:00'
                                      AND ("CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time <=  '${endTime}') `;
        }
      }

      if (incomeData.equipmentFilter && incomeData.equipmentFilter != 0) {
        whereClause += ` and "equipmentDetails->Equipment"."id" = :equipment  `;
      }

      if (incomeData.defineFilter && incomeData.defineFilter != 0) {
        whereClause += ` and "defineWorkDetails->DeliverDefineWork"."id" = :define `;
      }

      if (incomeData.memberFilter && incomeData.memberFilter != 0) {
        whereClause += ` and "memberDetails->Member"."id" = :member `;
      }

      if (incomeData.companyFilter && incomeData.companyFilter != 0) {
        whereClause += ` and "companyDetails->Company"."id" = :company  `;
      }

      if (
        incomeData.statusFilter &&
        incomeData.statusFilter !== '' &&
        incomeData.statusFilter !== 0
      ) {
        whereClause += ` and "CraneRequests"."status" = :status_fix `;
      }
    } else {
      mainQuery = ` ${mainQueryuilder(0, incomeData, true)} union all select
                                  (
                                  date_trunc('hour', "CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}' )::timestamp
                                  ) AS "hour_start"
                              FROM
                                  public."CraneRequests"
                                  ${incomeData.memberFilter && incomeData.memberFilter != 0
          ? ' JOIN "CraneRequestResponsiblePeople" AS "memberDetails" ON "CraneRequests"."id" = "memberDetails"."CraneRequestId" AND "memberDetails"."isDeleted" = false AND "memberDetails"."isActive" = true  JOIN "Members" AS "memberDetails->Member" ON "memberDetails"."MemberId" = "memberDetails->Member"."id" JOIN "Users" AS "memberDetails->Member->User" ON "memberDetails->Member"."UserId" = "memberDetails->Member->User"."id"'
          : ''
        } 
                                  ${incomeData.companyFilter && incomeData.companyFilter != 0
          ? ' JOIN "CraneRequestCompanies" AS "companyDetails" ON "CraneRequests"."id" = "companyDetails"."CraneRequestId" AND "companyDetails"."isDeleted" = false JOIN "Companies" AS "companyDetails->Company" ON "companyDetails"."CompanyId" = "companyDetails->Company"."id"'
          : ''
        } 
                                  LEFT OUTER JOIN "Projects" AS "Project" ON "CraneRequests"."ProjectId" = "Project"."id" 
                                  LEFT OUTER JOIN "Members" AS "createdUserDetails" ON "CraneRequests"."createdBy" = "createdUserDetails"."id" 
                                  LEFT OUTER JOIN "Users" AS "createdUserDetails->User" ON "createdUserDetails"."UserId" = "createdUserDetails->User"."id" 
                                  LEFT OUTER JOIN "Members" AS "approverDetails" ON "CraneRequests"."approvedBy" = "approverDetails"."id" 
                                  LEFT OUTER JOIN "Users" AS "approverDetails->User" ON "approverDetails"."UserId" = "approverDetails->User"."id"  
                                  ${incomeData.defineFilter && incomeData.defineFilter != 0
          ? ' JOIN "CraneRequestDefinableFeatureOfWorks" AS "defineWorkDetails" ON "CraneRequests"."id" = "defineWorkDetails"."CraneRequestId" AND "defineWorkDetails"."isDeleted" = false JOIN "DeliverDefineWorks" AS "defineWorkDetails->DeliverDefineWork" ON "defineWorkDetails"."DeliverDefineWorkId" = "defineWorkDetails->DeliverDefineWork"."id" '
          : ''
        }
                                  ${incomeData.equipmentFilter && incomeData.equipmentFilter != 0
          ? ' JOIN "CraneRequestEquipments" AS "equipmentDetails" ON "CraneRequests"."id" = "equipmentDetails"."CraneRequestId" AND "equipmentDetails"."isDeleted" = false AND "equipmentDetails"."isActive" = true JOIN (  "Equipments" AS "equipmentDetails->Equipment"   INNER JOIN "PresetEquipmentTypes" AS "equipmentDetails->Equipment->PresetEquipmentType" ON "equipmentDetails->Equipment"."PresetEquipmentTypeId" = "equipmentDetails->Equipment->PresetEquipmentType"."id" AND "equipmentDetails->Equipment->PresetEquipmentType"."isDeleted" = false   AND "equipmentDetails->Equipment->PresetEquipmentType"."isActive" = true   AND "equipmentDetails->Equipment->PresetEquipmentType"."isCraneType" = true) ON "equipmentDetails"."EquipmentId" = "equipmentDetails->Equipment"."id"'
          : ''
        } 
                              WHERE
                                  "CraneRequests"."ProjectId" = :project_id 
                                  AND "CraneRequests"."isDeleted" = false 
                                  AND "CraneRequests"."id" NOT IN (SELECT "VoidLists"."CraneRequestId" FROM "VoidLists" WHERE "VoidLists"."CraneRequestId" IS NOT NULL) `;
      if (incomeData.startDate && incomeData.endDate) {
        if (singleQuery) {
          whereClause += ` and (DATE_TRUNC('day', "CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate}' AND '${endDate}'
            AND("CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}'):: time >= '${startTime}'
            AND("CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}'):: time <= '${endTime}') `;
        } else {
          whereClause += ` and (DATE_TRUNC('day', "CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate}' AND '${endDate}'
                                      AND ("CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time >= '${startTime}'
                                      AND ("CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time <= '23:59:59') or 
                                      (DATE_TRUNC('day', "CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${queryStartDate}' AND '${endDate}'
                                      AND ("CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time >= '00:00:00'
                                      AND ("CraneRequests"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time <=  '${endTime}') `;
        }
      }

      if (incomeData.equipmentFilter && incomeData.equipmentFilter != 0) {
        whereClause += ` and "equipmentDetails->Equipment"."id" = :equipment  `;
      }

      if (incomeData.defineFilter && incomeData.defineFilter != 0) {
        whereClause += ` and "defineWorkDetails->DeliverDefineWork"."id" = :define `;
      }

      if (incomeData.memberFilter && incomeData.memberFilter != 0) {
        whereClause += ` and "memberDetails->Member"."id" = :member `;
      }

      if (incomeData.companyFilter && incomeData.companyFilter != 0) {
        whereClause += ` and "companyDetails->Company"."id" = :company  `;
      }

      if (
        incomeData.statusFilter &&
        incomeData.statusFilter !== '' &&
        incomeData.statusFilter !== 0
      ) {
        whereClause += ` and "CraneRequests"."status" = :status_fix `;
      }
    }
  } else if (qType == 2) {
    mainQuery = ` select
                                (
                                date_trunc('hour', "ConcreteRequests"."concretePlacementStart" AT TIME ZONE '${timezone}' )::timestamp
                                ) AS "hour_start"
                                    FROM
                                public."ConcreteRequests"
                                ${incomeData.memberFilter && incomeData.memberFilter != 0
        ? ' JOIN ("ConcreteRequestResponsiblePeople" AS "memberDetails" INNER JOIN "Members" AS "memberDetails->Member" ON "memberDetails"."MemberId" = "memberDetails->Member"."id" AND "memberDetails->Member"."isDeleted" = false INNER JOIN "Users" AS "memberDetails->Member->User" ON "memberDetails->Member"."UserId" = "memberDetails->Member->User"."id" AND "memberDetails->Member->User"."isDeleted" = false) ON "ConcreteRequests"."id" = "memberDetails"."ConcreteRequestId" AND "memberDetails"."isDeleted" = false AND "memberDetails"."isActive" = true'
        : ''
      } 
                              INNER JOIN "Projects" AS "Project" ON "ConcreteRequests"."ProjectId" = "Project"."id" 
                              AND "Project"."isDeleted" = false 
                              LEFT OUTER JOIN (
                                "Members" AS "createdUserDetails" 
                                INNER JOIN "Users" AS "createdUserDetails->User" ON "createdUserDetails"."UserId" = "createdUserDetails->User"."id" 
                                AND "createdUserDetails->User"."isDeleted" = false
                              ) ON "ConcreteRequests"."createdBy" = "createdUserDetails"."id" 
                              LEFT OUTER JOIN "ConcreteRequestLocations" AS "locationDetails" ON "ConcreteRequests"."id" = "locationDetails"."ConcreteRequestId" 
                              AND "locationDetails"."isDeleted" = false 
                              LEFT OUTER JOIN "ConcreteLocations" AS "locationDetails->ConcreteLocation" ON "locationDetails"."ConcreteLocationId" = "locationDetails->ConcreteLocation"."id" 
                              LEFT OUTER JOIN "ConcreteRequestMixDesigns" AS "mixDesignDetails" ON "ConcreteRequests"."id" = "mixDesignDetails"."ConcreteRequestId" 
                              AND "mixDesignDetails"."isDeleted" = false 
                              LEFT OUTER JOIN "ConcreteMixDesigns" AS "mixDesignDetails->ConcreteMixDesign" ON "mixDesignDetails"."ConcreteMixDesignId" = "mixDesignDetails->ConcreteMixDesign"."id" 
                              LEFT OUTER JOIN "ConcreteRequestPumpSizes" AS "pumpSizeDetails" ON "ConcreteRequests"."id" = "pumpSizeDetails"."ConcreteRequestId" 
                              AND "pumpSizeDetails"."isDeleted" = false 
                              LEFT OUTER JOIN "ConcretePumpSizes" AS "pumpSizeDetails->ConcretePumpSize" ON "pumpSizeDetails"."ConcretePumpSizeId" = "pumpSizeDetails->ConcretePumpSize"."id" 
                              ${incomeData.companyFilter && incomeData.companyFilter != 0
        ? ' JOIN "ConcreteRequestCompanies" AS "concreteSupplierDetails" ON "ConcreteRequests"."id" = "concreteSupplierDetails"."ConcreteRequestId" AND "concreteSupplierDetails"."isDeleted" = false JOIN "Companies" AS "concreteSupplierDetails->Company" ON "concreteSupplierDetails"."CompanyId" = "concreteSupplierDetails->Company"."id"'
        : ''
      } 
                              LEFT OUTER JOIN "Members" AS "approverDetails" ON "ConcreteRequests"."approvedBy" = "approverDetails"."id" 
                              LEFT OUTER JOIN "Users" AS "approverDetails->User" ON "approverDetails"."UserId" = "approverDetails->User"."id"
                            WHERE
                                "ConcreteRequests"."ProjectId" = :project_id 
                                AND "ConcreteRequests"."isDeleted" = false 
                                AND "ConcreteRequests"."id" NOT IN (SELECT "VoidLists"."ConcreteRequestId" FROM "VoidLists" WHERE "VoidLists"."ConcreteRequestId" IS NOT NULL ) `;

    if (incomeData.startDate && incomeData.endDate) {
      if (singleQuery) {
        whereClause += ` and (DATE_TRUNC('day', "ConcreteRequests"."concretePlacementStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate}' AND '${endDate}'
          AND("ConcreteRequests"."concretePlacementStart" AT TIME ZONE '${timezone}'):: time >= '${startTime}'
          AND("ConcreteRequests"."concretePlacementStart" AT TIME ZONE '${timezone}'):: time <= '${endTime}') `;
      } else {
        whereClause += ` and (DATE_TRUNC('day', ""ConcreteRequests"."concretePlacementStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate}' AND '${endDate}'
                                    AND ("ConcreteRequests"."concretePlacementStart" AT TIME ZONE '${timezone}')::time >= '${startTime}'
                                    AND ("ConcreteRequests"."concretePlacementStart" AT TIME ZONE '${timezone}')::time <= '23:59:59') or 
                                    (DATE_TRUNC('day', "ConcreteRequests"."concretePlacementStart" AT TIME ZONE '${timezone}') BETWEEN '${queryStartDate}' AND '${endDate}'
                                    AND ("ConcreteRequests"."concretePlacementStart" AT TIME ZONE '${timezone}')::time >= '00:00:00'
                                    AND ("ConcreteRequests"."concretePlacementStart" AT TIME ZONE '${timezone}')::time <=  '${endTime}') `;
      }
    }

    if (incomeData.memberFilter && incomeData.memberFilter != 0) {
      whereClause += ` and "memberDetails->Member"."id" = :member `;
    }

    if (incomeData.companyFilter && incomeData.companyFilter != 0) {
      whereClause += ` and "concreteSupplierDetails->Company"."id" = :company  `;
    }

    if (
      incomeData.statusFilter &&
      incomeData.statusFilter !== '' &&
      incomeData.statusFilter !== 0
    ) {
      whereClause += ` and "ConcreteRequests"."status" = :status_fix `;
    }
  } else {
    throw new Error('please send valid template type');
  }

  return mainQuery.concat(whereClause);
};
const queryBuilderExternal = async (inputData, incomeData, params) => {
  let mainQuery = ``;
  const whereClause = ``;
  const baseQuery = ` WITH time_slots AS ( `;
  const orderGropuQuery = `)
                            select
                            to_char("hour_start", 'Month DD,YYYY') as "Date", to_char("hour_start", 'HH12 PM') AS "Time", count(to_char(hour_start, 'HH12 PM')) as "Count"
                            FROM
                            time_slots group by "Date", "Time"    ORDER BY "Date", "Time" ${params.sortOrder};`;

  if (incomeData.templateType.length === 1) {
    // 0 for Delivery Request Heat map Only
    if (incomeData.templateType.find((ele) => ele.id == 0)) {
      mainQuery = mainQueryuilder(0, incomeData);
      // 1 for Crane Request Heat map Only
    } else if (incomeData.templateType.find((ele) => ele.id == 1)) {
      mainQuery = mainQueryuilder(1, incomeData);
      // 2 for Concrete Request Heat map Only
    } else if (incomeData.templateType.find((ele) => ele.id == 2)) {
      mainQuery = mainQueryuilder(2, incomeData);
    } else {
      throw new Error('please send valid template type');
    }
  } else if (incomeData.templateType.length > 1 && incomeData.templateType.length < 4) {
    _.forEach(incomeData.templateType, (d, i) => {
      if (i !== incomeData.templateType.length - 1) {
        if (
          (d.id == 1 && incomeData.gateFilter != 0) ||
          (d.id == 2 &&
            (incomeData.gateFilter != 0 ||
              incomeData.equipmentFilter != 0 ||
              incomeData.defineFilter != 0))
        ) {
          return;
        }
        if (
          i == incomeData.templateType.length - 2 &&
          ((incomeData.templateType[incomeData.templateType.length - 1].id == 1 &&
            incomeData.gateFilter != 0) ||
            (incomeData.templateType[incomeData.templateType.length - 1].id == 2 &&
              (incomeData.gateFilter != 0 ||
                incomeData.equipmentFilter != 0 ||
                incomeData.defineFilter != 0)))
        ) {
          mainQuery += `${mainQueryuilder(d.id, incomeData)} `;
          return;
        }
        mainQuery += `${mainQueryuilder(d.id, incomeData)} union all `;
      } else {
        if (
          (d.id == 1 && incomeData.gateFilter != 0) ||
          (d.id == 2 &&
            (incomeData.gateFilter != 0 ||
              incomeData.equipmentFilter != 0 ||
              incomeData.defineFilter != 0))
        ) {
          return;
        }
        mainQuery += mainQueryuilder(d.id, incomeData);
      }
    });
  } else {
    throw new Error('please send valid template type or please check template type length');
  }

  const OutQuery = baseQuery.concat(mainQuery).concat(whereClause).concat(orderGropuQuery);

  return OutQuery;
};

const convertToCron = (dateTime) => {
  const date = moment(dateTime);
  return `${0} ${date.minute()} ${date.hour()} ${date.date()} ${date.month() + 1} ` + `*`;
};

const convertToCronYearly = (order, day, month, hour, minute) => {
  let orderValue;
  let dayValue;
  switch (order) {
    case 'first':
      orderValue = 1;
      break;
    case 'second':
      orderValue = 2;
      break;
    case 'third':
      orderValue = 3;
      break;
    case 'fourth':
      orderValue = 4;
      break;
    case 'last':
      orderValue = 'L';
      break;
    default:
      throw new Error('Order is wrong for montly');
  }
  switch (day) {
    case 'SUN':
      dayValue = 0;
      break;
    case 'MON':
      dayValue = 1;
      break;
    case 'TUE':
      dayValue = 2;
      break;
    case 'WED':
      dayValue = 3;
      break;
    case 'THU':
      dayValue = 4;
      break;
    case 'FRI':
      dayValue = 5;
      break;
    case 'SAT':
      dayValue = 6;
      break;
    default:
      throw new Error('Order is wrong for montly');
  }
  return order == 'last'
    ? `${minute} ${hour} * ${month} ${dayValue}${orderValue}`
    : `${minute} ${hour} * ${month} ${dayValue}#${orderValue}`;
};

const convertToCronMonthly = (order, day, every, hour, minute) => {
  let orderValue;
  let dayValue;
  switch (order) {
    case 'first':
      orderValue = 1;
      break;
    case 'second':
      orderValue = 2;
      break;
    case 'third':
      orderValue = 3;
      break;
    case 'fourth':
      orderValue = 4;
      break;
    case 'last':
      orderValue = 'L';
      break;
    default:
      throw new Error('Order is wrong for montly');
  }
  switch (day) {
    case 'SUN':
      dayValue = 0;
      break;
    case 'MON':
      dayValue = 1;
      break;
    case 'TUE':
      dayValue = 2;
      break;
    case 'WED':
      dayValue = 3;
      break;
    case 'THU':
      dayValue = 4;
      break;
    case 'FRI':
      dayValue = 5;
      break;
    case 'SAT':
      dayValue = 6;
      break;
    default:
      throw new Error('Order is wrong for montly');
  }
  return order == 'last'
    ? `${minute} ${hour} * */${every} ${dayValue}${orderValue}`
    : `${minute} ${hour} * */${every} ${dayValue}#${orderValue}`;
};
const replacementsBuilderExternal = async (inputData, incomeData, params) => {
  const offset = (+params.pageNo - 1) * +params.pageSize;
  let replacements = { project_id: +params.ProjectId, offset, limit: +params.pageSize };
  if (incomeData.startdate && incomeData.enddate) {
    const startDateTime = moment(incomeData.startdate, 'YYYY-MM-DD')
      .startOf('day')
      .utcOffset(Number(inputData.headers.timezoneoffset), true);
    const endDateTime = moment(incomeData.enddate, 'YYYY-MM-DD')
      .endOf('day')
      .utcOffset(Number(inputData.headers.timezoneoffset), true);
    replacements = {
      ...replacements,
      start_date: moment(startDateTime).format(),
      end_date: moment(endDateTime).format(),
    };
  }
  if (incomeData.statusFilter && incomeData.statusFilter !== '' && incomeData.statusFilter !== 0) {
    replacements = {
      ...replacements,
      status: incomeData.statusFilter,
      status_fix: incomeData.statusFilter == 'Delivered' ? 'Completed' : incomeData.statusFilter,
    };
  }
  if (incomeData.gateFilter && incomeData.gateFilter != 0) {
    replacements = { ...replacements, gate: incomeData.gateFilter };
  }

  if (incomeData.equipmentFilter && incomeData.equipmentFilter != 0) {
    replacements = { ...replacements, equipment: incomeData.equipmentFilter };
  }

  if (incomeData.defineFilter && incomeData.defineFilter != 0) {
    replacements = { ...replacements, define: incomeData.defineFilter };
  }

  if (incomeData.memberFilter && incomeData.memberFilter != 0) {
    replacements = { ...replacements, member: incomeData.memberFilter };
  }

  if (incomeData.companyFilter && incomeData.companyFilter != 0) {
    replacements = { ...replacements, company: incomeData.companyFilter };
  }
  return replacements;
};

const defaultTimeSlots = () => {
  const baseObj = {};
  const timeslots = [
    '01 AM',
    '02 AM',
    '03 AM',
    '04 AM',
    '05 AM',
    '06 AM',
    '07 AM',
    '08 AM',
    '09 AM',
    '10 AM',
    '11 AM',
    '12 PM',
    '01 PM',
    '02 PM',
    '03 PM',
    '04 PM',
    '05 PM',
    '06 PM',
    '07 PM',
    '08 PM',
    '09 PM',
    '10 PM',
    '11 PM',
    '12 AM',
  ];
  _.forEach(timeslots, (d) => (baseObj[d] = 0));
  return baseObj;
};

module.exports = {
  queryBuilderExternal,
  replacementsBuilderExternal,
  defaultTimeSlots,
  convertToCron,
  convertToCronMonthly,
  convertToCronYearly,
};
