const moment = require('moment');
const { Sequelize } = require('sequelize');

const { Op } = Sequelize;
module.exports = (sequelize, DataTypes) => {
  const SchedulerReport = sequelize.define('SchedulerReport', {
    reportName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    reportType: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    ProjectId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM,
      values: ['Pending', 'Approved', 'Declined', 'Delivered', 'Expired'],
      allowNull: true,
    },
    outputFormat: {
      type: DataTypes.ENUM,
      values: ['PDF', 'EXCEL', 'CSV'],
      allowNull: false,
    },
    runReportAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    recurrence: {
      type: DataTypes.ENUM,
      values: ['Does Not Repeat', 'Daily', 'Weekly', 'Monthly', 'Yearly'],
      allowNull: false,
    },
    repeatEvery: {
      type: DataTypes.TEXT,
      get() {
        return JSON.parse(
          this.getDataValue('repeatEvery') !== undefined &&
            this.getDataValue('repeatEvery') !== 'undefined'
            ? this.getDataValue('repeatEvery')
            : '{}',
        );
      },
      set(value) {
        this.setDataValue('repeatEvery', JSON.stringify(value));
      },
    },
    sendTo: {
      type: DataTypes.TEXT,
      get() {
        return this.getDataValue('sendTo') !== undefined &&
          this.getDataValue('sendTo') !== 'undefined'
          ? this.getDataValue('sendTo').split(',')
          : '';
      },
      set(value) {
        this.setDataValue('sendTo', value.join(','));
      },
    },
    subject: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
    },
    isSaved: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
    },
    createdBy: {
      type: DataTypes.INTEGER,
      references: {
        model: 'Users',
        key: 'id',
      },
      allowNull: false,
    },
    // memberBy: {
    //   type: DataTypes.INTEGER,
    //   references: {
    //     model: 'Members',
    //     key: 'id',
    //   },
    //   allowNull: false,
    // },
    lastRun: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    s3_url: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    cronExpression: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    timezone: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    startDate: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    endDate: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    recurrenceEndDate: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    defineId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    companyId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    equipmentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    gateId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    memberFilterId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    parentFilterCompanyId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    templateFilterType: {
      type: DataTypes.STRING,
      get() {
        return JSON.parse(
          this.getDataValue('templateFilterType') !== undefined &&
            this.getDataValue('templateFilterType') !== 'undefined'
            ? this.getDataValue('templateFilterType')
            : '{}',
        );
      },
      set(value) {
        this.setDataValue('templateFilterType', JSON.stringify(value));
      },
    },
    selectedHeaders: {
      type: DataTypes.STRING,
      get() {
        return JSON.parse(
          this.getDataValue('selectedHeaders') !== undefined &&
            this.getDataValue('selectedHeaders') !== 'undefined'
            ? this.getDataValue('selectedHeaders')
            : '{}',
        );
      },
      set(value) {
        this.setDataValue('selectedHeaders', JSON.stringify(value));
      },
      allowNull: true,
    },
    isEndDateMeet: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
    },
    idFilter: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    sort: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    sortByField: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    queuedNdr: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
    },
    pickFrom: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    pickTo: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    locationFilter: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    descriptionFilter: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    orderNumberFilter: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    mixDesignFilter: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    truckspacingFilter: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    slumpFilter: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    primerFilter: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    quantityFilter: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    companyFilter: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    equipmentFilter: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    dateRangeId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    customStartDate: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    customEndDate: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  });
  SchedulerReport.associate = (models) => {
    // associations can be defined here
    SchedulerReport.belongsTo(models.User, {
      as: 'createdUser',
      foreignKey: 'createdBy',
    });
    SchedulerReport.belongsTo(models.SchedulerDateRange, {
      as: 'dateRange',
      foreignKey: 'dateRangeId',
    });
    // SchedulerReport.belongsTo(models.Member, {
    //   as: 'createdUserDetails',
    //   foreignKey: 'memberBy',
    // });
  };
  SchedulerReport.createInstance = async (paramData) => {
    const newDeliveryRequest = await SchedulerReport.create(paramData);
    return newDeliveryRequest;
  };
  SchedulerReport.getAll = async (
    ProjectId,
    limit,
    offset,
    sortByField,
    sortByType,
    createdUserId,
    reportName,
    templateType,
    lastRun,
    search,
    timezone,
    isSaved,
    excludeSave = false,
  ) => {
    const sortByFieldName = sortByField || 'id';
    const sortByColumnType = sortByType || 'DESC';
    let orderQuery = [[`${sortByFieldName}`, `${sortByColumnType}`]];
    if (sortByFieldName === 'author') {
      orderQuery = [['createdUser', 'firstName', `${sortByColumnType}`]];
    }
    let commonSearch = {
      ProjectId,
      isDeleted: false,
    };
    if (!excludeSave) {
      commonSearch = { ...commonSearch, isSaved };
    }
    if (search) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              { reportName: { [Sequelize.Op.iLike]: `%${search}%` } },
              { reportType: { [Sequelize.Op.iLike]: `%${search}%` } },
              Sequelize.literal(
                `"SchedulerReport"."outputFormat"::text ILIKE '%${search.toLowerCase()}%'`,
              ),
              { '$createdUser.firstName$': { [Sequelize.Op.iLike]: `%${search}%` } },
              { '$createdUser.lastName$': { [Sequelize.Op.iLike]: `%${search}%` } },
            ],
          },
        ],
      };
    }
    if (reportName) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ reportName: { [Sequelize.Op.iLike]: `%${reportName}%` } }],
          },
        ],
      };
    }
    if (templateType) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ reportType: { [Sequelize.Op.iLike]: `%${templateType}%` } }],
          },
        ],
      };
    }
    if (createdUserId) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ '$createdUser.id$': createdUserId }],
          },
        ],
      };
    }
    if (lastRun) {
      const startOfDay = moment.tz(lastRun, timezone).startOf('day').utc();
      const endOfDay = moment.tz(lastRun, timezone).endOf('day').utc();
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                lastRun: {
                  [Op.between]: [startOfDay, endOfDay],
                },
              },
            ],
          },
        ],
      };
    }
    const scheduledData = await SchedulerReport.findAll({
      include: [
        { association: 'createdUser', attributes: ['id', 'email', 'firstName', 'lastName'] },
      ],
      attributes: [
        'id',
        'reportName',
        'reportType',
        'lastRun',
        's3_url',
        'outputFormat',
        'isSaved',
        'createdBy',
      ],
      where: commonSearch,
      limit,
      offset,
      order: orderQuery,
    });
    const count = await SchedulerReport.count({
      include: [
        { association: 'createdUser', attributes: ['id', 'email', 'firstName', 'lastName'] },
      ],
      where: commonSearch,
    });
    return { scheduledData, count };
  };

  SchedulerReport.getAllList = async (condition, sortByField, sortByType) => {
    const sortByFieldName = sortByField || 'id';
    const sortByColumnType = sortByType || 'DESC';
    const orderQuery = [[`${sortByFieldName}`, `${sortByColumnType}`]];
    const scheduledData = await SchedulerReport.findAll({
      include: [
        { association: 'createdUser', attributes: ['id', 'email', 'firstName', 'lastName'] },
      ],
      attributes: [
        'id',
        'reportName',
        'reportType',
        'lastRun',
        's3_url',
        'outputFormat',
        'isSaved',
        'createdBy',
      ],
      where: condition,
      order: orderQuery,
    });
    return scheduledData;
  };
  SchedulerReport.updateInstance = async (id, args) => {
    const updatedDeliveryRequest = await SchedulerReport.update(args, { where: { id } });
    return updatedDeliveryRequest;
  };
  return SchedulerReport;
};
