const { Router } = require('express');
const { validate } = require('express-validation');
const passportConfig = require('../config/passport');
const { concreteRequestHistoryController } = require('../controllers');
const { concreteRequestHistoryValidation } = require('../middlewares/validations');

const commentRoute = {
  get router() {
    const router = Router();
    router.get(
      '/get_concrete_request_histories/:ConcreteRequestId/?:ParentCompanyId/:ProjectId',
      validate(
        concreteRequestHistoryValidation.getConcreteRequestHistories,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      concreteRequestHistoryController.getConcreteRequestHistories,
    );
    return router;
  },
};
module.exports = commentRoute;
