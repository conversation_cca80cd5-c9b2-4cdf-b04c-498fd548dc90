const { Router } = require('express');
const { validate } = require('express-validation');
const { VoidController } = require('../controllers');
const passportConfig = require('../config/passport');
const { voidValidation } = require('../middlewares/validations');

const userRoute = {
  get router() {
    const router = Router();
    router.post(
      '/create_void',
      validate(voidValidation.createVoidList, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      VoidController.createVoidList,
    );
    router.post(
      '/remove_void',
      validate(voidValidation.removeVoidList, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      VoidController.removeVoidList,
    );
    router.post(
      '/add_crane_request_to_void',
      validate(
        voidValidation.createCraneRequestVoidList,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      VoidController.createCraneRequestVoid,
    );
    router.post(
      '/add_concrete_request_to_void',
      validate(
        voidValidation.createConcreteRequestVoidList,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      VoidController.createConcreteRequestVoid,
    );
    router.post(
      '/get_void_list/:ProjectId/:pageSize/:pageNo/:void',
      passportConfig.isAuthenticated,
      VoidController.getVoidList,
    );
    router.post(
      '/createMultiple_void',
      validate(voidValidation.createVoidList, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      VoidController.createMultipleVoidList,
    );
    return router;
  },
};

module.exports = userRoute;
