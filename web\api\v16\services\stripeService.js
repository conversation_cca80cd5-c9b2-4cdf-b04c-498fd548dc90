const stripe = require('stripe')(process.env.STRIPE_API_KEY, {
  maxNetworkRetries: 3,
});
const express = require('express');
const moment = require('moment');
const { Router } = require('express');
const {
  StripePlan,
  User,
  StripeSubscription,
  Sequelize,
  Plan,
  Project,
  Member,
} = require('../models');

const router = Router();

const stripeService = {
  async planList(input, done) {
    try {
      let condition = Sequelize.or({ stripePlanName: ['free', 'enterprise', input.interval] });
      if (input.interval === 'all') {
        condition = {};
      }
      const stripePlans = await StripePlan.findAll({
        include: ['Plan'],
        where: condition,
      });
      done(stripePlans, null);
    } catch (e) {
      done(null, e);
    }
  },
  async upgradePlanList(input, done) {
    try {
      let condition = Sequelize.or({ stripePlanName: ['enterprise', input.interval] });
      if (input.interval === 'all') {
        condition = {};
      }
      const stripePlans = await StripePlan.findAll({
        include: ['Plan'],
        where: condition,
      });
      done(stripePlans, null);
    } catch (e) {
      done(null, e);
    }
  },
  async addNewCard(inputData, done) {
    try {
      if (inputData.cardDetails === undefined) {
        done({ message: 'Please enter card details.' }, null);
      } else {
        const { email } = inputData.basicDetails;
        const name = `${inputData.companyDetails.fullName} ${inputData.companyDetails.lastName}`;
        const { companyDetails } = inputData;
        const customer = await stripe.customers.create({
          email,
          name,
          phone: `${inputData.basicDetails.phoneCode} ${inputData.basicDetails.phoneNumber}`,
          address: {
            city: companyDetails.city,
            country: companyDetails.country,
            line1: companyDetails.address,
            postal_code: inputData.cardDetails.zipCode,
            state: companyDetails.state,
          },
        });
        // eslint-disable-next-line camelcase
        const { number, exp_month, exp_year, cvc } = inputData.cardDetails;
        const payment = await stripe.paymentMethods.create({
          type: 'card',
          card: {
            number,
            exp_month,
            exp_year,
            cvc,
          },
        });
        await stripe.paymentMethods.attach(payment.id, {
          customer: customer.id,
        });
        await stripe.customers.update(customer.id, {
          invoice_settings: { default_payment_method: payment.id },
        });
        const subData = {
          stripeCustomerId: customer.id,
          stripePlanId: inputData.planData.stripePlanId,
        };
        this.newSubscribe(subData, (result, err) => {
          if (!err) {
            const newResult = result;
            newResult.stripeCustomerId = customer.id;
            done(newResult, false);
          } else {
            done(null, err);
          }
        });
      }
    } catch (error) {
      done(null, { message: 'Invalid Card.' });
    }
  },
  async addCard(inputData, done) {
    try {
      const { id } = inputData.user;
      const user = await User.getBy({ id });
      const customerId = user.stripeCustomerId;
      let customer;
      if (customerId) {
        customer = await stripe.customers.retrieve(customerId);
      } else {
        const { email } = user;
        const name = `${user.firstName} ${user.lastName}`;
        customer = await stripe.customers.create({ email, name });
        await user.updateInstance(user.id, {
          stripeCustomerId: customer.id,
        });
      }
      if (inputData.body.existcard === true) {
        const subData = {
          stripeCustomerId: customer.id,
          stripePlanId: inputData.planData.stripePlanId,
          companyDet: inputData.CompanyDet,
          cardDetails: inputData.body.cardDetails,
          name: inputData.user.firstName,
        };
        this.subscribe(subData, user, (result, err) => {
          if (!err) {
            const newResult = result;
            newResult.stripeCustomerId = customer.id;
            done(newResult, false);
          } else {
            done(null, err);
          }
        });
      } else if (inputData.body.cardDetails !== undefined && inputData.body.cardDetails !== null) {
        // eslint-disable-next-line camelcase
        const { number, exp_month, exp_year, cvc } = inputData.body.cardDetails;
        const payment = await stripe.paymentMethods.create({
          type: 'card',
          card: {
            number,
            exp_month,
            exp_year,
            cvc,
          },
        });
        await stripe.paymentMethods.attach(payment.id, {
          customer: customer.id,
        });
        await stripe.customers.update(customer.id, {
          invoice_settings: { default_payment_method: payment.id },
        });
        const subData = {
          stripeCustomerId: customer.id,
          stripePlanId: inputData.planData.stripePlanId,
          companyDet: inputData.CompanyDet,
          cardDetails: inputData.body.cardDetails,
          name: inputData.user.firstName,
        };
        this.subscribe(subData, user, (result, err) => {
          if (!err) {
            const newResult = result;
            newResult.stripeCustomerId = customer.id;
            done(newResult, false);
          } else {
            done(null, err);
          }
        });
      } else {
        done(null, { message: 'Please Enter Card Details.' });
      }
    } catch (error) {
      done(null, error);
    }
  },
  async stripeProductcreate(productData, done) {
    try {
      const product = await stripe.products.create({
        name: productData.name,
        type: productData.type,
      });
      done(null, product);
    } catch (error) {
      done(error, null);
    }
  },
  async stripeAddProduct(inputData, done) {
    const existPlan = await Plan.getBy({ planType: inputData.product.name });
    let planDetails;
    if (!existPlan) {
      planDetails = await Plan.createInstance({ planType: inputData.product.name });
    } else {
      planDetails = existPlan;
    }
    const plan = await stripe.plans.create({
      nickname: inputData.plan.nickName,
      product: inputData.product.id,
      amount: inputData.plan.amount,
      currency: inputData.plan.currency,
      interval: inputData.plan.interval,
    });
    const planAttrs = {
      stripePlanName: plan.nickname,
      stripeProductName: inputData.product.name,
      stripeProductId: plan.product,
      stripeAmount: inputData.plan.amount,
      stripeCurrency: inputData.plan.currency,
      stripePlanId: plan.id,
      PlanId: planDetails.id,
    };
    const stripePlan = await StripePlan.createPlan(planAttrs);
    done(null, stripePlan);
  },
  async newSubscribe(inputData, done) {
    try {
      const customer = await stripe.customers.retrieve(inputData.stripeCustomerId);
      const plan = await StripePlan.getBy({ stripePlanId: inputData.stripePlanId });
      // You need to make sure that you always pass trial_end or trial_period_days
      // when you create the subscription instead.
      const subscription = await stripe.subscriptions.create({
        customer: customer.id,
        // Either charge_automatically, or send_invoice.When charging automatically, Stripe will attempt to pay this subscription at the end of the cycle using the default source attached to the customer.When sending an invoice, Stripe will email your customer an invoice with payment instructions.Defaults to charge_automatically.
        // collection_method: 'send_invoice',
        // days_until_due: 1,
        items: [{ plan: plan.stripePlanId }],
      });
      done(subscription, false);
    } catch (err) {
      done(null, err);
    }
  },
  async subscribe(inputData, user, done) {
    try {
      const companyDetails = inputData.companyDet;
      const plan = await StripePlan.getBy({ stripePlanId: inputData.stripePlanId });
      // You need to make sure that you always pass trial_end or trial_period_days
      // when you create the subscription instead.
      await stripe.customers.update(inputData.stripeCustomerId, {
        name: inputData.name,
        address: {
          city: companyDetails.city,
          country: companyDetails.country,
          line1: companyDetails.address,
          postal_code: companyDetails.zipCode,
          state: companyDetails.state,
        },
      });
      const customer = await stripe.customers.retrieve(inputData.stripeCustomerId);

      const subscription = await stripe.subscriptions.create({
        customer: customer.id,
        items: [{ plan: plan.stripePlanId }],
      });
      const newSubscription = await StripeSubscription.createInstance(user, subscription);
      done(newSubscription, false);
    } catch (err) {
      done(null, err);
    }
  },
  async cancelSubscription(inputData, done) {
    try {
      let id;
      if (inputData.params.ProjectId !== null && inputData.params.ProjectId !== undefined) {
        id = inputData.params.ProjectId;
      } else {
        id = inputData.body.ProjectId;
      }
      await this.checkProject(inputData, async (response, error) => {
        if (!error) {
          const projectDetail = await Project.findByPk(id);
          const stripeSubscription = await StripeSubscription.getBy({
            id: projectDetail.StripeSubscriptionId,
          });
          if (stripeSubscription && stripeSubscription.status !== 'inactive') {
            await stripe.subscriptions.del(stripeSubscription.subscriptionId);
            await Project.update({ status: 'canceled' }, { where: { id } });
            await stripeSubscription.updateInstance(stripeSubscription.id, { status: 'inactive' });
            done({ message: 'Subscription Canceled Successfully' }, false);
          } else {
            done(null, { message: 'You have not any Subscribed Plan' });
          }
        } else {
          done(null, error);
        }
      });
    } catch (error) {
      done(null, error);
    }
  },
  async checkProject(inputData, done) {
    let id;
    if (inputData.params.ProjectId !== null && inputData.params.ProjectId !== undefined) {
      id = inputData.params.ProjectId;
    } else {
      id = inputData.body.ProjectId;
    }
    const existMember = await Member.getBy({
      UserId: inputData.user.id,
      ProjectId: id,
      RoleId: 2,
    });
    if (existMember) {
      done({ status: true }, false);
    } else {
      done({ message: 'You are not exist member to this project' });
    }
  },
  async holdSubscription(inputData, done) {
    try {
      const id = inputData.params.ProjectId;
      await this.checkProject(inputData, async (response, error) => {
        if (!error) {
          const projectDetail = await Project.findByPk(id);
          const stripeSubscription = await StripeSubscription.getBy({
            id: projectDetail.StripeSubscriptionId,
          });
          if (projectDetail && projectDetail.status !== 'Holded') {
            if (stripeSubscription) {
              await stripe.subscriptions.update(stripeSubscription.subscriptionId, {
                pause_collection: {
                  behavior: 'void',
                },
              });
              const status = 'Holded';
              this.updateProject(stripeSubscription, status, id, (newResponse, errorValue) => {
                if (!errorValue) {
                  done({ message: 'Subscription Paused Successfully.' }, false);
                } else {
                  done(null, errorValue);
                }
              });
            } else {
              done(null, 'You have not any Subscribed Plan');
            }
          } else if (projectDetail && projectDetail.status === 'Holded') {
            if (stripeSubscription) {
              await stripe.subscriptions.update(stripeSubscription.subscriptionId, {
                pause_collection: '',
              });
              const status = 'Active';
              this.updateProject(stripeSubscription, status, id, (newResponse, errorValue) => {
                if (!errorValue) {
                  done({ message: 'Subscription Resumed Successfully.' }, false);
                } else {
                  done(null, errorValue);
                }
              });
            } else {
              done(null, 'You have not any Subscribed Plan');
            }
          }
        }
      });
    } catch (error) {
      done(null, error);
    }
  },
  async updateProject(stripeSubscription, status, id, done) {
    try {
      await Project.update({ status }, { where: { id } });
      await stripeSubscription.updateInstance(id, { status });
      done({ message: 'Subscription Holded Successfully' }, false);
    } catch (error) {
      done(null, error);
    }
  },
  async payOnline(inputData) {
    try {
      const user = await User.getBy({ id: inputData.user.id });
      // eslint-disable-next-line camelcase
      const { number, exp_month, exp_year, cvc } = inputData.body.cardDetails;
      const payment = await stripe.paymentMethods.create({
        type: 'card',
        card: {
          number,
          exp_month,
          exp_year,
          cvc,
        },
      });
      const customerId = user.stripeCustomerId;
      let customer;
      if (customerId) {
        await stripe.paymentMethods.attach(payment.id, {
          customer: user.stripeCustomerId,
        });
      } else {
        const { email } = user;
        const name = `${user.firstName} ${user.lastName}`;
        customer = await stripe.customers.create({ email, name });
        await stripe.paymentMethods.attach(payment.id, {
          customer: customer.id,
        });
        await user.updateInstance(user.id, {
          stripeCustomerId: customer.id,
        });
      }
      // One time payment using stripe paymentIntents
      const paymentIntent = await stripe.paymentIntents.create({
        amount: 99,
        currency: 'usd',
        customer: user.stripeCustomerId,
        payment_method: payment.id,
        off_session: true,
        confirm: true,
        payment_method_types: ['card'],
        receipt_email: user.email,
        // description: 'give appropriate description for receipt',
      });
      return paymentIntent;
    } catch (err) {
      return err;
    }
  },
  async listPlans() {
    try {
      const stripePlans = await Plan.getAllPlanList();
      return stripePlans;
    } catch (err) {
      return err;
    }
  },
  async updatePlanDetail(req) {
    try {
      const { body } = req;
      const stripePlans = await Plan.update(
        {
          features: body.features,
        },
        {
          where: {
            id: req.params.id,
          },
        },
      );
      return stripePlans;
    } catch (err) {
      return err;
    }
  },
  async getOldSubscribersList(req, id, newPlan) {
    const stripePlans = newPlan.StripePlans;
    const lists = await StripeSubscription.editNotify({ id });
    lists.forEach(async (list) => {
      const userDetail = await Member.findOne({
        where: { UserId: list.UserId, isDeleted: false },
        include: ['User'],
      });
      if (list && list.StripePlan.interval === 'month') {
        const plan = await stripePlans.filter((stripePlan) => stripePlan.interval === 'month');
        const subscription = await stripe.subscriptions.create({
          customer: userDetail.user.stripeCustomerId,
          trial_period_days: 0,
          items: [{ plan: plan.planId }],
        });
        const addSubscription = {
          UserId: list.userId,
          subscriptionId: subscription.id,
          status: subscription.status,
        };
        await StripeSubscription.createInstance(addSubscription);
        // await mailHelper.mailService(req, plan);
        return true;
      }
      if (list && list.StripePlan.interval === 'year') {
        const plan = await stripePlans.filter((stripePlan) => stripePlan.interval === 'year');
        const subscription = await stripe.subscriptions.create({
          customer: userDetail.user.stripeCustomerId,
          trial_period_days: 0,
          items: [{ plan: plan.planId }],
        });
        const addSubscription = {
          UserId: list.userId,
          subscriptionId: subscription.id,
          status: subscription.status,
        };
        await StripeSubscription.createInstance(addSubscription);
        // await mailHelper.mailService(req, plan);
        return true;
      }
    });
  },
};

module.exports = stripeService;
