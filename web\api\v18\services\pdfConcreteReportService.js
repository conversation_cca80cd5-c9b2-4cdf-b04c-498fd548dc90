const moment = require('moment');
const fs = require('fs');
const awsConfig = require('../middlewares/awsConfig');
const { Project, Company } = require('../models');
const puppeteerService = require('./puppeteerService');

const pdfConcreteReportService = {
  async pdfFormatOfConcreteRequest(params, loginUser, data, req, done) {
    const { timezoneoffset } = req.headers;
    const headerList = req.body.selectedHeaders;
    const header = [];
    const mailContent = [];
    let isIdSelected = false;
    let isDescriptionSelected = false;
    let isDateSelected = false;
    let isStatusSelected = false;
    let isApprovedBySelected = false;
    let isCompanySelected = false;
    let isOrderNumberSelected = false;
    let isSlumpSelected = false;
    let isTruckSpacingSelected = false;
    let isPrimerOrderedSelected = false;
    let isPersonSelected = false;
    let isQuantityOrderedSelected = false;
    let isMixDesignSelected = false;

    headerList.map((object) => {
      if (object.isActive === true) {
        if (object.key === 'id') isIdSelected = true;
        if (object.key === 'description') isDescriptionSelected = true;
        if (object.key === 'date') isDateSelected = true;
        if (object.key === 'status') isStatusSelected = true;
        if (object.key === 'approvedby') isApprovedBySelected = true;
        if (object.key === 'company') isCompanySelected = true;
        if (object.key === 'orderNumber') isOrderNumberSelected = true;
        if (object.key === 'slump') isSlumpSelected = true;
        if (object.key === 'truckSpacing') isTruckSpacingSelected = true;
        if (object.key === 'primer') isPrimerOrderedSelected = true;
        if (object.key === 'name') isPersonSelected = true;
        if (object.key === 'quantity') isQuantityOrderedSelected = true;
        if (object.key === 'mixDesign') isMixDesignSelected = true;

        const headerTag = `<th style="text-align :center">
          ${object.title}</th>`;
        header.push(headerTag);
      }
      return object;
    });
    const projectData = await Project.findOne({
      where: {
        isDeleted: false,
        id: +params.ProjectId,
      },
      attributes: ['projectName'],
    });
    const companyData = await Company.findOne({
      where: {
        isDeleted: false,
        ParentCompanyId: +req.body.ParentCompanyId,
        isParent: true,
      },
      attributes: ['companyName'],
    });
    let pdftemplate = fs.readFileSync(
      '/usr/src/web/api/v18/views/mail-templates/deliveryReport.html',
      {
        encoding: 'utf-8',
      },
    );

    for (let index = 0; index < data.length; index += 1) {
      const idTag = isIdSelected
        ? `<td style="padding:5px;color: #5B5B5B;font-weight: 600; text-align:center;
         font-size:12px;font-family:'Cairo',sans-serif">
       ${data[index].ConcreteRequestId}</td>`
        : '';
      const descriptionTag = isDescriptionSelected
        ? `<td style="color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">${data[index].description}</td>`
        : '';
      const dateTag = isDateSelected
        ? `<td style="color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">
                  ${moment(
          new Date(
            `${moment(data[index].concretePlacementStart).format('MM/DD/YYYY')} ${moment(
              data[index].concretePlacementStart,
            ).format('hh:mm a')}`,
          ),
        )
          .add(Number(timezoneoffset), 'm')
          .format('MM/DD/YYYY hh:mm a')}</td>`
        : '';
      const statusTag = isStatusSelected
        ? `<td style="color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">${data[index].status}</td>`
        : '';
      const approvedByTag = isApprovedBySelected
        ? `<td style="color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">
                  ${data[index].approverDetails
          ? `${data[index].approverDetails.User.firstName} ${data[index].approverDetails.User.lastName}`
          : '-'
        }
                  </td>`
        : '';

      let companyTag = [];
      if (isCompanySelected) {
        let companyValues = [];
        if (data[index].concreteSupplierDetails && data[index].concreteSupplierDetails.length > 0) {
          data[index].concreteSupplierDetails.map((element) => {
            const company = `<p>${element.Company.companyName}</p>`;
            companyValues.push(company);
            return company;
          });
        } else {
          companyValues = '-';
        }
        companyTag = `<td style="color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">
               ${companyValues}
                 </td>`;
      }

      const orderNumberTag = isOrderNumberSelected
        ? `<td style="color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">${data[index].concreteOrderNumber ? data[index].concreteOrderNumber : '-'
        }</td>`
        : '';

      const slumpTag = isSlumpSelected
        ? `<td style="color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">${data[index].slump ? data[index].slump : '-'
        }</td>`
        : '';

      const truckSpacingTag = isTruckSpacingSelected
        ? `<td style="color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">${data[index].truckSpacingHours ? data[index].truckSpacingHours : '-'
        }</td>`
        : '';

      const primerOrderedTag = isPrimerOrderedSelected
        ? `<td style="color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">${data[index].primerForPump ? data[index].primerForPump : '-'
        }</td>`
        : '';

      const quantityOrderedTag = isQuantityOrderedSelected
        ? `<td style="color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">${data[index].concreteQuantityOrdered ? data[index].concreteQuantityOrdered : '-'
        }</td>`
        : '';

      let mixDesignTag = [];
      if (isMixDesignSelected) {
        let mixDesignValues = [];
        if (data[index].mixDesignDetails && data[index].mixDesignDetails.length > 0) {
          data[index].mixDesignDetails.map((element) => {
            const mixDesign = `<p>${element.ConcreteMixDesign.mixDesign}</p>`;
            mixDesignValues.push(mixDesign);
            return mixDesign;
          });
        } else {
          mixDesignValues = '-';
        }
        mixDesignTag = `<td style="color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">
               ${mixDesignValues}
                 </td>`;
      }

      let memberTag = [];
      if (isPersonSelected) {
        let memberValues = [];
        if (data[index].memberDetails && data[index].memberDetails.length > 0) {
          data[index].memberDetails.map((element) => {
            const member = `<p>${element.Member.User.firstName} ${element.Member.User.lastName}</p>`;
            memberValues.push(member);
            return member;
          });
        } else {
          memberValues = '-';
        }
        memberTag = `<td style="color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">
               ${memberValues}
                 </td>`;
      }

      const content = `<tr style = "border-bottom: 1px solid #e0e0e0;font-size: 12px">
            ${idTag}${descriptionTag}${dateTag}${statusTag}${approvedByTag}${companyTag}${orderNumberTag}${slumpTag}${truckSpacingTag}${primerOrderedTag}${memberTag}${quantityOrderedTag}${mixDesignTag}
        </tr>`;
      mailContent.push(content);
    }

    pdftemplate = pdftemplate
      .replace('$projectName', `${projectData.projectName}`)
      .replace('$companyName', `${companyData.companyName}`)
      .replace('$generatedDate', req.body.generatedDate)
      .replace('$generatedBy', `${loginUser.firstName} ${loginUser.lastName}`)
      .replace('$reportType', 'Concrete')
      .replace('$header', `${header}`)
      .replace('$data', `${mailContent}`);
    pdftemplate = pdftemplate.replace(/,/g, '');

    const pdfBuffer = await puppeteerService.generatePdfBuffer(pdftemplate);
    if (pdfBuffer) {
      awsConfig.reportUpload(
        pdfBuffer,
        req.body.reportName,
        req.body.exportType,
        async (result, error1) => {
          if (!error1) {
            done(result, false);
          }
        },
      );
    }
  },
};
module.exports = pdfConcreteReportService;
