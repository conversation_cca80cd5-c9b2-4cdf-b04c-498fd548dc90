const status = require('http-status');
const { craneRequestCommentService } = require('../services');

const craneRequestCommentController = {
  async createCraneRequestComment(req, res, next) {
    try {
      await craneRequestCommentService.createCraneRequestComment(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Crane Booking Comment added successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getCraneRequestComments(req, res, next) {
    try {
      await craneRequestCommentService.getCraneRequestComments(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Crane Booking Comment Viewed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = craneRequestCommentController;
