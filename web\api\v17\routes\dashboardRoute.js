const { Router } = require('express');
const { validate } = require('express-validation');
const passportConfig = require('../config/passport');
const { DashboardController } = require('../controllers');
const { dashboardValidation } = require('../middlewares/validations');

const dashboardRoute = {
  get router() {
    const router = Router();
    router.post(
      '/get_dashboard_detail',
      validate(dashboardValidation.getDashboardData, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      DashboardController.getDashboardData,
    );
    router.get(
      '/get_projectadmin_detail/:ProjectId',
      validate(dashboardValidation.getPADashboardData, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      DashboardController.getPADashboardData,
    );
    router.post(
      '/get_graph_detail/?:ParentCompanyId',
      validate(dashboardValidation.getGraphDelivery, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      DashboardController.getGraphDelivery,
    );
    router.post(
      '/get_crane_graph_data/?:ParentCompanyId',
      validate(dashboardValidation.getGraphDelivery, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      DashboardController.getCraneGraphData,
    );
    router.post(
      '/get_concrete_graph_data/?:ParentCompanyId',
      validate(dashboardValidation.getGraphDelivery, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      DashboardController.getConcreteGraphData,
    );
    router.get(
      '/get_upcoming_delivery/:pageSize/:pageNo/?:ParentCompanyId',
      validate(dashboardValidation.upcomingDelivery, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      DashboardController.upcomingDelivery,
    );
    router.get(
      '/get_releasenote_version',
      passportConfig.isAuthenticated,
      DashboardController.getReleasenoteVersion,
    );
    return router;
  },
};
module.exports = dashboardRoute;
