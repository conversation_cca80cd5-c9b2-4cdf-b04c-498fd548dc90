const Joi = require('joi');

const locationValidation = {
  addLocation: {
    query: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      platform: Joi.string().required(),
    }),
    body: Joi.object({
      id: Joi.number().optional().allow('', null),
      mainCategory: Joi.string().required(),
      notes: Joi.string().optional().allow('', null),
      paths: Joi.array()
        .optional()
        .items({
          id: Joi.number().optional().allow(null, ''),
          subCategory: Joi.string().optional().allow(null, ''),
          isDeleted: Joi.number().optional().allow(0, 1),
          tier: Joi.array()
            .optional()
            .items({
              id: Joi.number().optional().allow(null, ''),
              tier: Joi.string().optional().allow(null, ''),
              isDeleted: Joi.number().optional().allow(0, 1),
            }),
        }),
    }),
  },
  getLocation: {
    query: Joi.object({
      id: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
  },
  listLocation: {
    query: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
      search: Joi.optional().allow('', null),
      sort: Joi.string().required(),
      sortByField: Joi.string().required(),
    }),
  },
  deleteLocation: {
    query: Joi.object({
      id: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
  },
  getLocations: {
    query: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
  },
  memberLocationPreference: {
    body: Joi.object({
      chosenMemberPreference: Joi.array().required().items({
        id: Joi.number().required(),
        MemberId: Joi.number().required(),
        ProjectId: Joi.number().required(),
        follow: Joi.boolean().required(),
      }),
    }),
  },
  importLocation: {
    body: Joi.object({
      location: Joi.optional().allow(''),
    }),
    query: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      platform: Joi.string().required(),
    }),
  },
  listLocations: {
    query: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
      search: Joi.optional().allow('', null),
      sort: Joi.string().required(),
      sortByField: Joi.string().required(),
    }),
  },
  sampleExcelFileDownload: {
    body: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
  },
};
module.exports = locationValidation;
