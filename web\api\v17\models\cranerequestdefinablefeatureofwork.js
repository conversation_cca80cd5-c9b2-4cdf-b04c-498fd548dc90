module.exports = (sequelize, DataTypes) => {
  const CraneRequestDefinableFeatureOfWork = sequelize.define(
    'CraneRequestDefinableFeatureOfWork',
    {
      CraneRequestId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      CraneRequestCode: DataTypes.INTEGER,
      DeliverDefineWorkId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ProjectId: DataTypes.INTEGER,
    },
    {},
  );
  CraneRequestDefinableFeatureOfWork.associate = (models) => {
    CraneRequestDefinableFeatureOfWork.belongsTo(models.CraneRequest, {
      as: 'craneRequest',
      foreignKey: 'CraneRequestId',
    });
    CraneRequestDefinableFeatureOfWork.belongsTo(models.DeliverDefineWork, {
      as: 'dfow',
      foreignKey: 'DeliverDefineWorkId',
    });
    CraneRequestDefinableFeatureOfWork.belongsTo(models.DeliverDefineWork);
  };
  CraneRequestDefinableFeatureOfWork.createInstance = async (paramData) => {
    const newCraneDefinableFeatureOfWork = await CraneRequestDefinableFeatureOfWork.create(
      paramData,
    );
    return newCraneDefinableFeatureOfWork;
  };
  return CraneRequestDefinableFeatureOfWork;
};
