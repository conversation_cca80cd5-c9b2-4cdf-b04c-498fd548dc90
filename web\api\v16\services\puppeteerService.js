const puppeteer = require('puppeteer');

const puppeteerService = {
    async generatePdfBuffer(pdftemplate) {
        const browser = await puppeteer.launch({
            headless: true,
            args: [
                '--disable-gpu',
                '--disable-dev-shm-usage',
                '--disable-setuid-sandbox',
                '--no-sandbox',
            ],
        });

        // Create a new page
        const page = await browser.newPage();
        await page.setContent(pdftemplate, { waitUntil: 'networkidle0' });
        // Downlaod the PDF
        const pdf = await page.pdf({
            margin: { top: '15px', right: '15px', bottom: '15px', left: '15px' },
            printBackground: true,
            format: 'A4',
            landscape: true,
        });
        // Close the browser instance
        await browser.close();
        if (pdf) {
            return Buffer.from(pdf);
        }
        return false;
    },
    async generatePdfByURL(url, timezone, next) {
        try {
            const browser = await puppeteer.launch({
                headless: true,
                args: ['--no-sandbox'],
            });
            const page = await browser.newPage();
            await page.emulateTimezone(timezone);
            await page.setViewport({
                width: 1479,
                height: 764,
            });
            await page.goto(url, {
                waitUntil: 'networkidle2',
                timeout: 0,
            });

            // Wait for all external API calls to complete
            await page.waitForFunction(
                () => {
                    return document.querySelector('.fc-event-main') !== null;
                },
                { timeout: 10000 },
            );
            const pdf = await page.pdf({
                margin: { top: '15px', right: '15px', bottom: '15px', left: '15px' },
                format: 'A4',
                pageRanges: '',
                printBackground: true,
                landscape: true,
            });

            await browser.close();
            if (pdf) {
                return pdf;
            }
            return false;
        } catch (e) {
            if (e.message === 'Waiting failed: 10000ms exceeded') {
                return { status: false, data: 'No data found!' };
            }
            next(e);
        }
    },
};

module.exports = puppeteerService;
