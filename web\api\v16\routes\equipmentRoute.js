const { Router } = require('express');
const { validate } = require('express-validation');
const { equipmentValidation } = require('../middlewares/validations');
const { EquipmentController } = require('../controllers');
const passportConfig = require('../config/passport');
const checkAdmin = require('../middlewares/checkAdmin');

const equipmentRoute = {
  get router() {
    const router = Router();
    router.post(
      '/add_equipment',
      validate(equipmentValidation.addEquipment, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdmin,
      EquipmentController.addEquipment,
    );
    router.post(
      '/update_equipment',
      validate(equipmentValidation.updateEquipment, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdmin,
      EquipmentController.updateEquipment,
    );
    router.post(
      '/equipment_list/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId',
      validate(equipmentValidation.equipmentDetail, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      EquipmentController.listEquipment,
    );
    router.post(
      '/crane_equipment_list/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId',
      validate(
        equipmentValidation.craneEquipmentDetail,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      EquipmentController.craneListEquipment,
    );
    router.get(
      '/equipment_type_list/:ProjectId',
      validate(equipmentValidation.listEquipmentType, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      EquipmentController.listEquipmentType,
    );
    router.post(
      '/delete_equipments',
      validate(equipmentValidation.deleteEquipment, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdmin,
      EquipmentController.deleteEquipment,
    );
    router.get(
      '/preset_equipment_type_list',
      passportConfig.isAuthenticated,
      EquipmentController.getPresetEquipmentTypeList,
    );
    router.post(
      '/get_mapped_requests',
      passportConfig.isAuthenticated,
      EquipmentController.getMappedRequests,
    );
    router.post(
      '/deactivate_equipment',
      passportConfig.isAuthenticated,
      EquipmentController.deactivateEquipment,
    );
    return router;
  },
};
module.exports = equipmentRoute;
