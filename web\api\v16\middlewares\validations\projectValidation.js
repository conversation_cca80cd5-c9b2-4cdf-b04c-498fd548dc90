const Joi = require('joi');

const projectValidation = {
  createProject: {
    body: Joi.object({
      firstName: Joi.string().min(3).required(),
      email: Joi.string().email().required(),
      phoneNumber: Joi.string().required(),
      projectName: Joi.string().min(3).required(),
      projectLocation: Joi.string().min(3).required(),
      projectLocationLatitude: Joi.any().optional().allow('', null),
      projectLocationLongitude: Joi.any().optional().allow('', null),
      PlanId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      stripeCustomerId: Joi.any().optional().allow('', null),
      existcard: Joi.boolean().optional().allow('', null),
      timezone: Joi.string().required(),
      // TimeZoneId: Joi.number().required().allow('', null),
      // cardDetails: Joi.object({
      //   number: Joi.number(),
      //   name: Joi.string(),
      //   interval: Joi.optional().allow(''),
      //   country: Joi.optional().allow(''),
      //   zipCode: Joi.optional().allow(''),
      //   exp_month: Joi.number(),
      //   exp_year: Joi.number(),
      //   cvc: Joi.number(),
      // }),
    }),
  },
  createAccountProject: {
    body: Joi.object({
      projectName: Joi.string().min(3).required(),
      projectLocation: Joi.string().min(3).required(),
      ParentCompanyId: Joi.any(),
      startDate: Joi.date().required(),
      endDate: Joi.date().required(),
    }),
  },
  getPlansAndProjects: {
    params: Joi.object({
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
      sort: Joi.any().optional().allow('', null),
      sortByField: Joi.any().optional().allow('', null),
    }),
    body: Joi.object({
      idFilter: Joi.string(),
      nameFilter: Joi.string(),
      search: Joi.any().optional().allow('', null),
    }),
  },
  getProjects: {
    params: Joi.object({
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  getSingleProjectDetail: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
    }),
  },
  existProject: {
    body: Joi.object({
      projectName: Joi.string().required(),
      id: Joi.number(),
      ParentCompanyId: Joi.any(),
    }),
  },
  upgradePlan: {
    body: Joi.object({
      ProjectId: Joi.number().required(),
      PlanId: Joi.number().required(),
      stripeCustomerId: Joi.string().required(),
      // existcard: Joi.boolean().required(),
      // cardDetails: Joi.object({
      //   number: Joi.number(),
      //   name: Joi.string(),
      //   interval: Joi.optional().allow(''),
      //   country: Joi.optional().allow(''),
      //   zipCode: Joi.optional().allow(''),
      //   exp_month: Joi.number(),
      //   exp_year: Joi.number(),
      //   cvc: Joi.number(),
      // }),
    }),
  },
};
module.exports = projectValidation;
