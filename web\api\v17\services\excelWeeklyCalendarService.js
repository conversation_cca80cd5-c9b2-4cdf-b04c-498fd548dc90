const moment = require('moment');

const excelWeeklyCalendarService = {
  async weeklyCalendarReport(
    finalArray,
    weekStartDate,
    weekEndDate,
    chosenDateRangeFilter,
    workbook,
    responseData,
    timezoneoffset,
  ) {
    const worksheet = workbook.addWorksheet('Delivery Report');
    const worksheet1 = workbook.addWorksheet('Crane Report');
    const worksheet2 = workbook.addWorksheet('Concrete Report');
    const worksheet3 = workbook.addWorksheet('Calendar Events');
    /* Note */
    /* Column headers */
    const rowValues = [];
    const columns = [];
    const rowValuesCrane = [];
    const columnsCrane = [];
    const rowValuesConcrete = [];
    const columnsConcrete = [];
    const rowValuesEvent = [];
    const columnsEvent = [];
    const isDescriptionSelected = true;
    const isTimeSelected = true;
    const isDateSelected = true;
    const isStatusSelected = true;
    const isApprovedBySelected = true;
    const isEquipmentSelected = true;
    const isDfowSelected = true;
    const isGateSelected = true;
    const isCompanySelected = true;
    const isPersonSelected = true;
    const isPickingFromSelected = true;
    const isPickingToSelected = true;
    const isOrderNumberSelected = true;
    const isSlumpSelected = true;
    const isTruckSpacingSelected = true;
    const isPrimerOrderedSelected = true;
    const isQuantityOrderedSelected = true;
    rowValues.push(
      'Description',
      'Time',
      'Date',
      'Status',
      'Approved By',
      'Equipment',
      'Definable Feature of Work',
      'Gate',
      'Responsible Company',
      'Responsible Person',
    );
    columns.push(
      { key: 'description', width: 32 },
      { key: 'time', width: 32 },
      { key: 'date', width: 32 },
      { key: 'status', width: 32 },
      { key: 'approvedby', width: 32 },
      { key: 'equipment', width: 32 },
      { key: 'dfow', width: 32 },
      { key: 'gate', width: 32 },
      { key: 'company', width: 32 },
      { key: 'name', width: 32 },
    );
    rowValuesCrane.push(
      'Description',
      'Time',
      'Date',
      'Status',
      'Approved By',
      'Equipment',
      'Definable Feature of Work',
      'Responsible Company',
      'Responsible Person',
      'Picking From',
      'Picking To',
    );
    columnsCrane.push(
      { key: 'description', width: 32 },
      { key: 'time', width: 32 },
      { key: 'date', width: 32 },
      { key: 'status', width: 32 },
      { key: 'approvedby', width: 32 },
      { key: 'equipment', width: 32 },
      { key: 'dfow', width: 32 },
      { key: 'company', width: 32 },
      { key: 'name', width: 32 },
      { key: 'pickingFrom', width: 32 },
      { key: 'pickingTo', width: 32 },
    );
    rowValuesConcrete.push(
      'Description',
      'Time',
      'Date',
      'Status',
      'Approved By',
      'Concrete Supplier',
      'Order Number',
      'Slump',
      'Truck Spacing',
      'Primer Ordered',
      'Responsible Person',
      'Quantity Ordered',
    );
    columnsConcrete.push(
      { key: 'description', width: 32 },
      { key: 'time', width: 32 },
      { key: 'date', width: 32 },
      { key: 'status', width: 32 },
      { key: 'approvedby', width: 32 },
      { key: 'company', width: 32 },
      { key: 'orderNumber', width: 32 },
      { key: 'slump', width: 32 },
      { key: 'truckSpacing', width: 32 },
      { key: 'primer', width: 32 },
      { key: 'name', width: 32 },
      { key: 'quantity', width: 32 },
    );
    rowValuesEvent.push('Description', 'Time', 'Date');
    columnsEvent.push(
      { key: 'description', width: 32 },
      { key: 'time', width: 32 },
      { key: 'date', width: 32 },
    );
    worksheet.getRow(1).values = rowValues;
    worksheet.columns = columns;
    const cellRange = {
      0: 'A',
      1: 'B',
      2: 'C',
      3: 'D',
      4: 'E',
      5: 'F',
      6: 'G',
      7: 'H',
      8: 'I',
      9: 'J',
      10: 'K',
      11: 'L',
    };
    worksheet1.getRow(1).values = rowValuesCrane;
    worksheet1.columns = columnsCrane;
    worksheet2.getRow(1).values = rowValuesConcrete;
    worksheet2.columns = columnsConcrete;
    worksheet3.getRow(1).values = rowValuesEvent;
    worksheet3.columns = columnsEvent;
    const filteredDeliveryRequests = [];
    const filteredCraneArray = [];
    const filteredConcreteArray = [];
    const calendarEventsArray = [];
    let startRange = moment(weekStartDate);
    let endRange = moment(weekEndDate);
    if (!chosenDateRangeFilter) {
      startRange = moment(moment(weekStartDate, 'YYYY-MM-DD HH:mm:ss')).add(
        -Number(timezoneoffset),
        'm',
      );
      endRange = moment(moment(weekEndDate, 'YYYY-MM-DD HH:mm:ss')).add(
        -Number(timezoneoffset),
        'm',
      );
    }
    if (responseData && responseData.length > 0) {
      for (let index = 1; index <= responseData.length; index += 1) {
        if (
          responseData[index - 1].requestType === 'deliveryRequest' ||
          responseData[index - 1].requestType === 'deliveryRequestWithCrane'
        ) {
          filteredDeliveryRequests.push(responseData[index - 1]);
        }
        if (finalArray.includes('Crane')) {
          if (
            responseData[index - 1].requestType === 'craneRequest' ||
            responseData[index - 1].requestType === 'deliveryRequestWithCrane'
          ) {
            filteredCraneArray.push(responseData[index - 1]);
          }
        }
        if (responseData[index - 1].requestType === 'concreteRequest') {
          filteredConcreteArray.push(responseData[index - 1]);
        }
        if (responseData[index - 1].requestType === 'calendarEvent') {
          calendarEventsArray.push(responseData[index - 1]);
        }
      }
      const deliveryArray = filteredDeliveryRequests.filter((object) => {
        if (
          moment(moment(object.deliveryStart).add(Number(timezoneoffset), 'm')).isBetween(
            startRange,
            endRange,
            null,
            '[]',
          )
        ) {
          return object;
        }
      });
      if (deliveryArray && deliveryArray.length) {
        for (let deliveryIndex = 1; deliveryIndex <= deliveryArray.length; deliveryIndex += 1) {
          worksheet.addRow();
          if (isDescriptionSelected) {
            const cellValue = cellRange[rowValues.indexOf('Description')];
            worksheet.getCell(`${cellValue}${deliveryIndex + 1}`).value =
              deliveryArray[deliveryIndex - 1].description;
          }
          if (isTimeSelected) {
            const cellValue = cellRange[rowValues.indexOf('Time')];
            if (
              deliveryArray[deliveryIndex - 1].requestType === 'deliveryRequest' ||
              deliveryArray[deliveryIndex - 1].requestType === 'deliveryRequestWithCrane'
            ) {
              const fromTime = moment(deliveryArray[deliveryIndex - 1].deliveryStart)
                .add(Number(timezoneoffset), 'm')
                .format('hh:mm A');
              const toTime = moment(deliveryArray[deliveryIndex - 1].deliveryEnd)
                .add(Number(timezoneoffset), 'm')
                .format('hh:mm A');
              worksheet.getCell(
                `${cellValue}${deliveryIndex + 1}`,
              ).value = `${fromTime} - ${toTime}`;
            }
          }
          if (isDateSelected) {
            const cellValue = cellRange[rowValues.indexOf('Date')];
            worksheet.getCell(`${cellValue}${deliveryIndex + 1}`).value = moment(
              new Date(
                `${moment(deliveryArray[deliveryIndex - 1].deliveryStart).format(
                  'MM/DD/YYYY',
                )} ${moment(deliveryArray[deliveryIndex - 1].deliveryStart).format('hh:mm a')}`,
              ),
            )
              .add(Number(timezoneoffset), 'm')
              .format('MM/DD/YYYY');
          }
          if (isStatusSelected) {
            const cellValue = cellRange[rowValues.indexOf('Status')];
            worksheet.getCell(`${cellValue}${deliveryIndex + 1}`).value =
              deliveryArray[deliveryIndex - 1].status;
          }
          if (isApprovedBySelected) {
            const cellValue = cellRange[rowValues.indexOf('Approved By')];
            worksheet.getCell(`${cellValue}${deliveryIndex + 1}`).value =
              deliveryArray[deliveryIndex - 1].approverDetails &&
              deliveryArray[deliveryIndex - 1].approverDetails.User.firstName
                ? `${deliveryArray[deliveryIndex - 1].approverDetails.User.firstName} ${
                    deliveryArray[deliveryIndex - 1].approverDetails.User.lastName
                  }`
                : '-';
          }
          if (isEquipmentSelected) {
            const cellValue = cellRange[rowValues.indexOf('Equipment')];
            worksheet.getCell(`${cellValue}${deliveryIndex + 1}`).value =
              deliveryArray[deliveryIndex - 1].equipmentDetails &&
              deliveryArray[deliveryIndex - 1].equipmentDetails[0]
                ? deliveryArray[deliveryIndex - 1].equipmentDetails[0].Equipment.equipmentName
                : '-';
          }
          if (isDfowSelected) {
            const cellValue = cellRange[rowValues.indexOf('Definable Feature of Work')];
            if (
              deliveryArray[deliveryIndex - 1].defineWorkDetails &&
              deliveryArray[deliveryIndex - 1].defineWorkDetails.length > 0
            ) {
              const dfowValues = [];
              for (
                let m = 0;
                m < deliveryArray[deliveryIndex - 1].defineWorkDetails.length;
                m += 1
              ) {
                if (
                  deliveryArray[deliveryIndex - 1].defineWorkDetails &&
                  deliveryArray[deliveryIndex - 1].defineWorkDetails[m]
                ) {
                  dfowValues.push(
                    deliveryArray[deliveryIndex - 1].defineWorkDetails[m].DeliverDefineWork.DFOW,
                  );
                }
              }
              const dfow = dfowValues.join(', ');
              worksheet.getCell(`${cellValue}${deliveryIndex + 1}`).value = dfow;
            } else {
              worksheet.getCell(`${cellValue}${deliveryIndex + 1}`).value = '-';
            }
          }
          if (isGateSelected) {
            const cellValue = cellRange[rowValues.indexOf('Gate')];
            worksheet.getCell(`${cellValue}${deliveryIndex + 1}`).value =
              deliveryArray[deliveryIndex - 1].gateDetails &&
              deliveryArray[deliveryIndex - 1].gateDetails[0]
                ? deliveryArray[deliveryIndex - 1].gateDetails[0].Gate.gateName
                : '-';
          }
          if (isCompanySelected) {
            const cellValue = cellRange[rowValues.indexOf('Responsible Company')];
            if (
              deliveryArray[deliveryIndex - 1].companyDetails &&
              deliveryArray[deliveryIndex - 1].companyDetails.length > 0
            ) {
              const companyValues = [];
              for (let m = 0; m < deliveryArray[deliveryIndex - 1].companyDetails.length; m += 1) {
                if (
                  deliveryArray[deliveryIndex - 1].companyDetails &&
                  deliveryArray[deliveryIndex - 1].companyDetails[m]
                ) {
                  companyValues.push(
                    deliveryArray[deliveryIndex - 1].companyDetails[m].Company.companyName,
                  );
                }
              }
              const company = companyValues.join(', ');
              worksheet.getCell(`${cellValue}${deliveryIndex + 1}`).value = company;
            } else {
              worksheet.getCell(`${cellValue}${deliveryIndex + 1}`).value = '-';
            }
          }
          if (isPersonSelected) {
            const cellValue = cellRange[rowValues.indexOf('Responsible Person')];
            if (
              deliveryArray[deliveryIndex - 1].memberDetails &&
              deliveryArray[deliveryIndex - 1].memberDetails.length > 0
            ) {
              const memberValues = [];
              for (let m = 0; m < deliveryArray[deliveryIndex - 1].memberDetails.length; m += 1) {
                if (
                  deliveryArray[deliveryIndex - 1].memberDetails &&
                  deliveryArray[deliveryIndex - 1].memberDetails[m]
                ) {
                  memberValues.push(
                    `${deliveryArray[deliveryIndex - 1].memberDetails[m].Member.User.firstName} ${
                      deliveryArray[deliveryIndex - 1].memberDetails[m].Member.User.lastName
                    }`,
                  );
                }
              }
              const member = memberValues.join(', ');
              worksheet.getCell(`${cellValue}${deliveryIndex + 1}`).value = member;
            } else {
              worksheet.getCell(`${cellValue}${deliveryIndex + 1}`).value = '-';
            }
          }
        }
      }
      const craneArray = filteredCraneArray.filter((object) => {
        if (
          moment(moment(object.craneDeliveryStart).add(Number(timezoneoffset), 'm')).isBetween(
            startRange,
            endRange,
            null,
            '[]',
          )
        ) {
          return object;
        }
      });

      if (craneArray && craneArray.length) {
        for (let craneIndex = 1; craneIndex <= craneArray.length; craneIndex += 1) {
          worksheet1.addRow();
          if (isDescriptionSelected) {
            const cellValue = cellRange[rowValuesCrane.indexOf('Description')];
            worksheet1.getCell(`${cellValue}${craneIndex + 1}`).value =
              craneArray[craneIndex - 1].description;
          }
          if (isTimeSelected) {
            const cellValue = cellRange[rowValuesCrane.indexOf('Time')];
            if (craneArray[craneIndex - 1].requestType === 'craneRequest') {
              const fromTime = moment(craneArray[craneIndex - 1].craneDeliveryStart)
                .add(Number(timezoneoffset), 'm')
                .format('hh:mm A');
              const toTime = moment(craneArray[craneIndex - 1].craneDeliveryEnd)
                .add(Number(timezoneoffset), 'm')
                .format('hh:mm A');
              worksheet1.getCell(`${cellValue}${craneIndex + 1}`).value = `${fromTime} - ${toTime}`;
            } else if (craneArray[craneIndex - 1].requestType === 'deliveryRequestWithCrane') {
              const fromTime = moment(craneArray[craneIndex - 1].deliveryStart)
                .add(Number(timezoneoffset), 'm')
                .format('hh:mm A');
              const toTime = moment(craneArray[craneIndex - 1].deliveryEnd)
                .add(Number(timezoneoffset), 'm')
                .format('hh:mm A');
              worksheet1.getCell(`${cellValue}${craneIndex + 1}`).value = `${fromTime} - ${toTime}`;
            }
          }
          if (isDateSelected) {
            const cellValue = cellRange[rowValuesCrane.indexOf('Date')];
            if (craneArray[craneIndex - 1].requestType === 'craneRequest') {
              worksheet1.getCell(`${cellValue}${craneIndex + 1}`).value = moment(
                new Date(
                  `${moment(craneArray[craneIndex - 1].craneDeliveryStart).format(
                    'MM/DD/YYYY',
                  )} ${moment(craneArray[craneIndex - 1].craneDeliveryStart).format('hh:mm a')}`,
                ),
              )
                .add(Number(timezoneoffset), 'm')
                .format('MM/DD/YYYY');
            } else if (craneArray[craneIndex - 1].requestType === 'deliveryRequestWithCrane') {
              worksheet1.getCell(`${cellValue}${craneIndex + 1}`).value = moment(
                new Date(
                  `${moment(craneArray[craneIndex - 1].deliveryStart).format(
                    'MM/DD/YYYY',
                  )} ${moment(craneArray[craneIndex - 1].deliveryStart).format('hh:mm a')}`,
                ),
              )
                .add(Number(timezoneoffset), 'm')
                .format('MM/DD/YYYY');
            }
          }
          if (isStatusSelected) {
            const cellValue = cellRange[rowValuesCrane.indexOf('Status')];
            worksheet1.getCell(`${cellValue}${craneIndex + 1}`).value =
              craneArray[craneIndex - 1].status;
          }
          if (isApprovedBySelected) {
            const cellValue = cellRange[rowValuesCrane.indexOf('Approved By')];
            worksheet1.getCell(`${cellValue}${craneIndex + 1}`).value =
              craneArray[craneIndex - 1].approverDetails &&
              craneArray[craneIndex - 1].approverDetails.User.firstName
                ? `${craneArray[craneIndex - 1].approverDetails.User.firstName} ${
                    craneArray[craneIndex - 1].approverDetails.User.lastName
                  }`
                : '-';
          }
          if (isEquipmentSelected) {
            const cellValue = cellRange[rowValuesCrane.indexOf('Equipment')];
            worksheet1.getCell(`${cellValue}${craneIndex + 1}`).value =
              craneArray[craneIndex - 1].equipmentDetails &&
              craneArray[craneIndex - 1].equipmentDetails[0]
                ? craneArray[craneIndex - 1].equipmentDetails[0].Equipment.equipmentName
                : '-';
          }
          if (isDfowSelected) {
            const cellValue = cellRange[rowValuesCrane.indexOf('Definable Feature of Work')];
            if (
              craneArray[craneIndex - 1].defineWorkDetails &&
              craneArray[craneIndex - 1].defineWorkDetails.length > 0
            ) {
              const dfowValues = [];
              for (let m = 0; m < craneArray[craneIndex - 1].defineWorkDetails.length; m += 1) {
                if (
                  craneArray[craneIndex - 1].defineWorkDetails &&
                  craneArray[craneIndex - 1].defineWorkDetails[m]
                ) {
                  dfowValues.push(
                    craneArray[craneIndex - 1].defineWorkDetails[m].DeliverDefineWork.DFOW,
                  );
                }
              }
              const dfow = dfowValues.join(', ');
              worksheet1.getCell(`${cellValue}${craneIndex + 1}`).value = dfow;
            } else {
              worksheet1.getCell(`${cellValue}${craneIndex + 1}`).value = '-';
            }
          }
          if (isCompanySelected) {
            const cellValue = cellRange[rowValuesCrane.indexOf('Responsible Company')];
            if (
              craneArray[craneIndex - 1].companyDetails &&
              craneArray[craneIndex - 1].companyDetails.length > 0
            ) {
              const companyValues = [];
              for (let m = 0; m < craneArray[craneIndex - 1].companyDetails.length; m += 1) {
                if (
                  craneArray[craneIndex - 1].companyDetails &&
                  craneArray[craneIndex - 1].companyDetails[m]
                ) {
                  companyValues.push(
                    craneArray[craneIndex - 1].companyDetails[m].Company.companyName,
                  );
                }
              }
              const company = companyValues.join(', ');
              worksheet1.getCell(`${cellValue}${craneIndex + 1}`).value = company;
            } else {
              worksheet1.getCell(`${cellValue}${craneIndex + 1}`).value = '-';
            }
          }
          if (isPersonSelected) {
            const cellValue = cellRange[rowValuesCrane.indexOf('Responsible Person')];
            if (
              craneArray[craneIndex - 1].memberDetails &&
              craneArray[craneIndex - 1].memberDetails.length > 0
            ) {
              const memberValues = [];
              for (let m = 0; m < craneArray[craneIndex - 1].memberDetails.length; m += 1) {
                if (
                  craneArray[craneIndex - 1].memberDetails &&
                  craneArray[craneIndex - 1].memberDetails[m]
                ) {
                  memberValues.push(
                    `${craneArray[craneIndex - 1].memberDetails[m].Member.User.firstName} ${
                      craneArray[craneIndex - 1].memberDetails[m].Member.User.lastName
                    }`,
                  );
                }
              }
              const member = memberValues.join(', ');
              worksheet1.getCell(`${cellValue}${craneIndex + 1}`).value = member;
            } else {
              worksheet1.getCell(`${cellValue}${craneIndex + 1}`).value = '-';
            }
          }
          if (isPickingFromSelected) {
            let pickingFromValue;
            const cellValue = cellRange[rowValuesCrane.indexOf('Picking From')];
            if (craneArray[craneIndex - 1].requestType === 'craneRequest') {
              pickingFromValue = craneArray[craneIndex - 1].pickUpLocation;
            }
            // else if (responseData[craneIndex - 1].requestType === 'deliveryRequestWithCrane') {
            //   pickingFromValue = responseData[craneIndex - 1].cranePickUpLocation;
            // }
            else {
              pickingFromValue = '-';
            }
            worksheet1.getCell(`${cellValue}${craneIndex + 1}`).value = pickingFromValue;
          }
          if (isPickingToSelected) {
            let pickingToValue;
            const cellValue = cellRange[rowValuesCrane.indexOf('Picking To')];
            if (craneArray[craneIndex - 1].requestType === 'craneRequest') {
              pickingToValue = craneArray[craneIndex - 1].dropOffLocation;
            }
            // else if (craneArray[craneIndex - 1].requestType === 'deliveryRequestWithCrane') {
            //   pickingToValue = craneArray[craneIndex - 1].craneDropOffLocation;
            // }
            else {
              pickingToValue = '-';
            }
            worksheet1.getCell(`${cellValue}${craneIndex + 1}`).value = pickingToValue;
          }
        }
      }
      const concreteArray = filteredConcreteArray.filter((object) => {
        if (
          moment(moment(object.concretePlacementStart).add(Number(timezoneoffset), 'm')).isBetween(
            startRange,
            endRange,
            null,
            '[]',
          )
        ) {
          return object;
        }
      });
      if (concreteArray && concreteArray.length) {
        for (let concreteIndex = 1; concreteIndex <= concreteArray.length; concreteIndex += 1) {
          worksheet2.addRow();
          if (isDescriptionSelected) {
            const cellValue = cellRange[rowValuesConcrete.indexOf('Description')];
            worksheet2.getCell(`${cellValue}${concreteIndex + 1}`).value =
              concreteArray[concreteIndex - 1].description;
          }
          if (isTimeSelected) {
            const cellValue = cellRange[rowValuesConcrete.indexOf('Time')];
            if (concreteArray[concreteIndex - 1].requestType === 'concreteRequest') {
              const fromTime = moment(concreteArray[concreteIndex - 1].concretePlacementStart)
                .add(Number(timezoneoffset), 'm')
                .format('hh:mm A');
              const toTime = moment(concreteArray[concreteIndex - 1].concretePlacementEnd)
                .add(Number(timezoneoffset), 'm')
                .format('hh:mm A');
              worksheet2.getCell(
                `${cellValue}${concreteIndex + 1}`,
              ).value = `${fromTime} - ${toTime}`;
            }
          }
          if (isDateSelected) {
            const cellValue = cellRange[rowValuesConcrete.indexOf('Date')];
            worksheet2.getCell(`${cellValue}${concreteIndex + 1}`).value = moment(
              new Date(
                `${moment(concreteArray[concreteIndex - 1].concretePlacementStart).format(
                  'MM/DD/YYYY',
                )} ${moment(concreteArray[concreteIndex - 1].concretePlacementStart).format(
                  'hh:mm a',
                )}`,
              ),
            )
              .add(Number(timezoneoffset), 'm')
              .format('MM/DD/YYYY');
          }
          if (isStatusSelected) {
            const cellValue = cellRange[rowValuesConcrete.indexOf('Status')];
            worksheet2.getCell(`${cellValue}${concreteIndex + 1}`).value =
              concreteArray[concreteIndex - 1].status;
          }
          if (isApprovedBySelected) {
            const cellValue = cellRange[rowValuesConcrete.indexOf('Approved By')];
            worksheet2.getCell(`${cellValue}${concreteIndex + 1}`).value =
              concreteArray[concreteIndex - 1].approverDetails &&
              concreteArray[concreteIndex - 1].approverDetails.User.firstName
                ? `${concreteArray[concreteIndex - 1].approverDetails.User.firstName} ${
                    concreteArray[concreteIndex - 1].approverDetails.User.lastName
                  }`
                : '-';
          }
          if (isCompanySelected) {
            const cellValue = cellRange[rowValuesConcrete.indexOf('Concrete Supplier')];
            if (
              concreteArray[concreteIndex - 1].concreteSupplierDetails &&
              concreteArray[concreteIndex - 1].concreteSupplierDetails.length > 0
            ) {
              const companyValues = [];
              for (
                let m = 0;
                m < concreteArray[concreteIndex - 1].concreteSupplierDetails.length;
                m += 1
              ) {
                if (
                  concreteArray[concreteIndex - 1].concreteSupplierDetails &&
                  concreteArray[concreteIndex - 1].concreteSupplierDetails[m]
                ) {
                  companyValues.push(
                    concreteArray[concreteIndex - 1].concreteSupplierDetails[m].Company.companyName,
                  );
                }
              }
              const company = companyValues.join(', ');
              worksheet2.getCell(`${cellValue}${concreteIndex + 1}`).value = company;
            } else {
              worksheet2.getCell(`${cellValue}${concreteIndex + 1}`).value = '-';
            }
          }
          if (isOrderNumberSelected) {
            const cellValue = cellRange[rowValuesConcrete.indexOf('Order Number')];
            worksheet2.getCell(`${cellValue}${concreteIndex + 1}`).value = concreteArray[
              concreteIndex - 1
            ].concreteOrderNumber
              ? concreteArray[concreteIndex - 1].concreteOrderNumber
              : '-';
          }
          if (isSlumpSelected) {
            const cellValue = cellRange[rowValuesConcrete.indexOf('Slump')];
            worksheet2.getCell(`${cellValue}${concreteIndex + 1}`).value = concreteArray[
              concreteIndex - 1
            ].slump
              ? concreteArray[concreteIndex - 1].slump
              : '-';
          }
          if (isTruckSpacingSelected) {
            const cellValue = cellRange[rowValuesConcrete.indexOf('Truck Spacing')];
            worksheet2.getCell(`${cellValue}${concreteIndex + 1}`).value = concreteArray[
              concreteIndex - 1
            ].truckSpacingHours
              ? concreteArray[concreteIndex - 1].truckSpacingHours
              : '-';
          }
          if (isPrimerOrderedSelected) {
            const cellValue = cellRange[rowValuesConcrete.indexOf('Primer Ordered')];
            worksheet2.getCell(`${cellValue}${concreteIndex + 1}`).value = concreteArray[
              concreteIndex - 1
            ].primerForPump
              ? concreteArray[concreteIndex - 1].primerForPump
              : '-';
          }
          if (isPersonSelected) {
            const cellValue = cellRange[rowValuesConcrete.indexOf('Responsible Person')];
            if (
              concreteArray[concreteIndex - 1].memberDetails &&
              concreteArray[concreteIndex - 1].memberDetails.length > 0
            ) {
              const memberValues = [];
              for (let m = 0; m < concreteArray[concreteIndex - 1].memberDetails.length; m += 1) {
                if (
                  concreteArray[concreteIndex - 1].memberDetails &&
                  concreteArray[concreteIndex - 1].memberDetails[m]
                ) {
                  memberValues.push(
                    `${concreteArray[concreteIndex - 1].memberDetails[m].Member.User.firstName} ${
                      concreteArray[concreteIndex - 1].memberDetails[m].Member.User.lastName
                    }`,
                  );
                }
              }
              const member = memberValues.join(', ');
              worksheet2.getCell(`${cellValue}${concreteIndex + 1}`).value = member;
            } else {
              worksheet2.getCell(`${cellValue}${concreteIndex + 1}`).value = '-';
            }
          }
          if (isQuantityOrderedSelected) {
            const cellValue = cellRange[rowValuesConcrete.indexOf('Quantity Ordered')];
            worksheet2.getCell(`${cellValue}${concreteIndex + 1}`).value = concreteArray[
              concreteIndex - 1
            ].concreteQuantityOrdered
              ? concreteArray[concreteIndex - 1].concreteQuantityOrdered
              : '-';
          }
        }
      }
      const filterCalendarEventsData = calendarEventsArray.filter((object) => {
        if (
          moment(moment(object.fromDate).add(Number(timezoneoffset), 'm')).isBetween(
            startRange,
            endRange,
            null,
            '[]',
          )
        ) {
          return object;
        }
      });
      if (filterCalendarEventsData.length) {
        for (let eventIndex = 1; eventIndex <= filterCalendarEventsData.length; eventIndex += 1) {
          worksheet3.addRow();
          if (isDescriptionSelected) {
            const cellValue = cellRange[rowValuesEvent.indexOf('Description')];
            worksheet3.getCell(`${cellValue}${eventIndex + 1}`).value =
              filterCalendarEventsData[eventIndex - 1].description;
          }
          if (isTimeSelected) {
            const cellValue = cellRange[rowValuesEvent.indexOf('Time')];
            if (filterCalendarEventsData[eventIndex - 1].requestType === 'calendarEvent') {
              const fromTime = moment(filterCalendarEventsData[eventIndex - 1].fromDate)
                .add(Number(timezoneoffset), 'm')
                .format('hh:mm A');
              const toTime = moment(filterCalendarEventsData[eventIndex - 1].toDate)
                .add(Number(timezoneoffset), 'm')
                .format('hh:mm A');
              worksheet3.getCell(`${cellValue}${eventIndex + 1}`).value = `${fromTime} - ${toTime}`;
            }
          }
          if (isDateSelected) {
            const cellValue = cellRange[rowValuesEvent.indexOf('Date')];
            worksheet3.getCell(`${cellValue}${eventIndex + 1}`).value = moment(
              new Date(
                `${moment(filterCalendarEventsData[eventIndex - 1].fromDate).format(
                  'MM/DD/YYYY',
                )} ${moment(filterCalendarEventsData[eventIndex - 1].fromDate).format('hh:mm a')}`,
              ),
            )
              .add(Number(timezoneoffset), 'm')
              .format('MM/DD/YYYY');
          }
        }
      }
    }
    return workbook;
  },
};
module.exports = excelWeeklyCalendarService;
