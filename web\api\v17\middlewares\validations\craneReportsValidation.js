const Joi = require('joi');

const craneReportsValidation = {
  CraneReports: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
      void: Joi.number().required(),
    }),
    body: Joi.object({
      companyFilter: Joi.number(),
      queuedNdr: Joi.boolean().required(),
      descriptionFilter: Joi.optional().allow(''),
      memberFilter: Joi.number(),
      gateFilter: Joi.number(),
      ParentCompanyId: Joi.number(),
      upcoming: Joi.boolean(),
      equipmentFilter: Joi.number(),
      assignedFilter: Joi.boolean(),
      startdate: Joi.optional().allow(''),
      statusFilter: Joi.optional().allow(''),
      search: Joi.optional().allow(''),
      sort: Joi.any().optional().allow('', null),
      sortByField: Joi.any().optional().allow('', null),
      pickFrom: Joi.optional().allow('', null),
      pickTo: Joi.optional().allow('', null),
      defineFilter: Joi.number(),
      enddate: Joi.optional().allow(''),
      idFilter: Joi.number(),
    }),
  },
};
module.exports = craneReportsValidation;
