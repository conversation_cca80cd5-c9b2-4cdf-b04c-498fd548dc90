/* eslint-disable no-await-in-loop */
/* eslint-disable no-continue */
/* eslint-disable no-loop-func */
const moment = require('moment');
const status = require('http-status');
const Moment = require('moment');
const MomentRange = require('moment-range');
const momenttz = require('moment-timezone');

const momentRange = MomentRange.extendMoment(Moment);

const { Sequelize, Enterprise, Project, TimeZone } = require('../models');
let { CalendarSetting, Member, User } = require('../models');
const helper = require('../helpers/domainHelper');
const ApiError = require('../helpers/apiError');

const { Op } = Sequelize;
let publicUser;
let publicMember;
const calendarSettingsService = {
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicUser = modelData.User;
    publicMember = modelData.Member;
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let domainName = inputData.user && inputData.user.domainName ? inputData.user.domainName : '';
    let enterpriseValue;
    let ProjectId;
    const incomeData = inputData;
    const ParentCompanyId =
      inputData.body && inputData.body.ParentCompanyId
        ? inputData.body && inputData.body.ParentCompanyId
        : inputData.params && inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    Member = modelObj.Member;
    CalendarSetting = modelObj.CalendarSetting;
    User = modelObj.User;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return ProjectId;
  },
  async getAll(req, next) {
    try {
      const { ProjectId } = req.query;
      const { ParentCompanyId } = req.query;
      const { search } = req.query;
      const { isApplicableToDelivery } = req.body;
      const { isApplicableToCrane } = req.body;
      const { isApplicableToConcrete } = req.body;
      await this.getDynamicModel(req);
      const loginUser = req.user;
      const isMemberExists = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId,
          isDeleted: false,
          ParentCompanyId,
        }),
      });
      if (isMemberExists) {
        const condition = {
          ProjectId,
          isDeleted: false,
          description: '',
          isApplicableToDelivery: '',
          isApplicableToCrane: '',
          isApplicableToConcrete: '',
        };
        if (search) {
          condition.description = {
            [Op.iLike]: `%${search}%`,
          };
        } else {
          delete condition.description;
        }
        if (isApplicableToDelivery) {
          condition.isApplicableToDelivery = true;
        } else {
          delete condition.isApplicableToDelivery;
        }
        if (isApplicableToCrane) {
          condition.isApplicableToCrane = true;
        } else {
          delete condition.isApplicableToCrane;
        }
        if (isApplicableToConcrete) {
          condition.isApplicableToConcrete = true;
        } else {
          delete condition.isApplicableToConcrete;
        }
        let events;
        if (req.query.weeklyReportTest === 'weeklyReport') {
          const newCondition = {
            ProjectId: +req.query.ProjectId,
            isDeleted: false,
          };
          events = await CalendarSetting.getAllWeeklyReport(newCondition);
        } else {
          events = await CalendarSetting.getAll(condition);
        }
        if (events.length > 0) {
          const eventsArray = [];
          let uniqueNumber = 0;
          for (let indexval = 0; indexval < events.length; indexval += 1) {
            const eventObject = events[indexval];
            const eventTimeZone = await TimeZone.findOne({
              where: {
                isDeleted: false,
                id: +eventObject.TimeZoneId,
              },
              attributes: [
                'id',
                'location',
                'isDayLightSavingEnabled',
                'timeZoneOffsetInMinutes',
                'dayLightSavingTimeInMinutes',
                'timezone',
              ],
            });
            if (eventObject) {
              const range = momentRange.range(
                moment(eventObject.fromDate),
                moment(eventObject.toDate),
              );
              let totalDays = Array.from(range.by('day'));
              if (eventObject.recurrence === 'Does Not Repeat') {
                const startDate = totalDays[0];
                const endDate = totalDays[totalDays.length - 1];
                totalDays.forEach(async (data) => {
                  uniqueNumber += 1;
                  const recurrenceObject = await this.createRecurrenceObject(
                    eventObject,
                    eventTimeZone,
                    data.toDate(),
                    startDate,
                    endDate,
                    uniqueNumber,
                    eventObject.recurrence,
                  );
                  eventsArray.push(recurrenceObject);
                });
              }
              if (eventObject.recurrence === 'Daily') {
                let dailyIndex = 0;
                const startDate = totalDays[0];
                const endDate = totalDays[totalDays.length - 1];
                while (dailyIndex < totalDays.length) {
                  const data = totalDays[dailyIndex];
                  uniqueNumber += 1;
                  const recurrenceObject = await this.createRecurrenceObject(
                    eventObject,
                    eventTimeZone,
                    data.toDate(),
                    startDate,
                    endDate,
                    uniqueNumber,
                    eventObject.recurrence,
                  );
                  eventsArray.push(recurrenceObject);
                  dailyIndex += +eventObject.repeatEveryCount;
                }
              }
              if (eventObject.recurrence === 'Weekly') {
                const startDayWeek = moment(eventObject.fromDate).startOf('week');
                const endDayWeek = moment(eventObject.endDate).endOf('week');
                const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
                const totalDaysOfRecurrence = Array.from(range1.by('day'));
                totalDays = totalDaysOfRecurrence;
                const startDate = moment(eventObject.fromDate);
                const endDate = moment(eventObject.endDate);
                let count;
                let weekIncrement;
                if (+eventObject.repeatEveryCount > 1) {
                  count = +eventObject.repeatEveryCount - 1;
                  weekIncrement = 7;
                } else {
                  count = 1;
                  weekIncrement = 0;
                }
                for (
                  let indexba = 0;
                  indexba < totalDays.length;
                  indexba += weekIncrement * count
                ) {
                  const totalLength = indexba + 6;
                  for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
                    const data = totalDays[indexb];
                    indexba += 1;
                    if (
                      data &&
                      !moment(data).isBefore(eventObject.fromDate) &&
                      !moment(data).isAfter(eventObject.endDate)
                    ) {
                      const day = moment(data).format('dddd');
                      const indexVal = eventObject.days.includes(day);
                      if (indexVal) {
                        uniqueNumber += 1;
                        const recurrenceObject = await this.createRecurrenceObject(
                          eventObject,
                          eventTimeZone,
                          data.toDate(),
                          startDate,
                          endDate,
                          uniqueNumber,
                          eventObject.recurrence,
                        );
                        eventsArray.push(recurrenceObject);
                      }
                    }
                  }
                }
              }
              if (eventObject.recurrence === 'Monthly' || eventObject.recurrence === 'Yearly') {
                const startMonth = moment(eventObject.fromDate).startOf('month');
                const startMonthNumber = moment(startMonth).format('MM');
                const endMonth = moment(eventObject.endDate).endOf('month');
                const endMonthNumber = moment(endMonth).format('MM');
                let startDate = moment(eventObject.fromDate, 'YYYY-MM-DD');
                const endDate = moment(eventObject.endDate, 'YYYY-MM-DD').endOf('month');
                const allMonthsInPeriod = [];
                while (startDate.isBefore(endDate)) {
                  allMonthsInPeriod.push(startDate.format('YYYY-MM'));
                  startDate =
                    eventObject.recurrence === 'Monthly'
                      ? startDate.add(1, 'month')
                      : startDate.add(12, 'month');
                }
                let currentMonthDates = [];
                let totalNumberOfMonths = endMonthNumber - startMonthNumber;
                if (totalNumberOfMonths < 0) {
                  totalNumberOfMonths *= -1;
                }
                let k = 0;
                while (k < allMonthsInPeriod.length + 1) {
                  currentMonthDates = Array.from(
                    { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                    (x, j) =>
                      moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                  );
                  if (eventObject.chosenDateOfMonth) {
                    const getDate = currentMonthDates.filter(
                      (value) => moment(value).format('DD') === eventObject.dateOfMonth,
                    );
                    if (getDate.length === 1) {
                      if (
                        moment(getDate[0]).isBetween(
                          moment(eventObject.fromDate),
                          moment(eventObject.endDate),
                          null,
                          '[]',
                        ) ||
                        moment(getDate[0]).isSame(eventObject.fromDate) ||
                        moment(getDate[0]).isSame(eventObject.endDate)
                      ) {
                        const recurrenceObject = await this.createRecurrenceObject(
                          eventObject,
                          eventTimeZone,
                          getDate[0].toDate(),
                          startDate,
                          eventObject.endDate,
                          uniqueNumber,
                          eventObject.recurrence,
                        );
                        eventsArray.push(recurrenceObject);
                      }
                    }
                  } else if (allMonthsInPeriod[k]) {
                    const dayOfMonth = eventObject.monthlyRepeatType;
                    const week = dayOfMonth.split(' ')[0].toLowerCase();
                    const day = dayOfMonth.split(' ')[1].toLowerCase();
                    const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM')
                      .startOf('month')
                      .day(day);
                    const getAllDays = [];
                    if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                    const month = chosenDay.month();
                    while (month === chosenDay.month()) {
                      getAllDays.push(chosenDay.toString());
                      chosenDay.add(7, 'd');
                    }
                    let i = 0;
                    if (week === 'second') {
                      i += 1;
                    } else if (week === 'third') {
                      i += 2;
                    } else if (week === 'fourth') {
                      i += 3;
                    } else if (week === 'last') {
                      i = getAllDays.length - 1;
                    }
                    const finalDay = getAllDays[i];
                    if (
                      moment(finalDay).isBetween(
                        moment(eventObject.fromDate),
                        moment(eventObject.endDate),
                        null,
                        '[]',
                      ) ||
                      moment(finalDay).isSame(eventObject.fromDate) ||
                      moment(finalDay).isSame(eventObject.endDate)
                    ) {
                      const recurrenceObject = await this.createRecurrenceObject(
                        eventObject,
                        eventTimeZone,
                        finalDay,
                        startDate,
                        eventObject.endDate,
                        uniqueNumber,
                        eventObject.recurrence,
                      );
                      eventsArray.push(recurrenceObject);
                    }
                  }
                  k =
                    eventObject.recurrence === 'Monthly'
                      ? k + +eventObject.repeatEveryCount
                      : k + 1;
                }
              }
            }
          }
          if (eventsArray.length > 0) {
            const resultantArray = [];
            if (req.query.weeklyReportTest === 'weeklyReport') {
              let startingDate = moment(req.query.start).format('YYYY-MM-DD');
              let endingDate = moment(req.query.end).format('YYYY-MM-DD');
              if (req.body.startDate && req.body.endDate) {
                startingDate = moment(req.body.startDate).format('YYYY-MM-DD');
                endingDate = moment(req.body.endDate).format('YYYY-MM-DD');
              }
              const nextDay = moment(startingDate).add(1, 'days').format('YYYY-MM-DD');
              eventsArray.forEach(async (data) => {
                const target = momenttz
                  .utc(data.fromDate, 'YYYY-MM-DD HH:mm:ssZ')
                  .tz(req.body.timezone);
                const timeRangeCondition = target.format('HH:mm');
                const compare = moment(target.format('YYYY-MM-DD'));
                if (req.body.eventStartTime < req.body.eventEndTime) {
                  if (
                    compare.isBetween(startingDate, endingDate, null, '[]') &&
                    timeRangeCondition <= req.body.eventEndTime &&
                    timeRangeCondition >= req.body.eventStartTime
                  ) {
                    resultantArray.push(data);
                  }
                } else if (req.body.eventStartTime > req.body.eventEndTime) {
                  if (
                    (compare.isBetween(startingDate, endingDate, null, '[]') &&
                      timeRangeCondition >= req.body.eventStartTime &&
                      timeRangeCondition <= '23:59:59') ||
                    (compare.isBetween(nextDay, endingDate, null, '[]') &&
                      timeRangeCondition >= '00:00:00' &&
                      timeRangeCondition <= req.body.eventEndTime)
                  ) {
                    resultantArray.push(data);
                  }
                }
              });
            } else {
              const startingDate = moment(req.query.start).subtract(2, 'days').format('YYYY-MM-DD');
              const endingDate = moment(req.query.end).add(2, 'days').format('YYYY-MM-DD');
              eventsArray.forEach(async (data) => {
                if (
                  moment(moment(data.fromDate).format('YYYY-MM-DD')).isBetween(
                    startingDate,
                    endingDate,
                    null,
                    '[]',
                  )
                ) {
                  resultantArray.push(data);
                }
              });
            }
            return resultantArray;
          }
          return eventsArray;
        }
        return events;
      }
    } catch (e) {
      const newError = new ApiError(e, status.BAD_REQUEST);
      next(newError);
    }
  },
  async addEvent(req) {
    try {
      await this.getDynamicModel(req);
      const event = req.body;
      event.ProjectId = req.query.ProjectId;
      event.ParentCompanyId = req.query.ParentCompanyId;
      const newEvent = await CalendarSetting.createInstance(event);
      return newEvent;
    } catch (e) {
      console.log(e);
    }
  },
  async updateEvent(req, next) {
    try {
      await this.getDynamicModel(req);
      const { id } = req.params;
      const isEventExists = await CalendarSetting.isExits(id);
      if (isEventExists) {
        let data = {};
        const { ProjectId } = req.query;
        const { ParentCompanyId } = req.query;
        data = req.body;
        data.ProjectId = ProjectId;
        data.ParentCompanyId = ParentCompanyId;
        const updatedEvent = await CalendarSetting.updateInstance(+id, data);
        return updatedEvent;
      }
      const error = new ApiError('No event found.', status.NOT_FOUND);
      next(error);
    } catch (e) {
      console.log(e);
    }
  },
  async getCalendarEvent(req, next) {
    try {
      const { ProjectId } = req.query;
      const { ParentCompanyId } = req.query;
      const { id } = req.params;
      await this.getDynamicModel(req);
      const loginUser = req.user;
      const isMemberExists = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId,
          isDeleted: false,
          ParentCompanyId,
        }),
      });
      if (isMemberExists) {
        const event = await CalendarSetting.getOne(id);
        const eventTimeZone = await TimeZone.findOne({
          where: {
            isDeleted: false,
            id: +event.TimeZoneId,
          },
          attributes: [
            'id',
            'location',
            'isDayLightSavingEnabled',
            'timeZoneOffsetInMinutes',
            'dayLightSavingTimeInMinutes',
            'timezone',
          ],
        });
        if (event) {
          let startTime1 = '';
          let startTime2 = '';
          startTime1 = await this.convertTimezoneToUtc(
            moment(event.fromDate).format('MM/DD/YYYY'),
            eventTimeZone.timezone,
            event.startTime,
          );
          startTime2 = await this.convertTimezoneToUtc(
            moment(event.toDate).format('MM/DD/YYYY'),
            eventTimeZone.timezone,
            event.endTime,
          );
          let fromDate1 = '';
          let toDate1 = '';
          let endDate1 = '';
          fromDate1 = await this.convertTimezoneToUtc(
            moment(event.fromDate).format('MM/DD/YYYY'),
            eventTimeZone.timezone,
            '00:00',
          );
          toDate1 = await this.convertTimezoneToUtc(
            moment(event.toDate).format('MM/DD/YYYY'),
            eventTimeZone.timezone,
            '00:00',
          );
          endDate1 = await this.convertTimezoneToUtc(
            moment(event.endDate).format('MM/DD/YYYY'),
            eventTimeZone.timezone,
            '00:00',
          );
          event.startTime = '';
          event.endTime = '';
          event.toDate = '';
          event.fromDate = '';
          event.startTime = startTime1;
          event.endTime = startTime2;
          event.fromDate = fromDate1;
          event.toDate = toDate1;
          event.endDate = endDate1;
          return event;
        }
        return {};
      }
    } catch (e) {
      const newError = new ApiError(e, status.BAD_REQUEST);
      next(newError);
    }
  },
  async convertTimezoneToUtc(date, timezone, time) {
    const chosenTimezoneDeliveryStart = moment.tz(`${date} ${time}`, 'MM/DD/YYYY HH:mm', timezone);
    const utcDate = chosenTimezoneDeliveryStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
    return utcDate;
  },
  async createRecurrenceObject(
    eventObject,
    eventTimeZone,
    data,
    startDate,
    endDate,
    uniqueNumber,
    recurrence,
  ) {
    const objectToAddEvents = {
      id: eventObject.id,
      description: eventObject.description,
      timeZoneLocation: eventObject.TimeZone.location,
      durationInMinutes: '',
      fromDate: eventObject.isAllDay
        ? await this.convertTimezoneToUtc(
            moment(data).startOf('day').format('MM/DD/YYYY'),
            eventTimeZone.timezone,
            '00:00',
          )
        : await this.convertTimezoneToUtc(
            moment(data).format('MM/DD/YYYY'),
            eventTimeZone.timezone,
            eventObject.startTime,
          ),
      toDate: eventObject.isAllDay
        ? await this.convertTimezoneToUtc(
            moment(data).endOf('day').format('MM/DD/YYYY'),
            eventTimeZone.timezone,
            '23:59',
          )
        : await this.convertTimezoneToUtc(
            moment(data).format('MM/DD/YYYY'),
            eventTimeZone.timezone,
            eventObject.endTime,
          ),
      startTime: await this.convertTimezoneToUtc(
        moment(startDate).format('MM/DD/YYYY'),
        eventTimeZone.timezone,
        eventObject.startTime,
      ),
      endTime: eventObject.isAllDay
        ? await this.convertTimezoneToUtc(
            moment(data).startOf('day').format('MM/DD/YYYY'),
            eventTimeZone.timezone,
            '00:00',
          )
        : await this.convertTimezoneToUtc(
            moment(endDate).format('MM/DD/YYYY'),
            eventTimeZone.timezone,
            eventObject.endTime,
          ),
      repeatEveryType: eventObject.repeatEveryType,
      repeatEveryCount: eventObject.repeatEveryCount,
      days: eventObject.days,
      isAllDay: eventObject.isAllDay,
      uniqueNumber,
      requestType: 'calendarEvent',
    };
    if (recurrence === 'Monthly' || recurrence === 'Yearly') {
      objectToAddEvents.chosenDateOfMonth = eventObject.chosenDateOfMonth;
      objectToAddEvents.dateOfMonth = eventObject.dateOfMonth;
      objectToAddEvents.monthlyRepeatType = eventObject.monthlyRepeatType;
    }
    objectToAddEvents.durationInMinutes = moment(objectToAddEvents.toDate)
      .diff(moment(objectToAddEvents.fromDate), 'minutes')
      .toString();
    return objectToAddEvents;
  },
};
module.exports = calendarSettingsService;
