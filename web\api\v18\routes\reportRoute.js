const { Router } = require('express');
const { validate } = require('express-validation');

const passportConfig = require('../config/passport');
const { ReportController, CornController } = require('../controllers');
const { deliveryReportsValidation } = require('../middlewares/validations');

const reportRoute = {
  get router() {
    const router = Router();
    router.post(
      '/delivery_request/:ProjectId/:pageSize/:pageNo/:void',
      validate(
        deliveryReportsValidation.DeliveryReports,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      ReportController.deliveryRequest,
    );
    router.post(
      '/crane_request/:ProjectId/:pageSize/:pageNo/:void',
      passportConfig.isAuthenticated,
      ReportController.craneRequest,
    );
    router.post(
      '/concrete_request/:ProjectId/:pageSize/:pageNo/:void',
      passportConfig.isAuthenticated,
      ReportController.concreteRequest,
    );
    router.post(
      '/export_delivery_request/:ProjectId/:pageSize/:pageNo/:void',
      passportConfig.isAuthenticated,
      ReportController.exportDeliveryReport,
    );
    router.post(
      '/saved/export_delivery_request/:ProjectId/:pageSize/:pageNo/:void',
      (req, res, next) => {
        req.body.saved = true;
        next();
      },
      validate(deliveryReportsValidation.SavedReports, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      ReportController.exportDeliveryReport,
    );
    router.post(
      '/export_crane_request/:ProjectId/:pageSize/:pageNo/:void',
      passportConfig.isAuthenticated,
      ReportController.exportCraneReport,
    );

    router.post(
      '/saved/export_crane_request/:ProjectId/:pageSize/:pageNo/:void',
      (req, res, next) => {
        req.body.saved = true;
        next();
      },
      validate(deliveryReportsValidation.SavedReports, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      ReportController.exportCraneReport,
    );

    router.post(
      '/export_concrete_request/:ProjectId/:pageSize/:pageNo/:void',
      passportConfig.isAuthenticated,
      ReportController.exportConcreteReport,
    );

    router.post(
      '/saved/export_concrete_request/:ProjectId/:pageSize/:pageNo/:void',
      (req, res, next) => {
        req.body.saved = true;
        next();
      },
      validate(deliveryReportsValidation.SavedReports, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      ReportController.exportConcreteReport,
    );

    router.post(
      '/weekly_calendar_request/:ProjectId/:void',
      passportConfig.isAuthenticated,
      ReportController.weeklyCalendarRequest,
    );
    router.post(
      '/weekly_calendar_request/no_auth/:ProjectId/:void',
      async (req, res, next) => {
        req.user = await passportConfig.findUserPayload(
          JSON.parse(JSON.stringify(req.body.noAuth)),
        );
        next();
      },
      ReportController.weeklyCalendarRequest,
    );
    router.post(
      '/export_weekly_calendar/:ProjectId/:void',
      passportConfig.isAuthenticated,
      ReportController.exportWeeklyCalendarRequest,
    );

    router.post(
      '/saved/export_weekly_calendar/:ProjectId/:void',
      (req, res, next) => {
        req.body.saved = true;
        next();
      },
      validate(deliveryReportsValidation.SavedReports, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      ReportController.exportWeeklyCalendarRequest,
    );

    router.post(
      '/heat_map/:ProjectId/:pageSize/:pageNo/:void/:sortOrder',
      validate(
        deliveryReportsValidation.HeatMapDeliveryReports,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      ReportController.heatMapdeliveryRequest,
    );
    router.post(
      '/export_heat_map/:ProjectId/:pageSize/:pageNo/:void/:sortOrder',
      passportConfig.isAuthenticated,
      ReportController.exportHeatMapRequest,
    );
    router.post(
      '/saved/export_heat_map/:ProjectId/:pageSize/:pageNo/:void/:sortOrder',
      (req, res, next) => {
        req.body.saved = true;
        next();
      },
      validate(deliveryReportsValidation.SavedReports, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      ReportController.exportHeatMapRequest,
    );

    router.get(
      '/schedule',
      (req, res, next) => {
        req.body.saved = false;
        next();
      },
      validate(
        deliveryReportsValidation.GetScheduleReports,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      CornController.getSchedulerReportRequest,
    );
    router.post(
      '/schedule/:ProjectId/',
      validate(
        deliveryReportsValidation.ScheduleReports,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      CornController.schedulerReportRequest,
    );
    router.delete(
      '/schedule',
      validate(
        deliveryReportsValidation.deleteSchedulerReport,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      ReportController.deleteSchedulerReport,
    );
    router.get(
      '/saved-reports',
      (req, res, next) => {
        req.body.saved = true;
        next();
      },
      validate(
        deliveryReportsValidation.GetScheduleReports,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      CornController.getSchedulerReportRequest,
    );
    router.get(
      '/recent-reports',
      validate(
        deliveryReportsValidation.GetScheduleReports,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      CornController.getSchedulerReportRequest,
    );
    router.get(
      '/rerun',
      validate(
        deliveryReportsValidation.rerunSchedulerReport,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      CornController.getRerunReportRequest,
    );
    router.get(
      '/scheduler-timeline-names',
      passportConfig.isAuthenticated,
      CornController.getSchedulerTimelineNames,
    );
    return router;
  },
};
module.exports = reportRoute;
