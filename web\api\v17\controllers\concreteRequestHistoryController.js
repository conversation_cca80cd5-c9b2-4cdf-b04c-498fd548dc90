const status = require('http-status');
const { concreteRequestHistoryService } = require('../services');

const concreteRequestHistoryController = {
  async createConcreteRequestHistory(req, res, next) {
    try {
      concreteRequestHistoryService.createConcreteRequestHistory(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Concrete Booking History created successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getConcreteRequestHistories(req, res, next) {
    try {
      await concreteRequestHistoryService.getConcreteRequestHistories(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Concrete Booking History Viewed Successfully.',
            data: response.historyList,
            concreteRequest: response.exist,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = concreteRequestHistoryController;
