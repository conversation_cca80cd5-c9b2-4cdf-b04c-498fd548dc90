const { Router } = require('express');
const { validate } = require('express-validation');
const multer = require('multer');
const { projectValidation } = require('../middlewares/validations');
const { ProjectController } = require('../controllers');
const passportConfig = require('../config/passport');
const checkAdmin = require('../middlewares/checkAdmin');

const storage = multer.memoryStorage();
const upload = multer({ storage });

const projectRoute = {
  get router() {
    const router = Router();
    router.post(
      '/create_project',
      validate(projectValidation.createProject, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      ProjectController.createProject,
    );
    router.post(
      '/create_account_project',
      validate(projectValidation.createAccountProject, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isAccountAdmin,
      ProjectController.createAccountProject,
    );
    router.post('/edit_project', passportConfig.isAuthenticated, ProjectController.editProject);
    router.post(
      '/exist_project',
      validate(projectValidation.existProject, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      ProjectController.existProject,
    );
    router.post(
      '/upgrade_plan',
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdmin,
      validate(projectValidation.upgradePlan, { keyByField: true }, { abortEarly: false }),
      ProjectController.upgradePlan,
    );
    router.post(
      '/get_plans_projects/:pageSize/:pageNo/:sort/:sortByField',
      validate(projectValidation.getPlansAndProjects, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      ProjectController.getPlansAndProjects,
    );
    router.post(
      '/get_projects_list/:pageSize/:pageNo/?:ParentCompanyId',
      validate(projectValidation.getProjects, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      ProjectController.getProjects,
    );
    router.get('/get_project', passportConfig.isAuthenticated, ProjectController.getUserProjects);

    router.get(
      '/get_accounts_company',
      passportConfig.isAuthenticated,
      ProjectController.getProjectsCompany,
    );
    router.get(
      '/get_account_projects/:companyId',
      passportConfig.isAuthenticated,
      ProjectController.getAccountProjects,
    );
    router.get(
      '/get_single_project/:ProjectId',
      passportConfig.isAuthenticated,
      validate(
        projectValidation.getSingleProjectDetail,
        { keyByField: true },
        { abortEarly: false },
      ),
      ProjectController.getSingleProjectDetail,
    );
    router.get(
      '/get_project_profile/:ProjectId',
      passportConfig.isAuthenticated,
      validate(
        projectValidation.getSingleProjectDetail,
        { keyByField: true },
        { abortEarly: false },
      ),
      ProjectController.getSingleProjectProfileDetail,
    );
    router.get(
      '/projects',
      passportConfig.isAuthenticated,
      checkAdmin.isAdmin,
      ProjectController.getProjectList,
    );
    router.post(
      '/assign_new_project_to_member',
      passportConfig.isAuthenticated,
      checkAdmin.isAdmin,
      ProjectController.assignNewProjectToMember,
    );
    router.get(
      '/get_member_project/:id',
      passportConfig.isAuthenticated,
      checkAdmin.isAdmin,
      ProjectController.getMemberProject,
    );
    router.put(
      '/edit_member_project/:id',
      passportConfig.isAuthenticated,
      checkAdmin.isAdmin,
      ProjectController.editMemberProject,
    );
    router.get(
      '/projects_billing_histories',
      passportConfig.isAuthenticated,
      checkAdmin.isAdmin,
      ProjectController.projectsBillingHistories,
    );
    router.get(
      '/get_project_billing_histories/:id',
      passportConfig.isAuthenticated,
      checkAdmin.isAdmin,
      ProjectController.getProjectBillingHistories,
    );
    router.get('/project_list', ProjectController.getTotalProjects);

    router.put('/extend_project', ProjectController.extendProjectDuration);

    router.put(
      '/project_sharing_settings',
      passportConfig.isAuthenticated,
      ProjectController.updateProjectSharingSettings,
    );

    router.post(
      '/upload_logistic_plan',
      upload.single('projectPlan'),
      passportConfig.isAuthenticated,
      ProjectController.uploadProjectLogisticPlanUrl,
    );

    router.get(
      '/generate_project_public_url',
      ProjectController.generatePublicUrlForExistingProjects,
    );

    router.put('/decode_project_detail_url', ProjectController.decodeProjectDetailUrl );

    router.put(
      '/update_dashboard_logistic_plan',
      passportConfig.isAuthenticated,
      ProjectController.updateDashboardLogisticPlan,
    );
    router.get(
      '/retool_parentCompanyWithProjects',
      ProjectController.retoolParentCompanyWithProjects,
    );
    
    return router;
  },
};
module.exports = projectRoute;
