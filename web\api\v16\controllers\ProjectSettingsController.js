const status = require('http-status');
const { projectSettingsService } = require('../services');
const { Project, ProjectSettings } = require('../models');

const ProjectSettingsController = {
  /**
   *
   * @param {project settings list} res
   * @param {return if any exception} next
   */
  async getProjectSettings(req, res, next) {
    try {
      const { ProjectId } = req.query;
      const projectSettings = await projectSettingsService.getProjectSettings(ProjectId);
      res.status(status.OK).json({
        status: 200,
        message: 'Project Settings listed Successfully',
        data: projectSettings,
      });
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {project settings list} res
   * @param {return if any exception} next
   */
  async updateProjectSettings(req, res, next) {
    try {
      const projectSettings = await projectSettingsService.updateProjectSettings(req.body);
      if (projectSettings) {
        res.status(status.OK).json({
          status: 200,
          message: 'Project Settings Updated Successfully',
        });
      } else {
        res.status(status.INTERNAL_SERVER_ERROR).json({
          status: 500,
          message: 'Cannot update project settings',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {success} res
   * @param {return if any exception} next
   */
  async setDefaultDeliveryWindow(req, res, next) {
    try {
      const project = await Project.findAll();
      const createObject = {
        deliveryWindowTime: 0,
        deliveryWindowTimeUnit: 'minutes',
      };
      for (let i = 0; i < project.length; i += 1) {
        createObject.ProjectId = project[i].id;
        await ProjectSettings.create(createObject);
      }
      res.status(status.OK).json({
        status: 200,
        message: 'success',
      });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = ProjectSettingsController;
