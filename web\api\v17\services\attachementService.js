const {
  Sequelize,
  Enterprise,
  NotificationPreference,
  Locations,
  LocationNotificationPreferences,
} = require('../models');

let {
  DeliveryRequest,
  DeliverAttachement,
  DeliverHistory,
  Member,
  User,
  DeliveryPerson,
  DeliveryPersonNotification,
  Project,
  Notification,
} = require('../models');
const helper = require('../helpers/domainHelper');
const notificationHelper = require('../helpers/notificationHelper');

const pushNotification = require('../config/fcm');

const awsConfig = require('../middlewares/awsConfig');

const MAILER = require('../mailer');

const { Op } = Sequelize;
let publicUser;
let publicMember;

const attachementService = {
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    let enterpriseValue;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    const incomeData = inputData;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    DeliveryRequest = modelObj.DeliveryRequest;
    Member = modelObj.Member;
    DeliverAttachement = modelObj.DeliverAttachement;
    DeliveryPerson = modelObj.DeliveryPerson;
    Project = modelObj.Project;
    User = modelObj.User;
    DeliverHistory = modelObj.DeliverHistory;
    Notification = modelObj.Notification;
    DeliveryPersonNotification = modelObj.DeliveryPersonNotification;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return true;
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    // publicProject = modelData.Project;
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async getAttachement(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const exist = await DeliveryRequest.findOne({
        where: { id: inputData.params.DeliveryRequestId },
      });
      if (exist) {
        const attachementList = await DeliverAttachement.findAll({
          where: {
            DeliveryRequestId: inputData.params.DeliveryRequestId,
            isDeleted: false,
          },
          order: [['id', 'DESC']],
        });
        done(attachementList, false);
      } else {
        done(null, { message: 'Delivery Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async deleteAttachement(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const loginUser = inputData.user;
      const { params } = inputData;
      const attachement = await DeliverAttachement.findOne({
        where: { id: params.id },
        include: [
          {
            association: 'DeliveryRequest',
          },
        ],
      });
      const { ProjectId } = attachement.DeliveryRequest;
      const memberDetail = await Member.findOne({
        where: [
          Sequelize.and({
            UserId: inputData.user.id,
            ProjectId,
            isDeleted: false,
          }),
        ],
      });
      await DeliverAttachement.update(
        { isDeleted: true },
        {
          where: {
            id: inputData.params.id,
          },
        },
      );
      const exist = await DeliveryRequest.findOne({ where: { id: attachement.DeliveryRequestId } });
      const locationChosen = await Locations.findOne({
        where: {
          ProjectId: exist.ProjectId,
          id: exist.LocationId,
        },
      });
      const memberLocationPreference = await LocationNotificationPreferences.findAll({
        where: {
          ProjectId: exist.ProjectId,
          LocationId: exist.LocationId,
          follow: true,
        },
        include: [
          {
            association: 'Member',
            attributes: ['id', 'RoleId'],
            where: {
              [Op.and]: [
                {
                  id: { [Op.ne]: memberDetail.id },
                },
              ],
            },
            include: [
              {
                association: 'User',
                attributes: ['id', 'firstName', 'lastName', 'email'],
              },
            ],
          },
        ],
      });
      const locationFollowMembers = [];
      memberLocationPreference.forEach(async (element) => {
        locationFollowMembers.push(element.Member.id);
      });
      const history = {
        DeliveryRequestId: attachement.DeliveryRequestId,
        DeliveryId: exist.DeliveryId,
        MemberId: memberDetail.id,
        type: 'attachement',
        description: `${inputData.user.firstName} ${inputData.user.lastName} Removed the file in ${exist.description}`,
        locationFollowDescription: `${inputData.user.firstName} ${inputData.user.lastName} Removed the file in the Booking, ${exist.description}. Location: ${locationChosen.locationPath}.`,
      };
      const notification = history;
      DeliverHistory.createInstance(history);
      notification.ProjectId = ProjectId;
      notification.title = 'Delivery Booking Attachment';
      history.firstName = loginUser.firstName;
      history.profilePic = loginUser.profilePic;
      history.createdAt = new Date();
      history.ProjectId = ProjectId;
      const projectDetails = await Project.findByPk(ProjectId);
      history.projectName = projectDetails.projectName;
      notification.requestType = 'deliveryRequest';
      const newNotification = await Notification.createInstance(notification);
      const personData = await DeliveryPerson.findAll({
        where: { DeliveryId: attachement.DeliveryRequestId, isDeleted: false },
        include: [
          {
            association: 'Member',
            include: [
              {
                association: 'User',
                attributes: ['id', 'firstName', 'lastName'],
              },
            ],
            where: {
              [Op.and]: {
                RoleId: {
                  [Op.notIn]: [1, 2],
                },
                id: { [Op.notIn]: locationFollowMembers },
              },
            },
            attributes: ['id', 'RoleId'],
          },
        ],
        attributes: ['id'],
      });
      const adminData = await Member.findAll({
        where: {
          [Op.and]: [
            { ProjectId },
            { isDeleted: false },
            { id: { [Op.ne]: newNotification.MemberId } },
            { id: { [Op.notIn]: locationFollowMembers } },
          ],
        },
        include: [
          {
            association: 'User',
            attributes: ['id', 'firstName', 'lastName'],
          },
        ],
        attributes: ['id'],
      });
      if (memberLocationPreference && memberLocationPreference.length > 0) {
        await pushNotification.sendMemberLocationPreferencePushNotification(
          memberLocationPreference,
          exist.DeliveryRequestId,
          history.locationFollowDescription,
          exist.requestType,
          exist.ProjectId,
          exist.id,
          2,
        );
        await notificationHelper.createMemberDeliveryLocationInAppNotification(
          DeliveryPersonNotification,
          exist.ProjectId,
          newNotification.id,
          memberLocationPreference,
          2,
        );
      }
      history.memberData = personData;
      history.adminData = adminData;
      const checkMemberNotification = await NotificationPreference.findAll({
        where: {
          ProjectId,
          isDeleted: false,
        },
        attributes: [
          'id',
          'MemberId',
          'ProjectId',
          'ParentCompanyId',
          'NotificationPreferenceItemId',
          'instant',
          'dailyDigest',
        ],
        include: [
          {
            association: 'NotificationPreferenceItem',
            where: {
              id: 2,
              isDeleted: false,
            },
            attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
          },
        ],
      });
      history.notificationPreference = checkMemberNotification;
      await notificationHelper.createDeliveryPersonNotification(
        adminData,
        personData,
        projectDetails,
        newNotification,
        DeliveryPersonNotification,
        memberDetail,
        loginUser,
        2,
        'deleted an attachment in',
        'Delivery Request',
        `delivery Booking (${exist.DeliveryId} - ${exist.description})`,
        attachement.DeliveryRequestId,
      );
      await pushNotification.sendDeviceToken(history, 2, ProjectId);
      done(history, false);
    } catch (e) {
      done(null, e);
    }
  },
  async createAttachement(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const incomeData = inputData.params;
      const loginUser = inputData.user;
      const bulkData = [];
      const exist = await DeliveryRequest.findOne({ where: { id: incomeData.DeliveryRequestId } });
      const memberDetail = await Member.findOne({
        where: [
          Sequelize.and({
            UserId: inputData.user.id,
            ProjectId: exist.ProjectId,
            isDeleted: false,
          }),
        ],
      });
      if (exist) {
        const locationChosen = await Locations.findOne({
          where: {
            ProjectId: exist.ProjectId,
            id: exist.LocationId,
          },
        });
        const memberLocationPreference = await LocationNotificationPreferences.findAll({
          where: {
            ProjectId: exist.ProjectId,
            LocationId: exist.LocationId,
            follow: true,
          },
          include: [
            {
              association: 'Member',
              attributes: ['id', 'RoleId'],
              where: {
                [Op.and]: [
                  {
                    id: { [Op.ne]: memberDetail.id },
                  },
                ],
              },
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'firstName', 'lastName', 'email'],
                },
              ],
            },
          ],
        });
        const locationFollowMembers = [];
        memberLocationPreference.forEach(async (element) => {
          locationFollowMembers.push(element.Member.id);
        });
        awsConfig.upload(inputData.files, async (result, err) => {
          if (!err) {
            const exist2 = await DeliveryRequest.findOne({
              include: [
                {
                  association: 'memberDetails',
                  required: false,
                  where: { isDeleted: false, isActive: true },
                  attributes: ['id'],
                  include: [
                    {
                      association: 'Member',
                      attributes: ['id', 'isGuestUser'],
                      include: [
                        {
                          association: 'User',
                          attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                        },
                      ],
                    },
                  ],
                },
              ],
              where: { id: incomeData.DeliveryRequestId },
            });
            const userDataMail = exist2.memberDetails;
              for(let i=0;i<userDataMail.length;i++){
                let responsibleGuestUser = userDataMail[i].Member.isGuestUser;
                if(responsibleGuestUser){
                  const guestMailPayload = {
                    email:  userDataMail[i].Member.User.email,
                    guestName: userDataMail[i].Member.User.firstName,
                    content: `We would like to inform you that 
                    ${inputData.user.firstName} ${inputData.user.lastName} Attached the file in Booking, ${exist.description}.`,
                  };
                  await MAILER.sendMail(
                    guestMailPayload,
                    'notifyGuestOnEdit',
                    `Attachment Added by ${inputData.user.firstName} `,
                    'Attachment Added against Delivery Booking',
                    async (info, err) => {
                      console.log(info, err);
                    },
                  );
                }
              }
            result.forEach((element, i) => {
              const fileData = inputData.files[i];
              let fileName;
              let relativePath;
              let extension;
              if (fileData.originalname !== undefined) {
                fileName = fileData.originalname;
                relativePath = fileData.originalname.split('.');
                extension = relativePath[relativePath.length - 1];
              } else {
                fileName = fileData.name;
                relativePath = fileData.name.split('.');
                extension = relativePath[relativePath.length - 1];
              }

              const data = {
                attachement: element.Location,
                filename: fileName,
                extension,
                DeliveryRequestId: +incomeData.DeliveryRequestId,
                DeliveryId: exist.DeliveryId,
                isDeleted: false,
              };
              bulkData.push(data);
            });

            if (bulkData.length > 0) {
              await DeliverAttachement.createMultipleInstance(bulkData);
              const history = {
                DeliveryRequestId: incomeData.DeliveryRequestId,
                DeliveryId: exist.DeliveryId,
                MemberId: memberDetail.id,
                type: 'attachement',
                description: `${inputData.user.firstName} ${inputData.user.lastName} Attached the file in ${exist.description}`,
                locationFollowDescription: `${inputData.user.firstName} ${inputData.user.lastName} Attached the file in Booking, ${exist.description}. Location: ${locationChosen.locationPath}.`,
              };
              const notification = history;
              notification.ProjectId = exist.ProjectId;
              notification.title = 'Delivery Booking Attachment';
              DeliverHistory.createInstance(history);
              const personData = await DeliveryPerson.findAll({
                where: { DeliveryId: exist.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    { ProjectId: exist.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.notIn]: locationFollowMembers } },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName'],
                  },
                ],
                attributes: ['id'],
              });
              history.memberData = personData;
              history.adminData = adminData;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = exist.ProjectId;
              const projectDetails = await Project.findByPk(exist.ProjectId);
              history.projectName = projectDetails.projectName;
              notification.requestType = 'deliveryRequest';
              const newNotification = await Notification.createInstance(notification);
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                await pushNotification.sendMemberLocationPreferencePushNotification(
                  memberLocationPreference,
                  exist.DeliveryRequestId,
                  history.locationFollowDescription,
                  exist.requestType,
                  exist.ProjectId,
                  exist.id,
                  1,
                );
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  exist.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  1,
                );
              }
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                personData,
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberDetail,
                loginUser,
                1,
                'attached a file in',
                'Delivery Request',
                `delivery Booking (${exist.DeliveryId} - ${exist.description})`,
                incomeData.DeliveryRequestId,
              );
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: exist.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 1,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              await pushNotification.sendDeviceToken(history, 1, exist.ProjectId);
              done(history, false);
            }
          } else {
            done(null, err);
          }
        });
      } else {
        done(null, { message: 'Delivery Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
};

module.exports = attachementService;
