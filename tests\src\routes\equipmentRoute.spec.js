const uuid = require('uuid');

const Helper = require('../../helpers/helper');

let helper = new Helper();

const EQUIPMENT_ENDPOINT = '/api/v3/equipment';

beforeAll((done) => {
  helper.server(done);
  // avoid jest open handle error
});
afterAll((done) => {
  helper.close(done);
  // avoid jest open handle error
});

/**
 * @test {authRoutes.js}
 */
describe(`${EQUIPMENT_ENDPOINT}`, () => {
  beforeAll(async () => {
    const res = await helper.apiServer.post(`/api/v3/auth/login`).send({
      email: '<EMAIL>',
      password: 'Test@123',
    });
    helper = new Helper(res.body.token);
  });
  const EQUIPMENT_END_POINT = `${EQUIPMENT_ENDPOINT}/add_equipment`;
  const UPDATE_EQUIPMENT_END_POINT = `${EQUIPMENT_ENDPOINT}/update_equipment`;
  const DELETE_EQUIPMENT_END_POINT = `${EQUIPMENT_ENDPOINT}/delete_equipments`;

  describe(`POST ${EQUIPMENT_END_POINT}`, () => {
    const equipmentNewData = {
      equipmentName: uuid.v1() + 1,
      equipmentType: uuid.v1() + 1,
      controlledBy: 198,
      ProjectId: 134,
    };
    describe('Create the equipment with 201', () => {
      it('It should return 201 when add the equipment', async () => {
        const res = await helper.apiServer.post(EQUIPMENT_END_POINT).send(equipmentNewData);
        const { status, body } = res;
        expect(status).toEqual(201);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Equipment added successfully.');
      });
    });
  });

  describe(`POST ${UPDATE_EQUIPMENT_END_POINT}`, () => {
    const updateEquipment = {
      equipmentName: uuid.v1() + 1,
      equipmentType: uuid.v1() + 1,
      controlledBy: 198,
      id: 1,
      ProjectId: 134,
    };
    describe('Update the equipment with 200', () => {
      it('It should return 200 when update the equipment', async () => {
        const res = await helper.apiServer.post(UPDATE_EQUIPMENT_END_POINT).send(updateEquipment);
        const { status, body } = res;
        expect(status).toEqual(200);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Equipment Updated successfully.');
      });
    });
    describe('Update the equipment name already exist', () => {
      it('It should return 400 when update the equipment', async () => {
        const testData = {
          equipmentName: '3c537890-1d4e-11eb-afdc-33a0c368c6f41',
          id: 1,
          equipmentType: uuid.v1() + 1,
          controlledBy: 198,
          ProjectId: 134,
        };
        const res = await helper.apiServer.post(UPDATE_EQUIPMENT_END_POINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Equipment Name Already exist.');
      });
    });
    describe('Update the equipment id does not exist. exist', () => {
      it('It should return 500 when update the equipment id does not exist.', async () => {
        const testData1 = {
          equipmentName: uuid.v1() + 1,
          equipmentType: uuid.v1() + 1,
          controlledBy: 198,
          id: -1,
          ProjectId: 134,
        };
        const res = await helper.apiServer.post(UPDATE_EQUIPMENT_END_POINT).send(testData1);
        const { status, body } = res;
        expect(status).toEqual(500);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Equipment id does not exist.');
      });
    });
  });
  describe(`POST ${DELETE_EQUIPMENT_END_POINT}`, () => {
    const deleteEquipmentData = {
      id: [1],
      ProjectId: 134,
      isSelectAll: false,
    };
    describe('Delete Equipment', () => {
      it('It Should Delete Equipment', async () => {
        const res = await helper.apiServer
          .post(`${DELETE_EQUIPMENT_END_POINT}`)
          .send(deleteEquipmentData);
        const { status, body } = res;
        expect(status).toEqual(200);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Equipment deleted successfully.');
      });
    });
    describe('Delete All Equipment', () => {
      it('It Should All Delete Equipment', async () => {
        deleteEquipmentData.isSelectAll = true;
        const res = await helper.apiServer
          .post(`${DELETE_EQUIPMENT_END_POINT}`)
          .send(deleteEquipmentData);
        const { status, body } = res;
        expect(status).toEqual(200);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Equipment deleted successfully.');
      });
    });
  });
});
