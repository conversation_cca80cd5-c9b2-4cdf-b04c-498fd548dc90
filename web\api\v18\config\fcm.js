const FCM = require('fcm-push');
const { DeviceToken, NotificationPreference } = require('../models');
// fcm server key
const serverKey = process.env.DEVICE_SERVER_KEY;
const fcm = new FCM(serverKey);

const fcmNot = {
  async sendDeviceToken(history, NotificationPreferenceItemId, ProjectId) {
    try {
      const { adminData } = history; // RoleId - 1(Account admin),2(Project Admin)
      const { memberData } = history;
      const existAdminData = [];
      if (adminData) {
        adminData.map(async (item) => {
          const getMemberNotificationPeference = await this.checkMemberNotificationPreference(
            +ProjectId,
            +item.id,
            +NotificationPreferenceItemId,
          );
          if (
            getMemberNotificationPeference &&
            getMemberNotificationPeference.NotificationPreferenceItem &&
            getMemberNotificationPeference.NotificationPreferenceItem.inappNotification
          ) {
            if (getMemberNotificationPeference && getMemberNotificationPeference.instant) {
              const index = existAdminData.findIndex(
                (adminNew) => adminNew.userId === +item.User.id,
              );
              if (index === -1) {
                existAdminData.push({ userId: +item.User.id });
                const deviceNewValue = await DeviceToken.findOne({
                  where: { UserId: item.User.id },
                });
                if (deviceNewValue) {
                  const message = {
                    to: deviceNewValue.deviceToken,
                    data: {
                      show_in_foreground: true,
                      priority: 'high',
                    },
                    notification: {
                      title: `Delivery ID - ${history.DeliveryId}`,
                      body: history.description,
                    },
                  };

                  message.notification.sound = 'default';
                  message.data.sound = 'default';
                  // add extra fields
                  message.data.type = history.requestType;
                  message.data.projectId = ProjectId;
                  message.data.id = history.DeliveryRequestId;
                  message.data.DeliveryId = history.DeliveryId;
                  fcm
                    .send(message)
                    .then((response) => {
                      console.log('Successfully sent with response: ', response);
                    })
                    .catch(async (err) => {
                      console.error(err);
                      if (err === 'NotRegistered') {
                        await DeviceToken.findOne({
                          where: { deviceToken: deviceNewValue.deviceToken },
                        });
                      }
                    });
                }
              }
            }
          }
        });
      }
      if (memberData) {
        memberData.map(async (item) => {
          const getMemberNotificationPeference = await this.checkMemberNotificationPreference(
            +ProjectId,
            +item.Member.id,
            +NotificationPreferenceItemId,
          );
          if (
            getMemberNotificationPeference &&
            getMemberNotificationPeference.NotificationPreferenceItem &&
            getMemberNotificationPeference.NotificationPreferenceItem.inappNotification
          ) {
            if (getMemberNotificationPeference && getMemberNotificationPeference.instant) {
              const index = existAdminData.findIndex(
                (adminNew) => adminNew.userId === +item.Member.User.get('id'),
              );
              if (index === -1) {
                existAdminData.push({ userId: item.Member.User.get('id') });
                const deviceValue = await DeviceToken.findOne({
                  where: { UserId: item.Member.User.get('id') },
                });
                if (deviceValue) {
                  const message = {
                    to: deviceValue.deviceToken,
                    data: {
                      show_in_foreground: true,
                      priority: 'high',
                    },
                    notification: {
                      title: `Delivery ID - ${history.DeliveryId}`,
                      body: history.description,
                    },
                  };

                  message.notification.sound = 'default';
                  message.data.sound = 'default';
                  // add extra fields
                  message.data.type = history.requestType;
                  message.data.projectId = ProjectId;
                  message.data.id = history.DeliveryRequestId;
                  message.data.DeliveryId = history.DeliveryId;
                  fcm
                    .send(message)
                    .then((response) => {
                      console.log('Successfully sent with response: ', response);
                    })
                    .catch(async (err) => {
                      console.error(err);
                      if (err === 'NotRegistered') {
                        await DeviceToken.findOne({
                          where: { deviceToken: deviceValue.deviceToken },
                        });
                      }
                    });
                }
              }
            }
          }
        });
      }
    } catch (error) {
      console.log('error', error);
    }
  },
  async sendPushNotificationForCrane(history, NotificationPreferenceItemId, ProjectId) {
    try {
      const { adminData } = history; // RoleId - 1(Account admin),2(Project Admin)
      const { memberData } = history;
      const existAdminData = [];
      if (adminData) {
        adminData.map(async (item) => {
          const getMemberNotificationPeference = await this.checkMemberNotificationPreference(
            +ProjectId,
            +item.id,
            +NotificationPreferenceItemId,
          );
          if (
            getMemberNotificationPeference &&
            getMemberNotificationPeference.NotificationPreferenceItem &&
            getMemberNotificationPeference.NotificationPreferenceItem.inappNotification
          ) {
            if (getMemberNotificationPeference && getMemberNotificationPeference.instant) {
              const index = existAdminData.findIndex(
                (adminNew) => adminNew.userId === +item.User.id,
              );
              if (index === -1) {
                existAdminData.push({ userId: +item.User.id });
                const deviceNewValue = await DeviceToken.findOne({
                  where: { UserId: item.User.id },
                });
                if (deviceNewValue) {
                  const message = {
                    to: deviceNewValue.deviceToken,
                    data: {
                      show_in_foreground: true,
                      priority: 'high',
                    },
                    notification: {
                      title: `Crane Pick ID - ${history.CraneRequestId}`,
                      body: history.description,
                    },
                  };

                  message.notification.sound = 'default';
                  message.data.sound = 'default';
                  // add extra fields
                  message.data.type = history.requestType;
                  message.data.projectId = ProjectId;
                  message.data.CraneRequestId = history.CraneRequestId;
                  fcm
                    .send(message)
                    .then((response) => {
                      console.log('Successfully sent with response: ', response);
                    })
                    .catch(async (err) => {
                      console.error(err);
                      if (err === 'NotRegistered') {
                        await DeviceToken.findOne({
                          where: { deviceToken: deviceNewValue.deviceToken },
                        });
                      }
                    });
                }
              }
            }
          }
        });
      }
      if (memberData) {
        memberData.map(async (item) => {
          const getMemberNotificationPeference = await this.checkMemberNotificationPreference(
            +ProjectId,
            +item.Member.id,
            +NotificationPreferenceItemId,
          );
          if (
            getMemberNotificationPeference &&
            getMemberNotificationPeference.NotificationPreferenceItem &&
            getMemberNotificationPeference.NotificationPreferenceItem.inappNotification
          ) {
            if (getMemberNotificationPeference && getMemberNotificationPeference.instant) {
              const index = existAdminData.findIndex(
                (adminNew) => adminNew.userId === +item.Member.User.get('id'),
              );
              if (index === -1) {
                existAdminData.push({ userId: item.Member.User.get('id') });
                const deviceValue = await DeviceToken.findOne({
                  where: { UserId: item.Member.User.get('id') },
                });
                if (deviceValue) {
                  const message = {
                    to: deviceValue.deviceToken,
                    data: {
                      show_in_foreground: true,
                      priority: 'high',
                    },
                    notification: {
                      title: `Crane Pick ID - ${history.CraneRequestId}`,
                      body: history.description,
                    },
                  };

                  message.notification.sound = 'default';
                  message.data.sound = 'default';
                  // add extra fields
                  message.data.type = history.requestType;
                  message.data.projectId = ProjectId;
                  message.data.CraneRequestId = history.CraneRequestId;
                  fcm
                    .send(message)
                    .then((response) => {
                      console.log('Successfully sent with response: ', response);
                    })
                    .catch(async (err) => {
                      console.error(err);
                      if (err === 'NotRegistered') {
                        await DeviceToken.findOne({
                          where: { deviceToken: deviceValue.deviceToken },
                        });
                      }
                    });
                }
              }
            }
          }
        });
      }
    } catch (error) {
      console.log('error', error);
    }
  },
  async checkMemberNotificationPreference(ProjectId, MemberId, NotificationPreferenceItemId) {
    const checkMemberNotification = await NotificationPreference.findOne({
      where: {
        MemberId,
        isDeleted: false,
        ProjectId,
      },
      attributes: [
        'id',
        'MemberId',
        'ProjectId',
        'ParentCompanyId',
        'NotificationPreferenceItemId',
        'instant',
        'dailyDigest',
      ],
      include: [
        {
          association: 'NotificationPreferenceItem',
          where: {
            id: +NotificationPreferenceItemId,
            isDeleted: false,
          },
          attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
        },
      ],
    });
    return checkMemberNotification;
  },
  async sendPushNotificationForConcrete(history, NotificationPreferenceItemId, ProjectId) {
    try {
      const { adminData } = history; // RoleId - 1(Account admin),2(Project Admin)
      const { memberData } = history;
      const existAdminData = [];
      if (adminData) {
        adminData.map(async (item) => {
          const getMemberNotificationPeference = await this.checkMemberNotificationPreference(
            +ProjectId,
            +item.id,
            +NotificationPreferenceItemId,
          );
          if (
            getMemberNotificationPeference &&
            getMemberNotificationPeference.NotificationPreferenceItem &&
            getMemberNotificationPeference.NotificationPreferenceItem.inappNotification
          ) {
            if (getMemberNotificationPeference && getMemberNotificationPeference.instant) {
              const index = existAdminData.findIndex(
                (adminNew) => adminNew.userId === +item.User.id,
              );
              if (index === -1) {
                existAdminData.push({ userId: +item.User.id });
                const deviceNewValue = await DeviceToken.findOne({
                  where: { UserId: item.User.id },
                });
                if (deviceNewValue) {
                  const message = {
                    to: deviceNewValue.deviceToken,
                    data: {
                      show_in_foreground: true,
                      priority: 'high',
                    },
                    notification: {
                      title: `Concrete Booking ID - ${history.ConcreteRequestId}`,
                      body: history.description,
                    },
                  };

                  message.notification.sound = 'default';
                  message.data.sound = 'default';
                  // add extra fields
                  message.data.type = history.requestType;
                  message.data.projectId = ProjectId;
                  message.data.ConcreteRequestId = history.ConcreteRequestId;
                  fcm
                    .send(message)
                    .then((response) => {
                      console.log('Successfully sent with response: ', response);
                    })
                    .catch(async (err) => {
                      console.error(err);
                      if (err === 'NotRegistered') {
                        await DeviceToken.findOne({
                          where: { deviceToken: deviceNewValue.deviceToken },
                        });
                      }
                    });
                }
              }
            }
          }
        });
      }
      if (memberData) {
        memberData.map(async (item) => {
          const getMemberNotificationPeference = await this.checkMemberNotificationPreference(
            +ProjectId,
            +item.Member.id,
            +NotificationPreferenceItemId,
          );
          if (
            getMemberNotificationPeference &&
            getMemberNotificationPeference.NotificationPreferenceItem &&
            getMemberNotificationPeference.NotificationPreferenceItem.inappNotification
          ) {
            if (getMemberNotificationPeference && getMemberNotificationPeference.instant) {
              const index = existAdminData.findIndex(
                (adminNew) => adminNew.userId === +item.Member.User.get('id'),
              );
              if (index === -1) {
                existAdminData.push({ userId: item.Member.User.get('id') });
                const deviceValue = await DeviceToken.findOne({
                  where: { UserId: item.Member.User.get('id') },
                });
                if (deviceValue) {
                  const message = {
                    to: deviceValue.deviceToken,
                    data: {
                      show_in_foreground: true,
                      priority: 'high',
                    },
                    notification: {
                      title: `Concrete Booking ID- ${history.ConcreteRequestId}`,
                      body: history.description,
                    },
                  };

                  message.notification.sound = 'default';
                  message.data.sound = 'default';
                  // add extra fields
                  message.data.type = history.requestType;
                  message.data.projectId = ProjectId;
                  message.data.ConcreteRequestId = history.ConcreteRequestId;
                  fcm
                    .send(message)
                    .then((response) => {
                      console.log('Successfully sent with response: ', response);
                    })
                    .catch(async (err) => {
                      console.error(err);
                      if (err === 'NotRegistered') {
                        await DeviceToken.findOne({
                          where: { deviceToken: deviceValue.deviceToken },
                        });
                      }
                    });
                }
              }
            }
          }
        });
      }
    } catch (error) {
      console.log('error', error);
    }
  },
  async sendMemberLocationPreferencePushNotification(
    dataArray,
    deliveryId,
    text,
    requestType,
    ProjectId,
    DeliveryRequestId,
    NotificationPreferenceItemId,
  ) {
    try {
      for (let index = 0; index < dataArray.length; index += 1) {
        const data = dataArray[index];
        if (data.follow) {
          const getMemberNotificationPeference = await this.checkMemberNotificationPreference(
            +ProjectId,
            +data.id,
            +NotificationPreferenceItemId,
          );
          if (
            getMemberNotificationPeference &&
            getMemberNotificationPeference.NotificationPreferenceItem &&
            getMemberNotificationPeference.NotificationPreferenceItem.inappNotification
          ) {
            if (getMemberNotificationPeference && getMemberNotificationPeference.instant) {
              const deviceNewValue = await DeviceToken.findOne({
                where: { UserId: data.Member.User.id },
              });
              if (deviceNewValue) {
                const message = {
                  to: deviceNewValue.deviceToken,
                  data: {
                    show_in_foreground: true,
                    priority: 'high',
                  },
                  notification: {
                    title: `Delivery ID - ${deliveryId}`,
                    body: text,
                  },
                };

                message.notification.sound = 'default';
                message.data.sound = 'default';
                // add extra fields
                message.data.type = requestType;
                message.data.projectId = ProjectId;
                message.data.id = DeliveryRequestId;
                message.data.DeliveryId = deliveryId;
                fcm
                  .send(message)
                  .then((response) => {
                    console.log('Successfully sent with response: ', response);
                  })
                  .catch(async (err) => {
                    console.error(err);
                    if (err === 'NotRegistered') {
                      await DeviceToken.findOne({
                        where: { deviceToken: deviceNewValue.deviceToken },
                      });
                    }
                  });
              }
            }
          }
        }
      }
    } catch (error) {
      console.log('error', error);
    }
  },
  async sendMemberLocationPreferencePushNotificationForCrane(
    dataArray,
    CraneRequestId,
    text,
    requestType,
    ProjectId,
    RequestId,
    NotificationPreferenceItemId,
  ) {
    try {
      for (let index = 0; index < dataArray.length; index += 1) {
        const data = dataArray[index];
        if (data.follow) {
          const getMemberNotificationPeference = await this.checkMemberNotificationPreference(
            +ProjectId,
            +data.id,
            +NotificationPreferenceItemId,
          );
          if (
            getMemberNotificationPeference &&
            getMemberNotificationPeference.NotificationPreferenceItem &&
            getMemberNotificationPeference.NotificationPreferenceItem.inappNotification
          ) {
            if (getMemberNotificationPeference && getMemberNotificationPeference.instant) {
              const deviceNewValue = await DeviceToken.findOne({
                where: { UserId: data.Member.User.id },
              });
              if (deviceNewValue) {
                const message = {
                  to: deviceNewValue.deviceToken,
                  data: {
                    show_in_foreground: true,
                    priority: 'high',
                  },
                  notification: {
                    title: `Crane Pick ID - ${CraneRequestId}`,
                    body: text,
                  },
                };

                message.notification.sound = 'default';
                message.data.sound = 'default';
                // add extra fields
                message.data.type = requestType;
                message.data.projectId = ProjectId;
                message.data.id = RequestId;
                message.data.DeliveryId = CraneRequestId;
                fcm
                  .send(message)
                  .then((response) => {
                    console.log('Successfully sent with response: ', response);
                  })
                  .catch(async (err) => {
                    console.error(err);
                    if (err === 'NotRegistered') {
                      await DeviceToken.findOne({
                        where: { deviceToken: deviceNewValue.deviceToken },
                      });
                    }
                  });
              }
            }
          }
        }
      }
    } catch (error) {
      console.log('error', error);
    }
  },
  async sendMemberLocationPreferencePushNotificationForConcrete(
    dataArray,
    ConcreteRequestId,
    text,
    requestType,
    ProjectId,
    RequestId,
    NotificationPreferenceItemId,
  ) {
    try {
      for (let index = 0; index < dataArray.length; index += 1) {
        const data = dataArray[index];
        if (data.follow) {
          const getMemberNotificationPeference = await this.checkMemberNotificationPreference(
            +ProjectId,
            +data.id,
            +NotificationPreferenceItemId,
          );
          if (
            getMemberNotificationPeference &&
            getMemberNotificationPeference.NotificationPreferenceItem &&
            getMemberNotificationPeference.NotificationPreferenceItem.inappNotification
          ) {
            if (getMemberNotificationPeference && getMemberNotificationPeference.instant) {
              const deviceNewValue = await DeviceToken.findOne({
                where: { UserId: data.Member.User.id },
              });
              if (deviceNewValue) {
                const message = {
                  to: deviceNewValue.deviceToken,
                  data: {
                    show_in_foreground: true,
                    priority: 'high',
                  },
                  notification: {
                    title: `Concrete Booking ID - ${ConcreteRequestId}`,
                    body: text,
                  },
                };

                message.notification.sound = 'default';
                message.data.sound = 'default';
                // add extra fields
                message.data.type = requestType;
                message.data.projectId = ProjectId;
                message.data.id = RequestId;
                message.data.ConcreteRequestId = ConcreteRequestId;
                fcm
                  .send(message)
                  .then((response) => {
                    console.log('Successfully sent with response: ', response);
                  })
                  .catch(async (err) => {
                    console.error(err);
                    if (err === 'NotRegistered') {
                      await DeviceToken.findOne({
                        where: { deviceToken: deviceNewValue.deviceToken },
                      });
                    }
                  });
              }
            }
          }
        }
      }
    } catch (error) {
      console.log('error', error);
    }
  },
};

module.exports = fcmNot;
