const status = require('http-status');
const { concreteRequestCommentService } = require('../services');

const concreteRequestCommentController = {
  async createConcreteRequestComment(req, res, next) {
    try {
      await concreteRequestCommentService.createConcreteRequestComment(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Concrete Booking Comment added successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getConcreteRequestComments1(req, res, next) {
    try {
      await concreteRequestCommentService.getConcreteRequestComments2(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Concrete Booking Comment Viewed Successfully.',
            data: response.commentList,
            concreteRequest: response.exist,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = concreteRequestCommentController;
