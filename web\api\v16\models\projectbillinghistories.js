const { Model } = require('sequelize');
const { Sequelize } = require('sequelize');

const { Op } = Sequelize;
module.exports = (sequelize, DataTypes) => {
  class ProjectBillingHistories extends Model {
    static associate(models) {
      ProjectBillingHistories.belongsTo(models.Project);
      ProjectBillingHistories.belongsTo(models.User, {
        as: 'userDetails',
        foreignKey: 'projectCreatedUserId',
      });
    }
  }
  ProjectBillingHistories.init(
    {
      ProjectId: DataTypes.INTEGER,
      paidUserId: DataTypes.INTEGER,
      projectCreatedUserId: DataTypes.INTEGER,
      paidDate: DataTypes.DATE,
      receiptUrl: DataTypes.STRING,
      invoiceUrl: DataTypes.STRING,
      paymentMethod: DataTypes.STRING,
      amountPaid: DataTypes.STRING,
      status: DataTypes.STRING,
    },
    {
      sequelize,
      modelName: 'ProjectBillingHistories',
    },
  );
  ProjectBillingHistories.createInstance = async (data) => {
    const newProjectBilling = await ProjectBillingHistories.create(data);
    return newProjectBilling;
  };

  ProjectBillingHistories.getProjectsWithBillingHistories = async (
    projectId,
    recepientName,
    billingStatus,
    paymentMethod,
    amount,
    search,
  ) => {
    let commonSearch = {
      ProjectId: projectId,
    };
    if (search) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              sequelize.where(
                sequelize.cast(sequelize.col('ProjectBillingHistories.ProjectId'), 'varchar'),
                {
                  [Op.iLike]: `%${search}%`,
                },
              ),
              sequelize.where(
                sequelize.cast(sequelize.col('ProjectBillingHistories.paidUserId'), 'varchar'),
                {
                  [Op.iLike]: `%${search}%`,
                },
              ),
              sequelize.where(
                sequelize.cast(
                  sequelize.col('ProjectBillingHistories.projectCreatedUserId'),
                  'varchar',
                ),
                {
                  [Op.iLike]: `%${search}%`,
                },
              ),
              { paymentMethod: { [Sequelize.Op.iLike]: `%${search}%` } },
              sequelize.where(
                sequelize.cast(sequelize.col('ProjectBillingHistories.paidDate'), 'varchar'),
                {
                  [Op.iLike]: `%${search}%`,
                },
              ),
              { '$userDetails.firstName$': { [Sequelize.Op.iLike]: `%${search}%` } },
              { '$userDetails.lastName$': { [Sequelize.Op.iLike]: `%${search}%` } },
              { status: { [Sequelize.Op.iLike]: `%${search}%` } },
              { amountPaid: { [Sequelize.Op.iLike]: `%${search}%` } },
            ],
          },
        ],
      };
    }
    if (recepientName) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              { '$userDetails.firstName$': { [Sequelize.Op.iLike]: `%${recepientName}%` } },
              { '$userDetails.lastName$': { [Sequelize.Op.iLike]: `%${recepientName}%` } },
            ],
          },
        ],
      };
    }
    if (billingStatus) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ status: { [Sequelize.Op.iLike]: `%${billingStatus}%` } }],
          },
        ],
      };
    }
    if (paymentMethod) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ paymentMethod: { [Sequelize.Op.iLike]: `%${paymentMethod}%` } }],
          },
        ],
      };
    }
    if (amount) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ amountPaid: { [Sequelize.Op.iLike]: `%${amount}%` } }],
          },
        ],
      };
    }
    const project = await ProjectBillingHistories.findAll({
      where: commonSearch,
      attributes: [
        'id',
        'ProjectId',
        'paidUserId',
        'projectCreatedUserId',
        'paidDate',
        'receiptUrl',
        'invoiceUrl',
        'paymentMethod',
        'amountPaid',
        'status',
      ],
      include: [
        {
          required: true,
          duplicating: false,
          association: 'userDetails',
          attributes: ['id', 'firstName', 'lastName'],
        },
      ],
    });
    return project;
  };
  return ProjectBillingHistories;
};
