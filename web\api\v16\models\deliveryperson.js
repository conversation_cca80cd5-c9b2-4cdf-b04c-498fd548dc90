module.exports = (sequelize, DataTypes) => {
  const DeliveryPerson = sequelize.define(
    'DeliveryPerson',
    {
      DeliveryId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      DeliveryCode: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      MemberId: DataTypes.INTEGER,
      ProjectId: DataTypes.INTEGER,
      isActive: DataTypes.BOOLEAN,
    },
    {},
  );
  DeliveryPerson.associate = (models) => {
    // associations can be defined here
    DeliveryPerson.belongsTo(models.DeliveryRequest, {
      as: 'deliveryrequest',
      foreignKey: 'DeliveryId',
    });
    DeliveryPerson.belongsTo(models.Member, {
      as: 'Member',
      foreignKey: 'MemberId',
    });
    DeliveryPerson.belongsTo(models.Member);
  };
  DeliveryPerson.createInstance = async (paramData) => {
    const newDeliveryPerson = await DeliveryPerson.create(paramData);
    return newDelivery<PERSON>erson;
  };
  return DeliveryPerson;
};
