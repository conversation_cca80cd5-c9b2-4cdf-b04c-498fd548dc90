const faker = require('faker');
const Helper = require('../../helpers/helper');

let helper = new Helper();

const MEMBER_ENDPOINT = '/api/v3/member';
const PROJECT_ENDPOINT = '/api/v3/project';

beforeAll((done) => {
  helper.server(done);
  // avoid jest open handle error
});
afterAll((done) => {
  helper.close(done);
  // avoid jest open handle error
});

/**
 * @test {authRoutes.js}
 */
describe(`${MEMBER_ENDPOINT}`, () => {
  beforeAll(async () => {
    const res = await helper.apiServer.post(`/api/v3/auth/login`).send({
      email: `pa@${helper.baseEmail}`,
      password: 'Test@123',
    });
    helper = new Helper(res.body.token);
  });
  const MEMBER_END_POINT = `${MEMBER_ENDPOINT}/add_member`;
  const EDIT_END_POINT = `${MEMBER_ENDPOINT}/edit_member`;
  const DELETE_END_POINT = `${MEMBER_ENDPOINT}/delete_member`;
  const MEMEBER_LIST_END_POINT = `${MEMBER_ENDPOINT}/list_member`;
  const SEARCH_DELIVERYMEMBER_END_POINT = `${MEMBER_ENDPOINT}/search_member`;
  const ALL_MEMBER_DELIVERY_POINT = `${MEMBER_ENDPOINT}/list_all_member`;

  const PROJECT_END_POINT = `${PROJECT_ENDPOINT}/get_project`;
  const PROFILE_END_POINT = `${MEMBER_ENDPOINT}/update_profile`;
  const OVERVIEW_END_POINT = `${MEMBER_ENDPOINT}/get_overview_detail`;

  const newData = {
    firstName: 'somadoss',
    lastName: 'santhosam',
    phoneNumber: '(*************',
    phoneCode: '91',
    address: 'madurai',
    secondAddress: 'test',
    website: 'https://optisol.com',
    ProjectId: 3,
    companyName: 'new',
    CompanyId: 1,
  };
  describe(`POST ${PROJECT_END_POINT}`, () => {
    describe('Get all projects', () => {
      it('It should return the projects', async () => {
        const res = await helper.apiServer.get(PROJECT_END_POINT).send();
        const { status, body } = res;
        expect(status).toEqual(200);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Project List.');
      });
    });
  });
  describe(`POST ${OVERVIEW_END_POINT}`, () => {
    describe('Get Overview List', () => {
      it('It Get Overview List', async () => {
        const res = await helper.apiServer.get(`${OVERVIEW_END_POINT}/3`).send();
        const { status, body } = res;
        expect(status).toEqual(200);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Member listed Successfully.');
      });
    });
  });
  describe(`POST ${ALL_MEMBER_DELIVERY_POINT}`, () => {
    describe('List All Member for NEW Delivery List POPUP SCREEN', () => {
      it('It List Member for NEW Delivery List POPUP  SCREEN', async () => {
        const res = await helper.apiServer.get(`${ALL_MEMBER_DELIVERY_POINT}/5`).send();
        const { status, body } = res;
        expect(status).toEqual(200);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Member listed Successfully.');
      });
    });
  });
  describe(`POST ${MEMEBER_LIST_END_POINT}`, () => {
    describe('List Member for MEMBER SCREEN', () => {
      it('It List Member for MEMBER SCREEN', async () => {
        const res = await helper.apiServer.post(`${MEMEBER_LIST_END_POINT}/5/1/10`).send();
        const { status, body } = res;
        expect(status).toEqual(200);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Member listed Successfully.');
      });
    });
  });
  describe(`POST ${SEARCH_DELIVERYMEMBER_END_POINT}`, () => {
    describe('List search Member for NEW Delivery List POPUP SCREEN', () => {
      it('It List search Member for NEW Delivery List POPUP SCREEN', async () => {
        const res = await helper.apiServer.get(`${SEARCH_DELIVERYMEMBER_END_POINT}/5/t`).send();
        const { status } = res;
        expect(status).toEqual(200);
      });
    });
  });
  describe(`POST ${EDIT_END_POINT}`, () => {
    const editMemberData = {
      firstName: 'Theeran',
      phoneNumber: faker.phone.phoneNumber(),
      phoneCode: '+91',
      ProjectId: 3,
      email: `somu@${helper.baseEmail}`,
      RoleId: 2,
      id: 5,
      CompanyId: 1,
    };
    describe('Update Member', () => {
      it('It Update Member', async () => {
        const res = await helper.apiServer.post(`${EDIT_END_POINT}`).send(editMemberData);
        const { status, body } = res;
        expect(status).toEqual(200);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Member Updated Successfully.');
      });
    });
  });
  describe(`POST ${DELETE_END_POINT}`, () => {
    const deleteMemberData = {
      id: [24],
      ProjectId: 5,
      isSelectAll: false,
    };
    describe('Delete Member', () => {
      it('It Should Delete Member', async () => {
        const res = await helper.apiServer.post(`${DELETE_END_POINT}`).send(deleteMemberData);
        const { status, body } = res;
        expect(status).toEqual(200);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Member Deleted Successfully.');
      });
    });
  });
  describe(`POST ${PROFILE_END_POINT}`, () => {
    describe('It Profile data', () => {
      it('It should return Phone number already exist in Profile data', async () => {
        const res = await helper.apiServer.post(PROFILE_END_POINT).send(newData);
        const { status, body } = res;
        expect(status).toEqual(500);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Mobile Number already exist.');
      });
      it('It should update the Profile data', async () => {
        const updateData = newData;
        updateData.phoneNumber = '8384834';
        updateData.phoneCode = '1';
        const res = await helper.apiServer.post(PROFILE_END_POINT).send(newData);
        const { status, body } = res;
        expect(status).toEqual(200);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Profile Updated Successfully.');
      });
      it('It should return company name or website already exist in the exist data', async () => {
        const updateData = newData;
        updateData.phoneNumber = '8384834';
        updateData.phoneCode = '1';
        updateData.companyName = 'mahindra';
        updateData.website = 'https://mahindra.com';
        const res = await helper.apiServer.post(PROFILE_END_POINT).send(newData);
        const { status, body } = res;
        expect(status).toEqual(500);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Company Name/Website already exist.');
      });
    });
  });

  describe(`POST ${MEMBER_END_POINT}`, () => {
    const email = faker.internet.email();
    const emailVal = email.split('@')[0];
    const memberData = {
      firstName: 'Theeran',
      email: `${emailVal}@${helper.baseEmail}`,
      phoneNumber: faker.phone.phoneNumber(),
      phoneCode: '+91',
      ProjectId: 3,
      RoleId: 2,
      CompanyId: 1,
    };
    describe('Create the member as Project Admin with 201', () => {
      it('should Create the member as Project Admin', async () => {
        const res = await helper.apiServer.post(MEMBER_END_POINT).send(memberData);
        const { status, body } = res;
        expect(status).toEqual(201);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Member Created Successfully.');
      });
    });
    describe('Create the member as SubContractor with 201', () => {
      const subEmail = faker.internet.email();
      const subEmailVal = subEmail.split('@')[0];
      const newSubData = {
        firstName: 'Theeran',
        email: `${subEmailVal}@${helper.baseEmail}`,
        phoneNumber: faker.phone.phoneNumber(),
        phoneCode: '+91',
        ProjectId: 3,
        RoleId: 4,
        CompanyId: 1,
      };
      it('should create the member as SubContractor', async () => {
        const res = await helper.apiServer.post(MEMBER_END_POINT).send(newSubData);
        const { status, body } = res;
        expect(status).toEqual(201);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Member Created Successfully.');
      });
    });
    describe('Create the member as SubContractor with different email 201', () => {
      const subEmail = faker.internet.email();
      const subEmailVal = subEmail.split('@')[0];
      const newSubData = {
        firstName: 'Theeran',
        email: `${subEmailVal}@${helper.baseEmail}`,
        phoneNumber: faker.phone.phoneNumber(),
        phoneCode: '+91',
        ProjectId: 3,
        RoleId: 4,
        CompanyId: 1,
      };
      it('It should Create the member as SubContractor with different email 201', async () => {
        const res = await helper.apiServer.post(MEMBER_END_POINT).send(newSubData);
        const { status, body } = res;
        expect(status).toEqual(201);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Member Created Successfully.');
      });
    });
    describe('Should return Role does not exist', () => {
      it('It Should return Role does not exist', async () => {
        memberData.email = `somu@${helper.baseEmail}`;
        const testData = memberData;
        testData.RoleId = -1;
        const res = await helper.apiServer.post(MEMBER_END_POINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(500);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Role does not exist.');
      });
    });
    describe('Should return Member Already exist ', () => {
      it('It Should return Member Already exist ', async () => {
        memberData.email = `somu@${helper.baseEmail}`;
        const testData = memberData;
        testData.RoleId = 2;
        const res = await helper.apiServer.post(MEMBER_END_POINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(500);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Member Already exist in this project.');
      });
    });
    describe('Restrict mail ', () => {
      it('It Restrict mail ', async () => {
        memberData.email = `somu@${helper.baseEmail}`;
        const testData = memberData;
        testData.email = '<EMAIL>';
        testData.RoleId = 2;
        const res = await helper.apiServer.post(MEMBER_END_POINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(500);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('This email is not allowed');
      });
    });
    describe('Change Role id error', () => {
      it('It Change Role id error', async () => {
        memberData.email = `somu@${helper.baseEmail}`;
        const testData = memberData;
        testData.email = '<EMAIL>';
        testData.RoleId = 4;
        const res = await helper.apiServer.post(MEMBER_END_POINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(500);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual(
          'This member is Project Admin/General Contractor in our organization , it is not possible to assign member as a Sub Contractor.',
        );
      });
    });
    describe('Change Role id subcontractor error', () => {
      it('It Change Role id subcontractor error', async () => {
        memberData.email = `somu@${helper.baseEmail}`;
        const testData = memberData;
        testData.email = '<EMAIL>';
        testData.RoleId = 2;
        const res = await helper.apiServer.post(MEMBER_END_POINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(500);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('This member is a sub contractor in our organization.');
      });
    });
    describe('Should return Forbidden error when project id not exist or Role is not a Project Admin', () => {
      it('It Should return project id not exist or Role is not a Project Admin', async () => {
        memberData.email = `somu@${helper.baseEmail}`;
        const testData = memberData;
        testData.ProjectId = -1;
        const res = await helper.apiServer.post(MEMBER_END_POINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(403);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Access Forbidden');
      });
    });
    describe('Should return company id not exist', () => {
      it('It Should return company id not exist', async () => {
        memberData.email = `somu@${helper.baseEmail}`;
        const testData = memberData;
        testData.CompanyId = -1;
        testData.ProjectId = 3;
        const res = await helper.apiServer.post(MEMBER_END_POINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(500);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Company does not exist.');
      });
    });
    describe('should return an phoneCode valid error', () => {
      it('Check phoneCode empty', async () => {
        const testData = memberData;
        testData.phoneCode = '';
        const res = await helper.apiServer.post(MEMBER_END_POINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].phoneCode).toEqual('"phoneCode" is not allowed to be empty');
      });
    });
    describe('should return an firstName error', () => {
      it('Check firstName invalid', async () => {
        const testData = memberData;
        testData.firstName = '';
        const res = await helper.apiServer.post(MEMBER_END_POINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].firstName).toEqual('"firstName" is not allowed to be empty');
      });
    });
    describe('should return an email error', () => {
      it('Check email invalid', async () => {
        const testData = memberData;
        testData.email = '';
        const res = await helper.apiServer.post(MEMBER_END_POINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].email).toEqual('"email" is not allowed to be empty');
      });
    });
    describe('should return an email valid error', () => {
      it('Check email invalid', async () => {
        const testData = memberData;
        testData.email = 'test@';
        const res = await helper.apiServer.post(MEMBER_END_POINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].email).toEqual('"email" must be a valid email');
      });
    });
    describe('should return an phoneNumber valid error', () => {
      it('Check phoneNumber empty', async () => {
        const testData = memberData;
        testData.phoneNumber = '';
        const res = await helper.apiServer.post(MEMBER_END_POINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].phoneNumber).toEqual(
          '"phoneNumber" is not allowed to be empty',
        );
      });
    });
    describe('should return an projectid valid error', () => {
      it('Check projectid empty', async () => {
        const testData = memberData;
        testData.ProjectId = '';
        const res = await helper.apiServer.post(MEMBER_END_POINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message.details[0].ProjectId).toEqual('"ProjectId" must be a number');
      });
    });
  });
});
