const { Enterprise, Sequelize } = require('../models');
let { DeliverHistory, DeliveryRequest, User } = require('../models');
const helper = require('../helpers/domainHelper');

const { Op } = Sequelize;
let publicUser;
let publicMember;
const commentService = {
  async getHistory(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const exist = await DeliveryRequest.findOne({
        where: { id: inputData.params.DeliveryRequestId },
      });
      if (exist) {
        const historyList = await DeliverHistory.findAll({
          include: [
            {
              association: 'Member',
              include: [
                { association: 'User', attributes: ['firstName', 'lastName', 'profilePic'] },
              ],
            },
          ],
          where: {
            DeliveryRequestId: inputData.params.DeliveryRequestId,
          },
          order: [['id', 'DESC']],
        });
        done(historyList, false);
      } else {
        done(null, { message: 'Delivery Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    // publicProject = modelData.Project;
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    let enterpriseValue;
    const incomeData = inputData;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    DeliverHistory = modelObj.DeliverHistory;
    DeliveryRequest = modelObj.DeliveryRequest;
    User = modelObj.User;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return true;
  },
};

module.exports = commentService;
