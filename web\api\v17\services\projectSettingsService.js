const { ProjectSettings, Member } = require('../models');

const ProjectSettingsService = {
  async getProjectSettings(ProjectId) {
    const projectSettings = await ProjectSettings.findOne({
      where: {
        ProjectId,
      },
      attributes: [
        'id',
        'deliveryWindowTime',
        'deliveryWindowTimeUnit',
        'ProjectId',
        'deliveryAllowOverlappingBooking',
        'deliveryAllowOverlappingCalenderEvents',
        'craneAllowOverlappingBooking',
        'craneAllowOverlappingCalenderEvents',
        'concreteAllowOverlappingBooking',
        'concreteAllowOverlappingCalenderEvents',
        'isAutoApprovalEnabled',
        'statusColorCode',
        'deliveryCard',
        'craneCard',
        'concreteCard',
        'isPublicWebsiteEnabled',
        'shareProjectInformation',
        'projectLogisticPlanUrl',
        'allowGuestToAddDeliveryBooking',
        'allowGuestToAddCraneBooking',
        'allowGuestToAddConcreteBooking',
        'allowGuestToViewDeliveryCalendar',
        'allowGuestToViewCraneCalendar',
        'allowGuestToViewConcreteCalendar',
        'autoRefreshRateInMinutes',
        'publicWebsiteUrl',
        'shareprojectLogisticPlan',
        'pdfOriginalName',
        'isPdfUploaded',
        'isDefaultColor',
        'useTextColorAsLegend',
      ],
      raw: true,
    });
    const autoApproveMembers = await Member.findAll({
      where: {
        ProjectId,
        isAutoApproveEnabled: true,
      },
      attributes: ['id', 'firstName', 'isAutoApproveEnabled', 'memberId'],
      include: [
        {
          association: 'User',
          attributes: ['email', 'isAccount', 'firstName', 'lastName', 'versionFlag'],
        },
      ],
    });
    if(projectSettings.isPdfUploaded){
      const fileName = projectSettings.pdfOriginalName;
      const array = fileName.split('.');
      const extension= array[array.length-1];
      projectSettings.fileExtension = extension;
    }else{
      projectSettings.fileExtension = null;
    }
    return { projectSettings, autoApproveMembers };
  },
  async updateProjectSettings(payload) { 
    try {
      const getProjectSettingsData = await ProjectSettings.findOne({
        where: {
          ProjectId: payload.ProjectId,
        },
      });
      if (payload.isDefaultColor) {
        getProjectSettingsData.statusColorCode = getProjectSettingsData.defaultStatusColor;
        getProjectSettingsData.isDefaultColor = payload.isDefaultColor;
        await getProjectSettingsData.save();
      }
      if (payload.setDefaultCard) {
        getProjectSettingsData.deliveryCard = getProjectSettingsData.defaultDeliveryCard;
        getProjectSettingsData.craneCard = getProjectSettingsData.defaultCraneCard;
        getProjectSettingsData.concreteCard = getProjectSettingsData.defaultConcreteCard;
        await getProjectSettingsData.save();
      }
      const projectSettings = await ProjectSettings.update(payload, {
        where: {
          ProjectId: payload.ProjectId,
        },
      });
      if (payload.enabledUser && payload.enabledUser.length > 0) {
        await Member.update(
          { isAutoApproveEnabled: true },
          {
            where: {
              id: payload.enabledUser,
            },
          },
        );
      }
      if (payload.disabledUser && payload.disabledUser.length > 0) {
        await Member.update(
          { isAutoApproveEnabled: false },
          {
            where: {
              id: payload.disabledUser,
            },
          },
        );
      }
      return projectSettings;
    } catch (err) {
      return err;
    }
  },
};

module.exports = ProjectSettingsService;
