const status = require('http-status');
const { craneRequestAttachmentService } = require('../services');

const craneRequestAttachmentController = {
  async createCraneRequestAttachement(req, res, next) {
    try {
      await craneRequestAttachmentService.createCraneRequestAttachement(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Uploaded Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getCraneRequestAttachements(req, res, next) {
    try {
      await craneRequestAttachmentService.getCraneRequestAttachements(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Crane Booking Attachment Viewed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async deleteCraneRequestAttachement(req, res, next) {
    try {
      await craneRequestAttachmentService.deleteCraneRequestAttachement(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Crane Booking Attachment Deleted Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = craneRequestAttachmentController;
