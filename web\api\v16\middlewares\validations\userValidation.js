const Joi = require('joi');

const userValidation = {
  login: {
    body: Joi.object({
      email: Joi.string().email().required(),
      password: Joi.string().required(),
    }),
  },
  existEmail: {
    body: Joi.object({
      email: Joi.string().email().required(),
      phoneNumber: Joi.string().required(),
      phoneCode: Joi.string().required(),
    }),
  },
  email: {
    body: Joi.object({
      email: Joi.string().email().required(),
    }),
  },
  register: {
    body: Joi.object({
      basicDetails: Joi.object({
        email: Joi.string().email().required(),
        phoneNumber: Joi.string().required(),
        phoneCode: Joi.string(),
        password: Joi.any().optional(),
        confirmPassword: Joi.any().optional(),
      }).required(),
      companyDetails: Joi.object({
        companyName: Joi.string().min(3).max(50).required(),
        fullName: Joi.string().min(3).max(30).required(),
        lastName: Joi.string().min(3).max(30).required(),
        scope: Joi.string().min(2),
        address: Joi.any().optional().allow('', null),
        secondAddress: Joi.any().optional().allow('', null),
        country: Joi.any().optional().allow('', null),
        city: Joi.any().optional().allow('', null),
        state: Joi.any().optional().allow('', null),
        zipCode: Joi.any().optional().allow('', null),
        website: Joi.string().allow('', null),
        isParent: Joi.boolean().required(),
        companyId: Joi.any().optional().allow('', null),
      }).required(),
      projectDetails: Joi.object({
        projectName: Joi.string().min(2).required(),
        projectLocation: Joi.string().min(2).required(),
        projectLocationLatitude: Joi.any().optional().allow('', null),
        projectLocationLongitude: Joi.any().optional().allow('', null),
      }).required(),
      planData: Joi.object({
        id: Joi.number().required(),
      }).required(),
      // cardDetails: Joi.object({
      //   number: Joi.number(),
      //   name: Joi.string(),
      //   interval: Joi.optional().allow(''),
      //   country: Joi.optional().allow(''),
      //   zipCode: Joi.optional().allow(''),
      //   exp_month: Joi.number(),
      //   exp_year: Joi.number(),
      //   cvc: Joi.number(),
      // }),
      stripeCustomerId: Joi.string().optional().allow('', null),
      timezone: Joi.string().required(),
    }),
  },
  checkResetToken: {
    params: Joi.object({
      resetToken: Joi.string().required(),
    }),
  },
  forgotPassword: {
    body: Joi.object({
      email: Joi.string().email().required(),
      requestType: Joi.number().required(),
    }),
  },
  changePassword: {
    body: Joi.object({
      oldPassword: Joi.string().required(),
      newPassword: Joi.string().required().label('password'),
      ParentCompanyId: Joi.any(),
    }),
  },
  resetPasswordOTP: {
    body: Joi.object({
      email: Joi.string().email().required(),
      password: Joi.string().required().min(8),
      otpCode: Joi.string().required(),
    }),
  },
  resetPasswordEmail: {
    params: Joi.object({
      reset_password_token: Joi.string().required(),
    }),
    body: Joi.object({
      password: Joi.string()
        .messages({
          'any.required': 'password is required',
        })
        .required(),
    }),
  },
  verifyByEmail: {
    params: Joi.object({
      registration_token: Joi.string().required(),
    }),
  },
};

module.exports = userValidation;
