module.exports = (sequelize, DataTypes) => {
  const City = sequelize.define(
    'City',
    {
      cityName: DataTypes.STRING,
      StateId: DataTypes.INTEGER,
    },
    {},
  );
  City.associate = (models) => {
    City.belongsTo(models.State);
    return City;
  };
  City.getAll = async (attr) => {
    const city = await City.findAll({
      include: ['State'],
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return city;
  };
  City.createInstance = async (paramData) => {
    const city = await City.create(paramData);
    return city;
  };
  return City;
};
