const faker = require('faker');
const uuid = require('uuid');

const Helper = require('../../helpers/helper');

let helper = new Helper();

const MEMBER_ENDPOINT = '/api/v3/member';
const GATE_ENDPOINT = '/api/v3/gates';

const email = faker.internet.email();
const emailVal = email.split('@')[0];

beforeAll((done) => {
  helper.server(done);
  // avoid jest open handle error
});
afterAll((done) => {
  helper.close(done);
  // avoid jest open handle error
});

/**
 * @test {authRoutes.js}
 */
describe(`${MEMBER_ENDPOINT}`, () => {
  beforeAll(async () => {
    const res = await helper.apiServer.post(`/api/v3/auth/login`).send({
      email: 'pa@yopmail',
      password: 'Test@123',
    });
    helper = new Helper(res.body.token);
  });
  const MEMBER_END_POINT = `${MEMBER_ENDPOINT}/add_member`;
  const GATE_END_POINT = `${GATE_ENDPOINT}/add_gates`;
  const UPDATE_GATE_END_POINT = `${GATE_ENDPOINT}/update_gates`;
  const DELETE_GATE_END_POINT = `${GATE_ENDPOINT}/delete_gates`;

  describe(`POST ${MEMBER_END_POINT}`, () => {
    const memberData = {
      firstName: 'Base',
      email: `${emailVal}@yopmail.com`,
      phoneNumber: faker.phone.phoneNumber(),
      phoneCode: '+1',
      ProjectId: 134,
      RoleId: 2,
      CompanyId: 134,
    };
    describe('Create the member as Project Admin with 201', () => {
      it('should Create the member as Project Admin', async () => {
        const res = await helper.apiServer.post(MEMBER_END_POINT).send(memberData);
        const { status, body } = res;
        expect(status).toEqual(201);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Member Created Successfully.');
      });
    });
  });
  describe(`POST ${UPDATE_GATE_END_POINT}`, () => {
    const updateGate = {
      gateName: 'first1',
      ProjectId: 134,
      id: 3,
    };
    describe('Update the gate with 200', () => {
      it('It should return 200 when update the gate', async () => {
        const res = await helper.apiServer.post(UPDATE_GATE_END_POINT).send(updateGate);
        const { status, body } = res;
        expect(status).toEqual(200);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Gate Updated successfully.');
      });
    });
    describe('Update the gate gate name already exist', () => {
      it('It should return 500 when update the gate', async () => {
        const testData = {
          gateName: 'first1',
          id: 1,
          ProjectId: 134,
        };
        const res = await helper.apiServer.post(UPDATE_GATE_END_POINT).send(testData);
        const { status, body } = res;
        expect(status).toEqual(400);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Gate Name Already exist.');
      });
    });
    describe('Update the gate Gate id does not exist. exist', () => {
      it('It should return 500 when update the gate Gate id does not exist.', async () => {
        const testData1 = {
          gateName: 'neww',
          id: -1,
          ProjectId: 134,
        };
        const res = await helper.apiServer.post(UPDATE_GATE_END_POINT).send(testData1);
        const { status, body } = res;
        expect(status).toEqual(500);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Gate id does not exist.');
      });
    });
  });
  describe(`POST ${DELETE_GATE_END_POINT}`, () => {
    const deleteGateData = {
      id: [1],
      ProjectId: 134,
      isSelectAll: false,
    };
    describe('Delete Gate', () => {
      it('It Should Delete Gate', async () => {
        const res = await helper.apiServer.post(`${DELETE_GATE_END_POINT}`).send(deleteGateData);
        const { status, body } = res;
        expect(status).toEqual(200);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Gate deleted successfully.');
      });
    });
    describe('Delete All Gate', () => {
      it('It Should All Delete Gate', async () => {
        deleteGateData.isSelectAll = true;
        const res = await helper.apiServer.post(`${DELETE_GATE_END_POINT}`).send(deleteGateData);
        const { status, body } = res;
        expect(status).toEqual(200);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Gate deleted successfully.');
      });
    });
  });
  describe(`POST ${GATE_END_POINT}`, () => {
    const gateData = {
      gateName: uuid.v1() + 1,
      ProjectId: 134,
    };
    describe('Create the gate with 201', () => {
      it('It should return 201 when add the gate', async () => {
        const res = await helper.apiServer.post(GATE_END_POINT).send(gateData);
        const { status, body } = res;
        expect(status).toEqual(201);
        expect(body).toHaveProperty('message');
        expect(body.message).toEqual('Gate added successfully.');
      });
    });
  });
});
