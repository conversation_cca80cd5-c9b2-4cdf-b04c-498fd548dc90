const status = require('http-status');
const { equipmentService } = require('../services');

const EquipmentController = {
  async addEquipment(req, res, next) {
    equipmentService.addEquipment(req, async (projectDetail, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.CREATED).json({
          message: 'Equipment added successfully.',
          data: projectDetail,
        });
      }
    });
  },
  async listEquipment(req, res, next) {
    equipmentService.listEquipment(req, async (equipmentDetail, error) => {
      if (error) {
        next(error);
      } else {
        equipmentService.lastEquipment(req, (lastDetail, error1) => {
          if (!error1) {
            res.status(status.OK).json({
              message: 'Equipment Listed successfully.',
              data: equipmentDetail,
              lastId: lastDetail,
            });
          } else {
            next(error1);
          }
        });
      }
    });
  },
  async craneListEquipment(req, res, next) {
    equipmentService.craneListEquipment(req, async (equipmentDetail, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.OK).json({
          message: 'Crane Equipment Listed successfully.',
          data: equipmentDetail,
        });
      }
    });
  },
  async listEquipmentType(req, res, next) {
    equipmentService.getAllEquipmentType(req, async (equipmentDetail, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.OK).json({
          message: 'Equipment Type Listed successfully.',
          data: equipmentDetail,
        });
      }
    });
  },
  async updateEquipment(req, res, next) {
    equipmentService.updateEquipment(req, async (projectDetail, error) => {
      if (error) {
        next(error);
      } else {
        let messageText = 'Equipment Updated successfully.';
        if (req.body.isActive) {
          messageText = 'Equipment Activated Successfully.';
        }
        res.status(status.OK).json({
          message: messageText,
          data: projectDetail,
        });
      }
    });
  },
  async deleteEquipment(req, res, next) {
    equipmentService.deleteEquipment(req, async (projectDetail, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.OK).json({
          message: 'Equipment deleted successfully.',
          data: projectDetail,
        });
      }
    });
  },
  async getPresetEquipmentTypeList(req, res, next) {
    equipmentService.getPresetEquipmentTypeList(req, async (equipmentTypeList, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.OK).json({
          message: 'Preset Equipment Type Listed successfully.',
          data: equipmentTypeList,
        });
      }
    });
  },
  async getMappedRequests(req, res, next) {
    try {
      await equipmentService.getMappedRequests(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Equipment Mapped Bookings Listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async deactivateEquipment(req, res, next) {
    try {
      await equipmentService.deactivateEquipment(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Equipment deactivated Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = EquipmentController;
