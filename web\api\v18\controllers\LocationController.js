const status = require('http-status');
const { locationService } = require('../services');
const exportService = require('../services/exportService');
const { Project } = require('../models');

const LocationController = {
  async addLocation(req, res) {
    const isLocationAdded = await locationService.addLocation(req);
    if (isLocationAdded) {
      res.status(status.OK).json({
        message: 'Location Added successfully.',
      });
    } else {
      res.status(status.UNPROCESSABLE_ENTITY).json({
        message: 'Cannot add location',
      });
    }
  },
  async listLocation(req, res) {
    const locations = await locationService.listLocation(req);
    if (locations) {
      res.status(status.OK).json({
        message: 'Location Listed successfully.',
        data: locations,
      });
    } else {
      res.status(status.UNPROCESSABLE_ENTITY).json({
        message: 'Cannot get locations',
        data: [],
      });
    }
  },
  async editLocation(req, res) {
    const isLocationUpdated = await locationService.editLocation(req);
    if (isLocationUpdated) {
      res.status(status.OK).json({
        message: 'Location Updated successfully.',
      });
    } else {
      res.status(status.UNPROCESSABLE_ENTITY).json({
        message: 'Cannot update location',
      });
    }
  },
  async deleteLocation(req, res) {
    const isLocationDeleted = await locationService.deleteLocation(req);
    if (isLocationDeleted) {
      res.status(status.OK).json({
        message: 'Location Deleted successfully.',
      });
    } else {
      res.status(status.UNPROCESSABLE_ENTITY).json({
        message: 'Cannot delete location',
      });
    }
  },
  async getLocation(req, res) {
    const location = await locationService.getLocation(req);
    if (location) {
      res.status(status.OK).json({
        message: 'Location Viewed successfully.',
        data: location,
      });
    } else {
      res.status(status.UNPROCESSABLE_ENTITY).json({
        message: 'Cannot view location',
        data: null,
      });
    }
  },
  async sampleExcelDownload(req, res, next) {
    try {
      const { ProjectId } = req.body;
      const Workbook = await exportService.exportSampleLocationDocument();
      const projectList = await Project.findOne({
        where: { id: ProjectId },
        attribute: ['projectName'],
      });
      const fileName = `${projectList.dataValues.projectName}_${projectList.dataValues.id
        }_${new Date().getTime()}`;
      if (Workbook) {
        res.setHeader('Content-Type', 'application/excel');
        res.setHeader('Content-Disposition', `attachment; filename=${fileName}.xlsx`);
        await Workbook.xlsx.write(res);
        res.end();
      } else {
        res.status(422).json({ message: 'cannot export document', status: 422 });
      }
    } catch (e) {
      next(e);
    }
  },
  async getLocations(req, res) {
    const locations = await locationService.getLocations(req);
    if (locations) {
      res.status(status.OK).json({
        message: 'Location Listed successfully.',
        data: locations,
      });
    } else {
      res.status(status.UNPROCESSABLE_ENTITY).json({
        message: 'Cannot get locations',
        data: [],
      });
    }
  },
  async importLocation(req, res, next) {
    try {
      const isLocationsAdded = await locationService.bulkUploadLocation(req);
      if (isLocationsAdded && !isLocationsAdded.error) {
        res.status(status.OK).json({
          message: 'Locations Added successfully.',
          isLocationsAdded,
        });
      } else {
        res.status(status.UNPROCESSABLE_ENTITY).json({
          message: isLocationsAdded.message,
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async listLocations(req, res) {
    const locations = await locationService.listLocations(req);
    if (locations) {
      res.status(status.OK).json({
        message: 'Location Listed successfully.',
        data: locations,
      });
    } else {
      res.status(status.UNPROCESSABLE_ENTITY).json({
        message: 'Cannot get locations',
        data: [],
      });
    }
  },
  async setLocationNotificationPreference(req, res) {
    const locations = await locationService.setLocationNotificationPreference();
    if (!locations.error) {
      res.status(status.OK).json({
        message: 'Location Notification Preference set successfully.',
        data: locations,
      });
    } else {
      res.status(status.UNPROCESSABLE_ENTITY).json({
        message: 'Cannot set location notification preference',
        data: [],
      });
    }
  },
  async updateMemberLocationPreference(req, res) {
    const locations = await locationService.updateMemberLocationPreference(req);
    if (!locations.error) {
      res.status(status.OK).json({
        message: 'Location Notification Preference set successfully.',
        data: locations,
      });
    } else {
      res.status(status.UNPROCESSABLE_ENTITY).json({
        message: 'Cannot set location notification preference',
        data: [],
      });
    }
  },
  async createDefaultLocationPathForExistingProject(req, res) {
    const locations = await locationService.createDefaultLocationPathForExistingProject(req);
    if (!locations.error) {
      res.status(status.OK).json({
        message: 'Default Location Path created successfully.',
      });
    } else {
      res.status(status.UNPROCESSABLE_ENTITY).json({
        message: 'Cannot create default location path',
      });
    }
  },
  async createDefaultLocationIDForExistingBookings(req, res) {
    const locations = await locationService.createDefaultLocationIDForExistingBookings(req);
    if (!locations.error) {
      res.status(status.OK).json({
        message: 'Default Location ID created successfully.',
      });
    } else {
      res.status(status.UNPROCESSABLE_ENTITY).json({
        message: 'Cannot create default location path',
      });
    }
  },
};
module.exports = LocationController;
