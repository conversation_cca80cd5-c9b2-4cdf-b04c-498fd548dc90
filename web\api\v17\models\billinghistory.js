const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class BillingHistory extends Model {
    static associate(models) {
      // define association here
      BillingHistory.belongsTo(models.Enterprise);
      BillingHistory.belongsTo(models.User);
    }
  }
  BillingHistory.init(
    {
      EnterpriseId: DataTypes.INTEGER,
      UserId: DataTypes.INTEGER,
      approvedBy: DataTypes.INTEGER,
      status: DataTypes.STRING,
      isDeleted: DataTypes.BOOLEAN,
      lastPayment: DataTypes.DATE,
      nextPayment: DataTypes.DATE,
      receiptUrl: DataTypes.STRING,
      paymentMethod: {
        type: DataTypes.ENUM,
        values: ['online', 'offline'],
      },
      amount: DataTypes.INTEGER,
      currency: DataTypes.STRING,
    },
    {
      sequelize,
      modelName: 'BillingHistory',
    },
  );
  return BillingHistory;
};
