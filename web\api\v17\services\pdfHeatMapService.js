const moment = require('moment');
const fs = require('fs');
const _ = require('lodash');
const awsConfig = require('../middlewares/awsConfig');
const puppeteerService = require('./puppeteerService');
const { Project, Company } = require('../models');

const pdfHeatMapService = {
  async pdfFormatOfDeliveryRequest(params, loginUser, data, req, done) {
    const mailContent = [];

    const projectData = await Project.findOne({
      where: {
        isDeleted: false,
        id: +params.ProjectId,
      },
      attributes: ['projectName'],
    });
    const companyData = await Company.findOne({
      where: {
        isDeleted: false,
        ParentCompanyId: +req.body.ParentCompanyId,
        isParent: true,
      },
      attributes: ['companyName'],
    });
    let pdftemplate = fs.readFileSync(
      '/usr/src/web/api/v17/views/mail-templates/report-heat-map.html',
      {
        encoding: 'utf-8',
      },
    );
    heatMapReportList = [];
    heatMapReportList = Object.entries(data).map(function ([date, { timeslots, totalCount }]) {
      return {
        date,
        timeslots: Object.values(timeslots),
        totalCount,
      };
    });
    const maxCount = _.max(_.flatMap(_.map(_.values(heatMapReportList), 'timeslots'), _.values));

    for (let index = 0; index < heatMapReportList.length; index += 1) {
      let content = `<tr style="font-size: 14px;border-top:5px solid #fff;border-bottom:5px solid #fff;">
                        <td style="padding:12px;"> ${heatMapReportList[index].date.replace(
        ',',
        '&comma;',
      )}</td>`;
      for (let i = 0; i < heatMapReportList[index].timeslots.length; i += 1) {
        const colorCode = heatMapReportList[index].timeslots[i] / maxCount;
        if (colorCode === 0) {
          content += `<td style="text-align: center;color:#000;background-color: rgb(227, 226, 227);">${heatMapReportList[index].timeslots[i]}</td>`;
        } else {
          content += `<td style="text-align: center;color:#000;background: rgb(244, 94, 40); opacity: ${colorCode};">${heatMapReportList[index].timeslots[i]}</td>`;
        }
      }

      content += `<td style="background-color: #F0F0F0;text-align: center;color:#000;">${heatMapReportList[index].totalCount}</td>
                      </tr>`;
      mailContent.push(content);
    }

    pdftemplate = pdftemplate
      .replace('$projectName', `${projectData.projectName} `)
      .replace('$companyName', `${companyData.companyName} `)
      .replace('$ReportName', req.body.reportName)
      .replace('$generatedDate', req.body.generatedDate)
      .replace('$generatedBy', `${loginUser.firstName} ${loginUser.lastName} `)
      .replace('$data', `${mailContent} `);
    pdftemplate = pdftemplate.replace(/,/g, '');

    const pdfBuffer = await puppeteerService.generatePdfBuffer(pdftemplate);
    if (pdfBuffer) {
      awsConfig.reportUpload(
        pdfBuffer,
        req.body.reportName,
        req.body.exportType,
        async (result, error1) => {
          if (!error1) {
            done(result, false);
          }
        },
      );
    } else {
      done(false, { message: 'cannot export the document' });
    }
  },
};
module.exports = pdfHeatMapService;
