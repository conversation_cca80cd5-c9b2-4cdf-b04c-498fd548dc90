module.exports = (sequelize, DataTypes) => {
  const CraneRequestComment = sequelize.define(
    'CraneRequestComment',
    {
      ProjectId: DataTypes.INTEGER,
      MemberId: DataTypes.INTEGER,
      CraneRequestId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      isDeleted: DataTypes.BOOLEAN,
      comment: DataTypes.STRING,
    },
    {},
  );
  CraneRequestComment.associate = (models) => {
    CraneRequestComment.belongsTo(models.Member);
    CraneRequestComment.belongsTo(models.CraneRequest)
  };

  CraneRequestComment.getAll = async (attr) => {
    const newCraneRequestComment = await CraneRequestComment.findAll({
      where: { ...attr },
    });
    return newCraneRequestComment;
  };
  CraneRequestComment.createInstance = async (paramData) => {
    const newCraneRequestComment = await CraneRequestComment.create(paramData);
    return newCraneRequestComment;
  };
  return CraneRequestComment;
};
