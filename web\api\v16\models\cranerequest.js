const moment = require('moment');
const { Sequelize } = require('sequelize');

const { Op } = Sequelize;
module.exports = (sequelize, DataTypes) => {
  const CraneRequest = sequelize.define(
    'CraneRequest',
    {
      description: DataTypes.STRING,
      isDeleted: DataTypes.BOOLEAN,
      CompanyId: DataTypes.INTEGER,
      isEscortNeeded: DataTypes.BOOLEAN,
      additionalNotes: DataTypes.STRING,
      CraneRequestId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      status: {
        type: DataTypes.STRING,
      }, // values: ['Pending', 'Approved', 'Declined', 'Completed', 'Expired'],
      approvedBy: DataTypes.INTEGER,
      craneDeliveryStart: DataTypes.DATE,
      approved_at: DataTypes.DATE,
      craneDeliveryEnd: DataTypes.DATE,
      ProjectId: DataTypes.INTEGER,
      pickUpLocation: DataTypes.STRING,
      dropOffLocation: DataTypes.STRING,
      createdBy: DataTypes.INTEGER,
      isAssociatedWithDeliveryRequest: DataTypes.BOOLEAN,
      requestType: DataTypes.STRING,
      recurrenceId: DataTypes.INTEGER,
      LocationId: DataTypes.INTEGER,
      // chosenDateOfMonth: {
      //   type: DataTypes.BOOLEAN,
      // },
      // dateOfMonth: {
      //   type: DataTypes.STRING,
      // },
      // monthlyRepeatType: {
      //   type: DataTypes.STRING,
      // },
      // recurrence: {
      //   type: DataTypes.ENUM,
      //   values: ['Does Not Repeat', 'Daily', 'Weekly', 'Monthly', 'Yearly', 'Custom'],
      // },
      // repeatEveryCount: DataTypes.STRING,
      // repeatEveryType: DataTypes.STRING,
      // days: DataTypes.ARRAY(DataTypes.STRING),
    },
    {},
  );
  CraneRequest.associate = (models) => {
    CraneRequest.hasMany(models.CraneRequestResponsiblePerson, {
      as: 'memberDetails',
      foreignKey: 'CraneRequestId',
    });
    CraneRequest.belongsTo(models.Member, {
      as: 'approverDetails',
      foreignKey: 'approvedBy',
    });
    CraneRequest.belongsTo(models.Project);
    CraneRequest.belongsTo(models.Member, {
      as: 'createdUserDetails',
      foreignKey: 'createdBy',
    });
    CraneRequest.hasMany(models.CraneRequestCompany, {
      as: 'companyDetails',
      foreignKey: 'CraneRequestId',
    });
    CraneRequest.hasMany(models.CraneRequestEquipment, {
      as: 'equipmentDetails',
      foreignKey: 'CraneRequestId',
    });
    CraneRequest.hasMany(models.CraneRequestDefinableFeatureOfWork, {
      as: 'defineWorkDetails',
      foreignKey: 'CraneRequestId',
    });
    CraneRequest.hasMany(models.VoidList, {
      as: 'voidList',
      foreignKey: 'CraneRequestId',
    });
    CraneRequest.belongsTo(models.RequestRecurrenceSeries, {
      as: 'recurrence',
      foreignKey: 'recurrenceId',
    });
    CraneRequest.belongsTo(models.Locations, {
      as: 'location',
      foreignKey: 'LocationId',
    });
  };
  CraneRequest.createInstance = async (paramData) => {
    const newCraneRequest = await CraneRequest.create(paramData);
    return newCraneRequest;
  };
  CraneRequest.getAll = async (
    req,
    roleId,
    memberId,
    attr,
    descriptionFilter,
    startdate,
    enddate,
    companyFilter,
    memberFilter,
    equipmentFilter,
    statusFilter,
    idFilter,
    pickFrom,
    pickTo,
    search,
    order,
    sort,
    sortColumn,
    dateFilter,
  ) => {
    let commonSearch = {
      ...attr,
      isDeleted: false,
    };
    const sortByFieldName = sortColumn || 'id';
    let sortByColumnType = sort || 'DESC';
    let locationFilter;
    if (req.body.locationFilter) {
      locationFilter = req.body.locationFilter;
    }
    if (order) {
      sortByColumnType = order;
    }
    let orderQuery;
    if (sortByFieldName === 'equipment') {
      orderQuery = [['equipmentDetails', 'Equipment', 'equipmentName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'approvedUser') {
      orderQuery = [['approverDetails', 'User', 'firstName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'company') {
      orderQuery = [['companyDetails', 'Company', 'companyName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'dfow') {
      orderQuery = [['defineWorkDetails', 'DeliverDefineWork', 'DFOW', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'member') {
      orderQuery = [['memberDetails', 'Member', 'User', 'firstName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'datetime') {
      orderQuery = [['craneDeliveryStart', `${sortByColumnType}`]];
    }
    if (
      sortByFieldName === 'description' ||
      sortByFieldName === 'id' ||
      sortByFieldName === 'status' ||
      sortByFieldName === 'pickUpLocation' ||
      sortByFieldName === 'dropOffLocation' ||
      sortByFieldName === 'requestType'
    ) {
      orderQuery = [[`${sortByFieldName}`, `${sortByColumnType}`]];
    }
    if (descriptionFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ description: { [Sequelize.Op.iLike]: `%${descriptionFilter}%` } }],
          },
        ],
      };
    }
    if (dateFilter) {
      const startDateTime = moment(dateFilter, 'YYYY-MM-DD')
        .startOf('day')
        .utcOffset(Number(req.headers.timezoneoffset), true);
      const endDateTime = moment(dateFilter, 'YYYY-MM-DD')
        .endOf('day')
        .utcOffset(Number(req.headers.timezoneoffset), true);
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                craneDeliveryStart: {
                  [Op.between]: [moment(startDateTime), moment(endDateTime)],
                },
              },
            ],
          },
        ],
      };
    }
    if (pickFrom) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ pickUpLocation: { [Sequelize.Op.iLike]: `%${pickFrom}%` } }],
          },
        ],
      };
    }
    if (pickTo) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ dropOffLocation: { [Sequelize.Op.iLike]: `%${pickTo}%` } }],
          },
        ],
      };
    }
    if (memberFilter > 0) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$memberDetails.Member.id$': +memberFilter,
              },
            ],
          },
        ],
      };
    }
    if (equipmentFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$equipmentDetails.Equipment.equipmentName$': {
                  [Sequelize.Op.iLike]: `${equipmentFilter}`,
                },
              },
            ],
          },
        ],
      };
    }
    if (locationFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$location.locationPath$': {
                  [Sequelize.Op.iLike]: `${locationFilter}`,
                },
              },
            ],
          },
        ],
      };
    }
    // if (defineFilter) {
    //   commonSearch = {
    //     [Op.and]: [
    //       {
    //         ...commonSearch,
    //         [Op.or]: [
    //           {
    //             '$defineWorkDetails.DeliverDefineWork.id$': +defineFilter,
    //           },
    //         ],
    //       },
    //     ],
    //   };
    // }
    if (statusFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ status: statusFilter }],
          },
        ],
      };
    }
    if (idFilter) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [{ CraneRequestId: idFilter }],
          },
        ],
      };
    }
    if (typeof companyFilter === 'string' && companyFilter !== '') {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                '$companyDetails.Company.companyName$': {
                  [Sequelize.Op.iLike]: `${companyFilter}`,
                },
              },
            ],
          },
        ],
      };
    }
    if (startdate) {
      const startDateTime = moment(startdate, 'YYYY-MM-DD')
        .startOf('day')
        .utcOffset(Number(req.headers.timezoneoffset), true);
      const endDateTime = moment(enddate, 'YYYY-MM-DD')
        .endOf('day')
        .utcOffset(Number(req.headers.timezoneoffset), true);
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              {
                craneDeliveryStart: {
                  [Op.between]: [moment(startDateTime), moment(endDateTime)],
                },
              },
            ],
          },
        ],
      };
    }
    if (search) {
      commonSearch = {
        [Op.and]: [
          {
            ...commonSearch,
            [Op.or]: [
              { description: { [Sequelize.Op.iLike]: `%${search}%` } },
              { pickUpLocation: { [Sequelize.Op.iLike]: `%${search}%` } },
              { dropOffLocation: { [Sequelize.Op.iLike]: `%${search}%` } },
              {
                '$equipmentDetails.Equipment.equipmentName$': {
                  [Sequelize.Op.iLike]: `%${search}%`,
                },
              },
              {
                '$location.locationPath$': {
                  [Sequelize.Op.iLike]: `%${search}%`,
                },
              },
            ],
          },
        ],
      };
    }
    let requiredCondition = true;
    if (roleId === 2) {
      requiredCondition = false;
    }
    const newDeliveryRequest = await CraneRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'UserId'],
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
        },
        {
          association: 'Project',
          attributes: ['projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          required: false,
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              include: [
                {
                  required: true,
                  where: { isDeleted: false, isActive: true, isCraneType: true },
                  association: 'PresetEquipmentType',
                  attributes: ['id', 'equipmentType', 'isCraneType'],
                },
              ],
              association: 'Equipment',
              attributes: ['equipmentName', 'id'],
            },
          ],
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'CraneRequestId'],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
          ],
        },
        {
          association: 'location',
          required: false,
          attributes: ['id', 'locationPath'],
        },
      ],
      where: commonSearch,
      attributes: [
        'id',
        'description',
        'craneDeliveryStart',
        'craneDeliveryEnd',
        'status',
        'additionalNotes',
        'CraneRequestId',
        'approved_at',
        'isEscortNeeded',
        'pickUpLocation',
        'dropOffLocation',
        'isAssociatedWithDeliveryRequest',
        'requestType',
        'LocationId',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
      order: orderQuery,
    });
    return newDeliveryRequest;
  };
  CraneRequest.getCalendarData = async (attr, searchCondition, order, sort, sortColumn) => {
    const sortByFieldName = sortColumn || 'id';
    let sortByColumnType = sort || 'DESC';
    if (order) {
      sortByColumnType = order;
    }
    let orderQuery;
    if (sortByFieldName === 'equipment') {
      orderQuery = [['equipmentDetails', 'Equipment', 'equipmentName', `${sortByColumnType}`]];
    }
    if (sortByFieldName === 'approvedUser') {
      orderQuery = [['approverDetails', 'User', 'firstName', `${sortByColumnType}`]];
    }
    if (
      sortByFieldName === 'description' ||
      sortByFieldName === 'deliveryStart' ||
      sortByFieldName === 'id' ||
      sortByFieldName === 'status'
    ) {
      orderQuery = [[`${sortByFieldName}`, `${sortByColumnType}`]];
    }
    const craneRequest = await CraneRequest.findAndCountAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id'],
              include: [{ association: 'User', attributes: ['email', 'firstName', 'lastName'] }],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'Project',
          attributes: ['projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          association: 'location',
          required: false,
        },
        // {
        //   association: 'voidList',
        //   attributes: ['id', 'MemberId', 'ProjectId', 'DeliveryRequestId'],
        // },
      ],
      where: { ...attr, ...searchCondition, isDeleted: false },
      attributes: [
        'id',
        'description',
        'craneDeliveryStart',
        'craneDeliveryEnd',
        'status',
        'additionalNotes',
        'CraneRequestId',
        'approved_at',
        'isEscortNeeded',
        'pickUpLocation',
        'dropOffLocation',
        'isAssociatedWithDeliveryRequest',
        'requestType',
        'LocationId',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
      order: orderQuery,
    });
    return craneRequest;
  };
  CraneRequest.getSingleCraneRequestData = async (attr) => {
    const newCraneRequest = await CraneRequest.findOne({
      subQuery: false,
      include: [
        {
          association: 'memberDetails',
          required: false,
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Company', attributes: ['companyName', 'id'] }],
        },
        {
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          required: false,
          attributes: ['id'],
          include: [{ association: 'Equipment', attributes: ['equipmentName', 'id'] }],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'recurrence',
          required: false,
          attributes: [
            'id',
            'recurrence',
            'recurrenceStartDate',
            'recurrenceEndDate',
            'dateOfMonth',
            'monthlyRepeatType',
            'repeatEveryCount',
            'days',
            'requestType',
            'repeatEveryType',
            'chosenDateOfMonth',
            'createdBy',
          ],
        },
        {
          association: 'location',
          required: false,
        },
      ],
      where: { ...attr, isDeleted: false },
      attributes: [
        'id',
        'description',
        'craneDeliveryStart',
        'craneDeliveryEnd',
        'status',
        'additionalNotes',
        'CraneRequestId',
        'approved_at',
        'isEscortNeeded',
        'pickUpLocation',
        'dropOffLocation',
        'isAssociatedWithDeliveryRequest',
        'requestType',
        'LocationId',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
    });
    return newCraneRequest;
  };
  CraneRequest.upcomingCraneRequest = async (condition, ProjectId) => {
    const commonSearch = {
      isDeleted: false,
      ProjectId,
      isAssociatedWithDeliveryRequest: false,
      requestType: 'craneRequest',
      craneDeliveryStart: { [Op.gt]: new Date() },
      ...condition,
    };
    const CraneRequestData = await CraneRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'UserId'],
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
        },
        {
          association: 'location',
          required: false,
        },
      ],
      where: commonSearch,
      attributes: [
        'id',
        'description',
        'craneDeliveryStart',
        'craneDeliveryEnd',
        'status',
        'additionalNotes',
        'CraneRequestId',
        'approved_at',
        'isEscortNeeded',
        'pickUpLocation',
        'dropOffLocation',
        'isAssociatedWithDeliveryRequest',
        'requestType',
        'ProjectId',
        'LocationId',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
      order: [['craneDeliveryStart', 'ASC']],
    });
    return CraneRequestData;
  };
  CraneRequest.getWeeklyCalendarList = async (
    req,
    roleId,
    attr,
    start,
    end,
    startTime,
    endTime,
    startDate,
    endDate,
    typeFormat,
    timezone,
    eventStartTime,
    eventEndTime,
  ) => {
    let commonSearch = {
      ...attr,
      isDeleted: false,
    };
    let startDate1 = start;
    let endDate1 = end;
    if (req.body.startDate && req.body.endDate) {
      startDate1 = req.body.startDate;
      endDate1 = req.body.endDate;
    }
    const finalFromTimeSeconds = timeToSeconds(eventStartTime);
    const finalToTimeSeconds = timeToSeconds(eventEndTime);

    function timeToSeconds(timeString) {
      const [hours, minutes, seconds] = timeString.split(':');
      return +hours * 60 * 60 + +minutes * 60 + +seconds;
    }

    let singleQuery = true;

    if (finalFromTimeSeconds > finalToTimeSeconds) {
      singleQuery = false;
    } else {
      singleQuery = true;
    }
    if (typeFormat) {
      if (start) {
        const startDateTime = moment(startDate1, 'YYYY-MM-DD');
        const endDateTime = moment(endDate1, 'YYYY-MM-DD');
        const nextDay = moment(startDate1).add(1, 'days');
        const queryStartDate = nextDay.format('YYYY-MM-DD');
        if (singleQuery) {
          commonSearch = {
            [Op.and]: [
              sequelize.literal(`(DATE_TRUNC('day', "CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDateTime}' AND '${endDateTime}'
                                    AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                                    AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time <= '${eventEndTime}')`),
            ],
          };
        } else {
          commonSearch = {
            [Op.or]: [
              sequelize.literal(`(DATE_TRUNC('day', "CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDateTime}' AND '${endDateTime}'
                                    AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                                    AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time <= '23:59:59')`),
              sequelize.literal(`(DATE_TRUNC('day', "CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${queryStartDate}' AND '${endDateTime}'
                                    AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time >= '00:00:00'
                                    AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time <=  '${eventEndTime}')`),
            ],
          };
        }
      }
    } else {
      const nextDay = moment(startDate1).add(1, 'days');
      const queryStartDate = nextDay.format('YYYY-MM-DD');
      if (singleQuery) {
        commonSearch = {
          [Op.and]: [
            sequelize.literal(`(DATE_TRUNC('day', "CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate1}' AND '${endDate1}'
                                    AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                                    AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time <= '${eventEndTime}')`),
          ],
        };
      } else {
        commonSearch = {
          [Op.or]: [
            sequelize.literal(`(DATE_TRUNC('day', "CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${startDate1}' AND '${endDate1}'
                                    AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time >= '${eventStartTime}'
                                    AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time <= '23:59:59')`),
            sequelize.literal(`(DATE_TRUNC('day', "CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}') BETWEEN '${queryStartDate}' AND '${endDate1}'
                                    AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time >= '00:00:00'
                                    AND ("CraneRequest"."craneDeliveryStart" AT TIME ZONE '${timezone}')::time <=  '${eventEndTime}')`),
          ],
        };
      }
    }
    let requiredCondition = true;
    if (roleId === 2) {
      requiredCondition = false;
    }
    const newDeliveryRequest = await CraneRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          required: requiredCondition,
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'UserId'],
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
        },
        {
          association: 'Project',
          attributes: ['projectName'],
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          required: false,
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              include: [
                {
                  required: true,
                  where: { isDeleted: false, isActive: true, isCraneType: true },
                  association: 'PresetEquipmentType',
                  attributes: ['id', 'equipmentType', 'isCraneType'],
                },
              ],
              association: 'Equipment',
              attributes: ['equipmentName', 'id'],
            },
          ],
        },
        {
          association: 'voidList',
          attributes: ['id', 'MemberId', 'ProjectId', 'CraneRequestId'],
        },
        {
          association: 'location',
          required: false,
        },
      ],
      where: { ...attr, ...commonSearch, isDeleted: false },
      attributes: [
        'id',
        'description',
        'craneDeliveryStart',
        'craneDeliveryEnd',
        'status',
        'additionalNotes',
        'CraneRequestId',
        'approved_at',
        'isEscortNeeded',
        'pickUpLocation',
        'dropOffLocation',
        'isAssociatedWithDeliveryRequest',
        'requestType',
        'LocationId',
        // 'chosenDateOfMonth',
        // 'dateOfMonth',
        // 'monthlyRepeatType',
        // 'recurrence',
        // 'repeatEveryCount',
        // 'repeatEveryType',
        // 'days',
      ],
    });
    return newDeliveryRequest;
  };

  CraneRequest.upcomingCraneRequestForMobile = async (condition, ProjectId) => {
    const commonSearch = {
      isDeleted: false,
      ProjectId,
      isAssociatedWithDeliveryRequest: false,
      requestType: 'craneRequest',
      craneDeliveryStart: { [Op.gt]: new Date() },
      ...condition,
    };
    const CraneRequestData = await CraneRequest.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'memberDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              association: 'Member',
              attributes: ['id', 'UserId'],
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
        {
          association: 'companyDetails',
          where: { isDeleted: false },
          required: false,
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
        },
        {
          association: 'location',
          required: false,
        },
        {
          association: 'createdUserDetails',
          required: false,
          attributes: ['id', 'RoleId'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          association: 'approverDetails',
          attributes: ['id'],
          include: [
            {
              association: 'User',
              attributes: ['email', 'firstName', 'lastName'],
            },
          ],
        },
        {
          required: false,
          association: 'defineWorkDetails',
          where: { isDeleted: false },
          attributes: ['id'],
          include: [{ association: 'DeliverDefineWork', attributes: ['DFOW', 'id'] }],
        },
        {
          required: false,
          association: 'equipmentDetails',
          where: { isDeleted: false, isActive: true },
          attributes: ['id'],
          include: [
            {
              include: [
                {
                  required: true,
                  where: { isDeleted: false, isActive: true, isCraneType: true },
                  association: 'PresetEquipmentType',
                  attributes: ['id', 'equipmentType', 'isCraneType'],
                },
              ],
              association: 'Equipment',
              attributes: ['equipmentName', 'id'],
            },
          ],
        },
      ],
      where: commonSearch,
      limit: 2,
      attributes: [
        'id',
        'description',
        'craneDeliveryStart',
        'craneDeliveryEnd',
        'status',
        'additionalNotes',
        'CraneRequestId',
        'approved_at',
        'isEscortNeeded',
        'pickUpLocation',
        'dropOffLocation',
        'isAssociatedWithDeliveryRequest',
        'requestType',
        'ProjectId',
        'LocationId',
      ],
      order: [['craneDeliveryStart', 'ASC']],
    });
    return CraneRequestData;
  };
  return CraneRequest;
};
