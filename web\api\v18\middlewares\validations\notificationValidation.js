const Joi = require('joi');

const notificationValidation = {
  listNotification: {
    params: Joi.object({
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
    }),
    body: Joi.object({
      dateFilter: Joi.optional().allow(''),
      descriptionFilter: Joi.optional().allow(''),
      projectNameFilter: Joi.number(),
      statusFilter: Joi.optional().allow(''),
      ParentCompanyId: Joi.any(),
      search: Joi.optional().allow(''),
      ProjectId: Joi.number(),
    }),
  },
  setReadNotification: {
    query: Joi.object({
      id: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  getNotificationCount: {
    query: Joi.object({
      ProjectId: Joi.any(),
      ParentCompanyId: Joi.any(),
    }),
  },
  deleteNotification: {
    query: Joi.object({
      id: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
};
module.exports = notificationValidation;
