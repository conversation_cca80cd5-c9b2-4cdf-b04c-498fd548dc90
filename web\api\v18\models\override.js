module.exports = (sequelize, DataTypes) => {
  const OverRide = sequelize.define(
    'OverRide',
    {
      MemberId: DataTypes.INTEGER,
      ProjectId: DataTypes.INTEGER,
      UserId: DataTypes.INTEGER,
      expiryDate: DataTypes.DATE,
      comment: DataTypes.STRING,
      reason: DataTypes.STRING,
      status: {
        type: DataTypes.ENUM,
        values: ['Pending', 'Approved', 'Rejected'],
      },
      StripePlanId: DataTypes.INTEGER,
    },
    {},
  );
  OverRide.associate = (models) => {
    // associations can be defined here
    OverRide.belongsTo(models.StripePlan);
    OverRide.belongsTo(models.User);
    OverRide.belongsTo(models.Member);
    OverRide.belongsTo(models.Project);
    return OverRide;
  };
  OverRide.createInstance = async (paramData) => {
    const newOverRide = await OverRide.create(paramData);
    return newOverRide;
  };
  OverRide.getBy = async (attr) => {
    const overRideDet = await OverRide.findOne({ where: { ...attr } });
    return overRideDet;
  };
  OverRide.getOverRides = async (limit, offset) => {
    let project;
    if (limit === 0 || offset === 0) {
      project = await OverRide.findAll({
        include: [
          {
            association: 'Member',
            include: [{ association: 'User', attributes: ['email', 'firstName', 'lastName'] }],
          },
          'StripePlan',
          'Project',
        ],
      });
    } else {
      project = await OverRide.findAll({
        include: [
          {
            association: 'Member',
            include: [{ association: 'User', attributes: ['email', 'firstName', 'lastName'] }],
          },
          'StripePlan',
          'Project',
        ],
        limit,
        offset,
      });
    }
    return project;
  };
  return OverRide;
};
