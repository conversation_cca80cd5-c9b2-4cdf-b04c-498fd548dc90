const jwt = require('jsonwebtoken');

exports.token = (user) => {
  const userObj = {
    id: user.id,
    domainName: user.domainName,
  };
  return jwt.sign(
    {
      iss: 'folloit', // change issuer name
      sub: userObj,
      iat: new Date().getTime(), // current time
      exp: new Date().setDate(new Date().getDate() + 1), // current time + 1 day ahead
    },
    process.env.JWT_SECRET,
  );
};
