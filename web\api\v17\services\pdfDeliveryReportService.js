const moment = require('moment');
const fs = require('fs');
const awsConfig = require('../middlewares/awsConfig');
const puppeteerService = require('./puppeteerService');
const { Project, Company } = require('../models');

const pdfDeliveryReportService = {
  async pdfFormatOfDeliveryRequest(params, loginUser, data, req, done) {
    const { timezoneoffset } = req.headers;
    const headerList = req.body.selectedHeaders;
    const header = [];
    const mailContent = [];
    let isIdSelected = false;
    let isDescriptionSelected = false;
    let isDateSelected = false;
    let isStatusSelected = false;
    let isApprovedBySelected = false;
    let isEquipmentSelected = false;
    let isDfowSelected = false;
    let isGateSelected = false;
    let isCompanySelected = false;
    let isPersonSelected = false;
    headerList.map((object) => {
      if (object.isActive === true) {
        if (object.key === 'id') isIdSelected = true;
        if (object.key === 'description') isDescriptionSelected = true;
        if (object.key === 'date') isDateSelected = true;
        if (object.key === 'status') isStatusSelected = true;
        if (object.key === 'approvedby') isApprovedBySelected = true;
        if (object.key === 'equipment') isEquipmentSelected = true;
        if (object.key === 'dfow') isDfowSelected = true;
        if (object.key === 'gate') isGateSelected = true;
        if (object.key === 'company') isCompanySelected = true;
        if (object.key === 'name') isPersonSelected = true;
        const headerTag = `<th style="text-align :center">
          ${object.title}</th>`;
        header.push(headerTag);
      }
      return object;
    });
    const projectData = await Project.findOne({
      where: {
        isDeleted: false,
        id: +params.ProjectId,
      },
      attributes: ['projectName'],
    });
    const companyData = await Company.findOne({
      where: {
        isDeleted: false,
        ParentCompanyId: +req.body.ParentCompanyId,
        isParent: true,
      },
      attributes: ['companyName'],
    });
    let pdftemplate = fs.readFileSync(
      '/usr/src/web/api/v17/views/mail-templates/deliveryReport.html',
      {
        encoding: 'utf-8',
      },
    );
    for (let index = 0; index < data.length; index += 1) {
      const idTag = isIdSelected
        ? `<td style="padding:5px;color: #5B5B5B;font-weight: 600; text-align:center;
         font-size:12px;font-family:'Cairo',sans-serif">
       ${data[index].DeliveryId}</td>`
        : '';
      const descriptionTag = isDescriptionSelected
        ? `<td style="color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">${data[index].description}</td>`
        : '';
      const dateTag = isDateSelected
        ? `<td style="color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">
         ${moment(
          new Date(
            `${moment(data[index].deliveryStart).format('MM/DD/YYYY')} ${moment(
              data[index].deliveryStart,
            ).format('hh:mm a')}`,
          ),
        )
          .add(Number(timezoneoffset), 'm')
          .format('MM/DD/YYYY hh:mm a')}
                 </td>`
        : '';
      const statusTag = isStatusSelected
        ? `<td style = "color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif"> ${data[index].status}</td>`
        : '';
      const approvedByTag = isApprovedBySelected
        ? `<td style = "color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">
  ${data[index].approverDetails
          ? `${data[index].approverDetails.User.firstName} ${data[index].approverDetails.User.lastName}`
          : '-'
        }
                  </td>`
        : '';
      const equipmentTag = isEquipmentSelected
        ? `<td style = "color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">
  ${data[index].equipmentDetails && data[index].equipmentDetails[0]
          ? data[index].equipmentDetails[0].Equipment.equipmentName
          : '-'
        }</td>`
        : '';

      const gateTag = isGateSelected
        ? `<td style = "color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">
  ${data[index].gateDetails && data[index].gateDetails[0]
          ? data[index].gateDetails[0].Gate.gateName
          : '-'
        }</td>`
        : '';

      let dfowTag = [];
      if (isDfowSelected) {
        let dfowValues = [];
        if (data[index].defineWorkDetails && data[index].defineWorkDetails.length > 0) {
          data[index].defineWorkDetails.map((element) => {
            const dfow = `<p> ${element.DeliverDefineWork.DFOW}</p>`;
            dfowValues.push(dfow);
            return dfow;
          });
        } else {
          dfowValues = '-';
        }
        dfowTag = `<td style = "color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">
  ${dfowValues}
                 </td>`;
      }

      let companyTag = [];
      if (isCompanySelected) {
        let companyValues = [];
        if (data[index].companyDetails && data[index].companyDetails.length > 0) {
          data[index].companyDetails.map((element) => {
            const company = `<p> ${element.Company.companyName}</p>`;
            companyValues.push(company);
            return company;
          });
        } else {
          companyValues = '-';
        }
        companyTag = `<td style = "color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">
  ${companyValues}
                 </td>`;
      }

      let memberTag = [];
      if (isPersonSelected) {
        let memberValues = [];
        if (data[index].memberDetails && data[index].memberDetails.length > 0) {
          data[index].memberDetails.map((element) => {
            if (
              element &&
              element.Member &&
              element.Member.User &&
              element.Member.User.firstName &&
              element.Member.User.lastName
            ) {
              const member = `<p> ${element.Member.User.firstName} ${element.Member.User.lastName}</p>`;
              memberValues.push(member);
              return member;
            }
          });
        } else {
          memberValues = '-';
        }
        memberTag = `<td style = "color: #5B5B5B;font-weight: 600; text-align:center;font-size:12px;font-family:'Cairo',sans-serif">
  ${memberValues}
                 </td>`;
      }
      const content = `<tr style = "border-bottom: 1px solid #e0e0e0;font-size: 12px">
  ${idTag}${descriptionTag}${dateTag}${statusTag}${approvedByTag}${equipmentTag}${dfowTag}${gateTag}${companyTag}${memberTag}
        </tr>`;
      mailContent.push(content);
    }
    pdftemplate = pdftemplate
      .replace('$projectName', `${projectData.projectName} `)
      .replace('$companyName', `${companyData.companyName} `)
      .replace('$generatedDate', req.body.generatedDate)
      .replace('$generatedBy', `${loginUser.firstName} ${loginUser.lastName} `)
      .replace('$reportType', 'Delivery')
      .replace('$header', `${header} `)
      .replace('$data', `${mailContent} `);
    pdftemplate = pdftemplate.replace(/,/g, '');

    const pdfBuffer = await puppeteerService.generatePdfBuffer(pdftemplate);
    if (pdfBuffer) {
      awsConfig.reportUpload(
        pdfBuffer,
        req.body.reportName,
        req.body.exportType,
        async (result, error1) => {
          if (!error1) {
            done(result, false);
          }
        },
      );
    } else {
      done(false, { message: 'cannot export the document' });
    }
  },
};
module.exports = pdfDeliveryReportService;
