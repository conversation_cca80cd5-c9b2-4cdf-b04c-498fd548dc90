const status = require('http-status');
const { companyService, projectService } = require('../services');
const { Company } = require('../models');
const exportService = require('../services/exportService');

const CompanyController = {
  async getAllCompanies(req, res, next) {
    try {
      await companyService.getAll(req, (response, err) => {
        if (!err) {
          res.status(status.OK).json({
            message: 'Company list.',
            data: response.companyArray,
            parentCompany: response.parentCompany,
          });
        } else {
          next(err);
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async companyLogoUpload(req, res, next) {
    try {
      await companyService.companyLogoUpload(req, (response, err) => {
        if (!err) {
          res.status(status.OK).json({
            message: 'Company uploaded successfully.',
            data: response,
          });
        } else {
          next(err);
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getDefinableWork(req, res, next) {
    try {
      await companyService.getDefinableWork(req, (response, err) => {
        if (!err) {
          res.status(status.OK).json({
            message: 'Definable work list.',
            data: response.defineRecord,
          });
        } else {
          next(err);
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getCompanies(req, res, next) {
    try {
      await companyService.getAllCompany(req, async (response, err) => {
        if (!err) {
          const { companyList, parentCompany } = response;
          const newCompanyList = [];
          await companyList.rows.forEach((element) => {
            newCompanyList.push({
              id: element.id,
              companyName: element.companyName,
            });
          });
          if (parentCompany) {
            const index = newCompanyList.findIndex(
              (item) =>
                item.id === parentCompany.id ||
                item.companyName.toLowerCase() === parentCompany.companyName.toLowerCase(),
            );
            if (index === -1) {
              newCompanyList.push({
                id: parentCompany.id,
                companyName: parentCompany.companyName,
              });
            }
          }
          newCompanyList.sort((a, b) =>
            a.companyName.toLowerCase() > b.companyName.toLowerCase() ? 1 : -1,
          );
          res.status(status.OK).json({
            message: 'Company list.',
            data: newCompanyList,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async addCompany(req, res, next) {
    try {
      await companyService.addCompany(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Company Created Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async deleteCompany(req, res, next) {
    try {
      await companyService.deleteCompany(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Company Deleted Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async editCompany(req, res, next) {
    try {
      await companyService.editCompany(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Company Updated Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {super admin token} req
   * @param {companyList} res
   * @param {return if any exception} next
   */
  async getAllCompaniesList(req, res, next) {
    try {
      let condition;
      if (req.query.parentCompanyId) {
        condition = {
          isDeleted: false,
          ParentCompanyId: req.query.parentCompanyId,
          isParent: true,
        };
      } else {
        condition = {
          isDeleted: false,
          isParent: true,
        };
      }
      const companyList = await Company.getAllCompany(condition);
      if (companyList) {
        res.status(status.OK).json({
          status: 200,
          message: 'Companies listed Successfully.',
          data: companyList,
        });
      } else {
        res.status(status.OK).json({
          status: 200,
          message: 'Companies listed successfully.',
          data: [],
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {token} req
   * @param {companyList} res
   * @param {return if any exception} next
   */
  async checkExistCompany(req, res, next) {
    try {
      const existCompany = await companyService.checkExistCompany(req);
      if (existCompany.existInProject.length > 0 || existCompany.sameAsParentCompany) {
        res.status(status.OK).json({
          status: 422,
          message: 'Company name exist.',
          data: existCompany,
        });
      }
      if (existCompany.existInProject.length === 0 && !existCompany.sameAsParentCompany) {
        res.status(status.OK).json({
          status: 200,
          message: 'Company name not exist.',
          data: [],
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async sampleCompanyTemplate(req, res, next) {
    try {
      const data = {
        ProjectId: req.params.ProjectId,
        ParentCompanyId: req.params.ParentCompanyId,
      };
      req.data = data;
      const Workbook = await exportService.sampleCompanyTemplate(req);
      const projectDetail = await projectService.getProjectDetails(req);
      const fileName = `${projectDetail.projectName}_${projectDetail.id}_${new Date().getTime()}`;
      if (Workbook) {
        res.setHeader(
          'Content-Type',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8;responseType= arraybuffer"',
        );
        res.setHeader('Content-Disposition', `attachment; filename=${fileName}.xlsx`);
        await Workbook.xlsx.write(res);
        res.end();
      } else {
        res.status(422).json({ message: 'cannot export document', status: 422 });
      }
    } catch (e) {
      next(e);
    }
  },
  async createCompany(req, res, next) {
    try {
      await companyService.createCompany(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            status: 201,
            message: 'Companies Created Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = CompanyController;
