const { Router } = require('express');
const { validate } = require('express-validation');
const { ConcreteRequestController } = require('../controllers');
const passportConfig = require('../config/passport');
const { concreteRequestValidation } = require('../middlewares/validations');

const concreteRoute = {
  get router() {
    const router = Router();
    router.post(
      '/create_concrete_request',
      validate(
        concreteRequestValidation.concreteRequest,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      ConcreteRequestController.createConcreteRequest,
    );
    router.post(
      '/edit_concrete_request',
      validate(
        concreteRequestValidation.editConcreteRequest,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      ConcreteRequestController.editConcreteRequest,
    );
    router.post(
      '/get_concrete_request_list/:ProjectId/:pageSize/:pageNo/:void',
      passportConfig.isAuthenticated,
      ConcreteRequestController.getConcreteRequestList,
    );
    router.get(
      '/get_last_concrete_request_id/:ProjectId/?:ParentCompanyId',
      passportConfig.isAuthenticated,
      ConcreteRequestController.getLastConcreteRequestId,
    );
    router.get(
      '/get_single_Concrete_request/:ConcreteRequestId/:ProjectId/?:ParentCompanyId',
      validate(
        concreteRequestValidation.getSingleConcreteRequest,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      ConcreteRequestController.getSingleConcreteRequest,
    );
    router.post(
      '/update_concrete_request_status',
      validate(
        concreteRequestValidation.updateConcreteRequestStatus,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      ConcreteRequestController.updateConcreteRequestStatus,
    );
    router.get(
      '/upcoming_Concrete_request',
      passportConfig.isAuthenticated,
      ConcreteRequestController.upcomingConcreteRequest,
    );
    router.get(
      '/upcoming_request_list',
      passportConfig.isAuthenticated,
      ConcreteRequestController.upcomingRequestList,
    );
    router.get(
      '/concrete_dropdown_detail',
      passportConfig.isAuthenticated,
      ConcreteRequestController.getConcreteDropdownDetail,
    );
    router.post(
      '/delete_concrete_request',
      passportConfig.isAuthenticated,
      ConcreteRequestController.deleteConcreteRequest,
    );
    router.post(
      '/edit_multiple_request',
      passportConfig.isAuthenticated,
      ConcreteRequestController.editMultipleDeliveryRequest,
    );
    return router;
  },
};
module.exports = concreteRoute;
