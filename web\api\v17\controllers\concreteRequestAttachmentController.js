const status = require('http-status');
const { concreteRequestAttachmentService } = require('../services');

const concreteRequestAttachmentController = {
  async createConcreteRequestAttachment(req, res, next) {
    try {
      await concreteRequestAttachmentService.createConcreteRequestAttachment(
        req,
        (response, error) => {
          if (error) {
            next(error);
          } else {
            res.status(status.CREATED).json({
              message: 'Uploaded Successfully.',
              data: response,
            });
          }
        },
      );
    } catch (e) {
      next(e);
    }
  },
  async getConcreteRequestAttachments(req, res, next) {
    try {
      await concreteRequestAttachmentService.getConcreteRequestAttachments(
        req,
        (response, error) => {
          if (error) {
            next(error);
          } else {
            res.status(status.OK).json({
              message: 'Concrete Booking Attachment Viewed Successfully.',
              data: response.attachmentList,
              concreteRequest: response.exist,
            });
          }
        },
      );
    } catch (e) {
      next(e);
    }
  },
  async deleteConcreteRequestAttachment(req, res, next) {
    try {
      await concreteRequestAttachmentService.deleteConcreteRequestAttachment(
        req,
        (response, error) => {
          if (error) {
            next(error);
          } else {
            res.status(status.OK).json({
              message: 'Concrete Booking Attachment Deleted Successfully.',
              data: response,
            });
          }
        },
      );
    } catch (e) {
      next(e);
    }
  },
};
module.exports = concreteRequestAttachmentController;
