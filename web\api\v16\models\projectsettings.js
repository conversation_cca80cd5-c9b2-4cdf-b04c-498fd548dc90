module.exports = (sequelize, DataTypes) => {
  const projectSettings = sequelize.define(
    'ProjectSettings',
    {
      deliveryWindowTime: DataTypes.INTEGER,
      deliveryWindowTimeUnit: DataTypes.STRING,
      ProjectId: DataTypes.INTEGER,
      isAutoApprovalEnabled: DataTypes.BOOLEAN,
      deliveryAllowOverlappingBooking: DataTypes.BOOLEAN,
      deliveryAllowOverlappingCalenderEvents: DataTypes.BOOLEAN,
      craneAllowOverlappingBooking: DataTypes.BOOLEAN,
      craneAllowOverlappingCalenderEvents: DataTypes.BOOLEAN,
      concreteAllowOverlappingBooking: DataTypes.BOOLEAN,
      concreteAllowOverlappingCalenderEvents: DataTypes.BOOLEAN,
      statusColorCode: DataTypes.TEXT,
      defaultStatusColor: DataTypes.TEXT,
      deliveryCard: DataTypes.TEXT,
      craneCard: DataTypes.TEXT,
      concreteCard: DataTypes.TEXT,
      defaultDeliveryCard: DataTypes.TEXT,
      defaultCraneCard: DataTypes.TEXT,
      defaultConcreteCard: DataTypes.TEXT,
      isDefaultColor: DataTypes.BOOLEAN,
      projectLogisticPlanUrl: DataTypes.TEXT,
      pdfOriginalName: DataTypes.TEXT,
      isPdfUploaded: DataTypes.BOOLEAN,
    },
    {},
  );
  projectSettings.associate = (models) => {
    projectSettings.belongsTo(models.Project);
    return projectSettings;
  };

  projectSettings.getCalendarStatusColor = async (ProjectId) => {
    const status = await projectSettings.findOne({
      where: {
        ProjectId,
      },
      attributes: ['statusColorCode', 'isDefaultColor'],
    });
    return status;
  };

  projectSettings.getCalendarCard = async (ProjectId) => {
    const status = await projectSettings.findOne({
      where: {
        ProjectId,
      },
      attributes: ['deliveryCard', 'craneCard', 'concreteCard'],
    });
    return status;
  };

  return projectSettings;
};
