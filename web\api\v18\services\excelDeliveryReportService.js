const moment = require('moment');

const excelDeliveryReportService = {
  async deliveryReport(workbook, responseData, selectedHeaders, timezoneoffset) {
    const worksheet = workbook.addWorksheet('Delivery Report');
    /* Note */
    /* Column headers */
    const rowValues = [];
    const columns = [];
    let isIdSelected = false;
    let isDescriptionSelected = false;
    let isDateSelected = false;
    let isStatusSelected = false;
    let isApprovedBySelected = false;
    let isEquipmentSelected = false;
    let isDfowSelected = false;
    let isGateSelected = false;
    let isCompanySelected = false;
    let isPersonSelected = false;
    selectedHeaders.map((object) => {
      if (object.isActive === true) {
        rowValues.push(object.title);
        if (object.key === 'id') {
          columns.push({ key: object.key, width: 5 });
        } else {
          columns.push({ key: object.key, width: 32 });
        }
        if (object.key === 'id') isIdSelected = true;
        if (object.key === 'description') isDescriptionSelected = true;
        if (object.key === 'date') isDateSelected = true;
        if (object.key === 'status') isStatusSelected = true;
        if (object.key === 'approvedby') isApprovedBySelected = true;
        if (object.key === 'equipment') isEquipmentSelected = true;
        if (object.key === 'dfow') isDfowSelected = true;
        if (object.key === 'gate') isGateSelected = true;
        if (object.key === 'company') isCompanySelected = true;
        if (object.key === 'name') isPersonSelected = true;
      }
      return object;
    });

    worksheet.getRow(1).values = rowValues;
    worksheet.columns = columns;
    const cellRange = {
      0: 'A',
      1: 'B',
      2: 'C',
      3: 'D',
      4: 'E',
      5: 'F',
      6: 'G',
      7: 'H',
      8: 'I',
      9: 'J',
    };
    // Insert data into worksheet
    for (let index = 1; index <= responseData.length; index += 1) {
      worksheet.addRow();
      if (isIdSelected) {
        const cellValue = cellRange[rowValues.indexOf('Id')];
        worksheet.getCell(`${cellValue}${index + 1}`).value = responseData[index - 1].DeliveryId;
      }
      if (isDescriptionSelected) {
        const cellValue = cellRange[rowValues.indexOf('Description')];
        worksheet.getCell(`${cellValue}${index + 1}`).value = responseData[index - 1].description;
      }
      if (isDateSelected) {
        const cellValue = cellRange[rowValues.indexOf('Date & Time')];
        worksheet.getCell(`${cellValue}${index + 1}`).value = moment(
          new Date(
            `${moment(responseData[index - 1].deliveryStart).format('MM/DD/YYYY')} ${moment(
              responseData[index - 1].deliveryStart,
            ).format('hh:mm a')}`,
          ),
        )
          .add(Number(timezoneoffset), 'm')
          .format('MM/DD/YYYY hh:mm a');
      }
      if (isStatusSelected) {
        const cellValue = cellRange[rowValues.indexOf('Status')];
        worksheet.getCell(`${cellValue}${index + 1}`).value = responseData[index - 1].status;
      }
      if (isApprovedBySelected) {
        const cellValue = cellRange[rowValues.indexOf('Approved By')];
        worksheet.getCell(`${cellValue}${index + 1}`).value =
          responseData[index - 1].approverDetails &&
          responseData[index - 1].approverDetails.User.firstName
            ? `${responseData[index - 1].approverDetails.User.firstName} ${
                responseData[index - 1].approverDetails.User.lastName
              }`
            : '-';
      }
      if (isEquipmentSelected) {
        // const cellValue = cellRange[rowValues.indexOf('Equipment')];
        // worksheet.getCell(`${cellValue}${index + 1}`).value =
        //   responseData[index - 1].equipmentDetails && responseData[index - 1].equipmentDetails[0]
        //     ? responseData[index - 1].equipmentDetails[0].Equipment.equipmentName
        //     : '-';
        
            const cellValue = cellRange[rowValues.indexOf('Equipment')];
            if (
              responseData[index - 1].equipmentDetails &&
              responseData[index - 1].equipmentDetails.length > 0
            ) {
              const equipmentValues = [];
              for (let m = 0; m < responseData[index - 1].equipmentDetails.length; m += 1) {
                if (
                  responseData[index - 1].equipmentDetails &&
                  responseData[index - 1].equipmentDetails[m]
                ) {
                  equipmentValues.push(responseData[index - 1].equipmentDetails[m].Equipment.equipmentName);
                }
              }
              const equipment = equipmentValues.join(', ');
              worksheet.getCell(`${cellValue}${index + 1}`).value = equipment;
            } else {
              worksheet.getCell(`${cellValue}${index + 1}`).value = '-';
            }
      }
      if (isDfowSelected) {
        const cellValue = cellRange[rowValues.indexOf('Definable Feature of Work')];
        if (
          responseData[index - 1].defineWorkDetails &&
          responseData[index - 1].defineWorkDetails.length > 0
        ) {
          const dfowValues = [];
          for (let m = 0; m < responseData[index - 1].defineWorkDetails.length; m += 1) {
            if (
              responseData[index - 1].defineWorkDetails &&
              responseData[index - 1].defineWorkDetails[m]
            ) {
              dfowValues.push(responseData[index - 1].defineWorkDetails[m].DeliverDefineWork.DFOW);
            }
          }
          const dfow = dfowValues.join(', ');
          worksheet.getCell(`${cellValue}${index + 1}`).value = dfow;
        } else {
          worksheet.getCell(`${cellValue}${index + 1}`).value = '-';
        }
      }
      if (isGateSelected) {
        const cellValue = cellRange[rowValues.indexOf('Gate')];
        worksheet.getCell(`${cellValue}${index + 1}`).value =
          responseData[index - 1].gateDetails && responseData[index - 1].gateDetails[0]
            ? responseData[index - 1].gateDetails[0].Gate.gateName
            : '-';
      }
      if (isCompanySelected) {
        const cellValue = cellRange[rowValues.indexOf('Responsible Company')];
        if (
          responseData[index - 1].companyDetails &&
          responseData[index - 1].companyDetails.length > 0
        ) {
          const companyValues = [];
          for (let m = 0; m < responseData[index - 1].companyDetails.length; m += 1) {
            if (
              responseData[index - 1].companyDetails &&
              responseData[index - 1].companyDetails[m]
            ) {
              companyValues.push(responseData[index - 1].companyDetails[m].Company.companyName);
            }
          }
          const company = companyValues.join(', ');
          worksheet.getCell(`${cellValue}${index + 1}`).value = company;
        } else {
          worksheet.getCell(`${cellValue}${index + 1}`).value = '-';
        }
      }
      if (isPersonSelected) {
        const cellValue = cellRange[rowValues.indexOf('Responsible Person')];
        if (
          responseData[index - 1].memberDetails &&
          responseData[index - 1].memberDetails.length > 0
        ) {
          const memberValues = [];
          for (let m = 0; m < responseData[index - 1].memberDetails.length; m += 1) {
            if (
              responseData[index - 1].memberDetails &&
              responseData[index - 1].memberDetails[m] &&
              responseData[index - 1].memberDetails[m].Member &&
              responseData[index - 1].memberDetails[m].Member.User
            ) {
              memberValues.push(
                `${responseData[index - 1].memberDetails[m].Member.User.firstName} ${
                  responseData[index - 1].memberDetails[m].Member.User.lastName
                }`,
              );
            }
          }
          const member = memberValues.join(', ');
          worksheet.getCell(`${cellValue}${index + 1}`).value = member;
        } else {
          worksheet.getCell(`${cellValue}${index + 1}`).value = '-';
        }
      }
    }
    return workbook;
  },
};
module.exports = excelDeliveryReportService;
