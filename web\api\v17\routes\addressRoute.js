const { Router } = require('express');
const { validate } = require('express-validation');
const { addressValidation } = require('../middlewares/validations');
const { AddressController } = require('../controllers');

const addressRoute = {
  get router() {
    const router = Router();
    router.get('/get_country', AddressController.getCountry);
    router.get(
      '/get_state/:CountryId',
      validate(addressValidation.getState, { keyByField: true }, { abortEarly: false }),
      AddressController.getState,
    );
    router.get(
      '/get_city/:StateId',
      validate(addressValidation.getCity, { keyByField: true }, { abortEarly: false }),
      AddressController.getCity,
    );

    return router;
  },
};
module.exports = addressRoute;
