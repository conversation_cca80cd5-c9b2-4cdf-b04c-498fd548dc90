const Joi = require('joi');

const guestUserValidation = {
  getCompanies: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
  },
  createGuestUser: {
    body: Joi.object({
      email: Joi.string().email().required(),
      phoneNumber: Joi.string().required(),
      phoneCode: Joi.string().required(),
      firstName: Joi.string().required(),
      lastName: Joi.string().required(),
      companyName: Joi.string().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      companyId: Joi.number().required(),
    }),
  },
  emailDetail: {
    body: Joi.object({
      email: Joi.string().email().required(),
      ProjectId: Joi.number().required(),
    }),
  },
  listAllMember: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  getMemberData: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
      id: Joi.number().required(),
    }),
  },
  newRequest: {
    body: Joi.object({
      description: Joi.string().min(3).required(),
      companies: Joi.array().required(),
      escort: Joi.boolean().required(),
      ProjectId: Joi.number().required(),
      GateId: Joi.number().required(),
      notes: Joi.optional().allow(''),
      EquipmentId: Joi.number().required(),
      LocationId: Joi.number().required(),
      define: Joi.array().required(),
      deliveryStart: Joi.date().required(),
      deliveryEnd: Joi.date().required(),
      ParentCompanyId: Joi.any(),
      persons: Joi.array().min(1).required(),
      isAssociatedWithCraneRequest: Joi.boolean().required(),
      requestType: Joi.string().required(),
      cranePickUpLocation: Joi.string().optional().allow('', null),
      craneDropOffLocation: Joi.string().optional().allow('', null),
      CraneRequestId: Joi.number().optional().allow('', null),
      recurrence: Joi.string().valid('Does Not Repeat', 'Daily', 'Weekly', 'Monthly', 'Yearly'),
      repeatEveryCount: Joi.when('recurrence', {
        is: Joi.string().valid('Daily', 'Weekly', 'Monthly'),
        then: Joi.string().min(1).required(),
        otherwise: Joi.string().allow('', null),
      }),
      repeatEveryType: Joi.when('recurrence', {
        is: Joi.string().valid('Daily', 'Weekly', 'Monthly'),
        then: Joi.string().min(1).required(),
        otherwise: Joi.string().allow('', null),
      }),
      days: Joi.when('recurrence', {
        is: Joi.string().valid('Daily', 'Weekly'),
        then: Joi.array().min(1).required(),
        otherwise: Joi.array().allow('', null),
      }),
      chosenDateOfMonth: Joi.when('recurrence', {
        is: Joi.string().valid('Monthly', 'Yearly'),
        then: Joi.boolean().required(),
        otherwise: Joi.boolean().allow('', null),
      }),
      dateOfMonth: Joi.when('recurrence', {
        is: Joi.string().valid('Monthly', 'Yearly'),
        then: Joi.when('chosenDateOfMonth', {
          is: Joi.boolean().valid(true),
          then: Joi.string().min(1).required(),
          otherwise: Joi.string().allow('', null),
        }),
        otherwise: Joi.string().allow('', null),
      }),
      monthlyRepeatType: Joi.when('recurrence', {
        is: Joi.string().valid('Monthly', 'Yearly'),
        then: Joi.when('chosenDateOfMonth', {
          is: Joi.string().valid(false),
          then: Joi.string().min(1).required(),
          otherwise: Joi.string().allow('', null),
        }),
        otherwise: Joi.string().allow('', null),
      }),
      TimeZoneId: Joi.number().required(),
      endPicker: Joi.string().allow('', null),
      startPicker: Joi.string().allow('', null),
      userId: Joi.number().required(),
    }),
  },
  craneRequest: {
    body: Joi.object({
      description: Joi.string().min(3).required(),
      companies: Joi.array().min(1).required(),
      isEscortNeeded: Joi.boolean().required(),
      ProjectId: Joi.number().required(),
      userId: Joi.number().required(),
      additionalNotes: Joi.optional().allow(''),
      EquipmentId: Joi.number().required(),
      LocationId: Joi.number().required(),
      definableFeatureOfWorks: Joi.array().required(),
      craneDeliveryStart: Joi.date().required(),
      craneDeliveryEnd: Joi.date().required(),
      ParentCompanyId: Joi.any(),
      responsiblePersons: Joi.array().min(1).required(),
      isAssociatedWithDeliveryRequest: Joi.boolean().required(),
      pickUpLocation: Joi.string().required(),
      dropOffLocation: Joi.string().required(),
      recurrence: Joi.string().valid('Does Not Repeat', 'Daily', 'Weekly', 'Monthly', 'Yearly'),
      repeatEveryCount: Joi.when('recurrence', {
        is: Joi.string().valid('Daily', 'Weekly', 'Monthly'),
        then: Joi.string().min(1).required(),
        otherwise: Joi.string().allow('', null),
      }),
      repeatEveryType: Joi.when('recurrence', {
        is: Joi.string().valid('Daily', 'Weekly', 'Monthly'),
        then: Joi.string().min(1).required(),
        otherwise: Joi.string().allow('', null),
      }),
      days: Joi.when('recurrence', {
        is: Joi.string().valid('Daily', 'Weekly'),
        then: Joi.array().min(1).required(),
        otherwise: Joi.array().allow('', null),
      }),
      chosenDateOfMonth: Joi.when('recurrence', {
        is: Joi.string().valid('Monthly', 'Yearly'),
        then: Joi.boolean().required(),
        otherwise: Joi.boolean().allow('', null),
      }),
      dateOfMonth: Joi.when('recurrence', {
        is: Joi.string().valid('Monthly', 'Yearly'),
        then: Joi.when('chosenDateOfMonth', {
          is: Joi.boolean().valid(true),
          then: Joi.string().min(1).required(),
          otherwise: Joi.string().allow('', null),
        }),
        otherwise: Joi.string().allow('', null),
      }),
      monthlyRepeatType: Joi.when('recurrence', {
        is: Joi.string().valid('Monthly', 'Yearly'),
        then: Joi.when('chosenDateOfMonth', {
          is: Joi.string().valid(false),
          then: Joi.string().min(1).required(),
          otherwise: Joi.string().allow('', null),
        }),
        otherwise: Joi.string().allow('', null),
      }),
      TimeZoneId: Joi.number().required(),
      endPicker: Joi.string().allow('', null),
      startPicker: Joi.string().allow('', null),
    }),
  },
  concreteRequest: {
    body: Joi.object({
      location: Joi.string().optional().allow('', null),
      // location: Joi.array().items({
      //   id: Joi.number().required().allow(null, ''),
      //   location: Joi.string().required(),
      //   chosenFromDropdown: Joi.boolean().required(),
      // }),
      LocationId: Joi.number().required(),
      mixDesign: Joi.array().items({
        id: Joi.number().optional().allow(null, ''),
        mixDesign: Joi.string().optional(),
        chosenFromDropdown: Joi.boolean().optional(),
      }).optional(),
      pumpSize: Joi.array().items({
        id: Joi.number().required().allow(null, ''),
        pumpSize: Joi.string().required(),
        chosenFromDropdown: Joi.boolean().required(),
      }),
      description: Joi.string().required(),
      concreteSupplier: Joi.array().min(1).required(),
      concreteOrderNumber: Joi.string().optional().allow(null, ''),
      truckSpacingHours: Joi.string().optional().allow(null, ''),
      notes: Joi.string().optional().allow('', null),
      slump: Joi.string().optional().allow(null, ''),
      concreteQuantityOrdered: Joi.string().optional().allow(null, ''),
      concreteConfirmedOn: Joi.date().required().allow('', null),
      isConcreteConfirmed: Joi.boolean().required(),
      pumpLocation: Joi.string().allow('', null),
      pumpOrderedDate: Joi.string().allow('', null),
      pumpWorkStart: Joi.string().allow('', null),
      pumpWorkEnd: Joi.string().allow('', null),
      pumpConfirmedOn: Joi.date().allow('', null),
      isPumpConfirmed: Joi.boolean().required(),
      isPumpRequired: Joi.boolean().required(),
      ProjectId: Joi.number().required(),
      userId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      ConcreteRequestId: Joi.number().required(),
      requestType: Joi.string().required(),
      primerForPump: Joi.string().optional().allow(null, ''),
      concretePlacementStart: Joi.date().required(),
      concretePlacementEnd: Joi.date().required(),
      responsiblePersons: Joi.array().min(1).required(),
      recurrence: Joi.string().valid('Does Not Repeat', 'Daily', 'Weekly', 'Monthly', 'Yearly'),
      repeatEveryCount: Joi.when('recurrence', {
        is: Joi.string().valid('Daily', 'Weekly', 'Monthly'),
        then: Joi.string().min(1).required(),
        otherwise: Joi.string().allow('', null),
      }),
      repeatEveryType: Joi.when('recurrence', {
        is: Joi.string().valid('Daily', 'Weekly', 'Monthly'),
        then: Joi.string().min(1).required(),
        otherwise: Joi.string().allow('', null),
      }),
      days: Joi.when('recurrence', {
        is: Joi.string().valid('Daily', 'Weekly'),
        then: Joi.array().min(1).required(),
        otherwise: Joi.array().allow('', null),
      }),
      chosenDateOfMonth: Joi.when('recurrence', {
        is: Joi.string().valid('Monthly', 'Yearly'),
        then: Joi.boolean().required(),
        otherwise: Joi.boolean().allow('', null),
      }),
      dateOfMonth: Joi.when('recurrence', {
        is: Joi.string().valid('Monthly', 'Yearly'),
        then: Joi.when('chosenDateOfMonth', {
          is: Joi.boolean().valid(true),
          then: Joi.string().min(1).required(),
          otherwise: Joi.string().allow('', null),
        }),
        otherwise: Joi.string().allow('', null),
      }),
      monthlyRepeatType: Joi.when('recurrence', {
        is: Joi.string().valid('Monthly', 'Yearly'),
        then: Joi.when('chosenDateOfMonth', {
          is: Joi.string().valid(false),
          then: Joi.string().min(1).required(),
          otherwise: Joi.string().allow('', null),
        }),
        otherwise: Joi.string().allow('', null),
      }),
      TimeZoneId: Joi.number().required(),
      endPicker: Joi.string().allow('', null),
      startPicker: Joi.string().allow('', null),
    }),
  },
  editDeliveryRequest: {
    body: Joi.object({
      id: Joi.number().required(),
      description: Joi.string().min(3).required(),
      DeliveryId: Joi.number(),
      companies: Joi.array().required(),
      escort: Joi.boolean().required(),
      ProjectId: Joi.number().required(),
      userId: Joi.number().required(),
      GateId: Joi.number().required(),
      notes: Joi.optional().allow(''),
      EquipmentId: Joi.number().required(),
      LocationId: Joi.number().required(),
      vehicleDetails: Joi.string().optional().allow('', null),
      define: Joi.array().required(),
      deliveryStart: Joi.date().required(),
      deliveryEnd: Joi.date().required(),
      persons: Joi.array().required(),
      ParentCompanyId: Joi.any(),
      isAssociatedWithCraneRequest: Joi.boolean().required(),
      requestType: Joi.string().required(),
      cranePickUpLocation: Joi.string().optional().allow('', null),
      craneDropOffLocation: Joi.string().optional().allow('', null),
      CraneRequestId: Joi.number().optional().allow('', null),
      recurrenceId: Joi.number().optional().allow('', null),
      recurrenceEndDate: Joi.date().optional().allow('', null),
      previousSeriesRecurrenceEndDate: Joi.date().required().allow('', null),
      nextSeriesRecurrenceStartDate: Joi.date().required().allow('', null),
      recurrenceSeriesStartDate: Joi.date().required().allow('', null),
      recurrenceSeriesEndDate: Joi.date().required().allow('', null),
      seriesOption: Joi.number().required(),
      deliveryStartTime: Joi.string().required(),
      deliveryEndTime: Joi.string().required(),
      timezone: Joi.string().required(),
    }),
  },
  editCraneRequest: {
    body: Joi.object({
      id: Joi.number().required(),
      description: Joi.string().min(3).required(),
      CraneRequestId: Joi.number(),
      companies: Joi.array().min(1).required(),
      isEscortNeeded: Joi.boolean().required(),
      ProjectId: Joi.number().required(),
      userId: Joi.number().required(),
      additionalNotes: Joi.optional().allow(''),
      EquipmentId: Joi.number().required(),
      LocationId: Joi.number().required(),
      definableFeatureOfWorks: Joi.array().required(),
      craneDeliveryStart: Joi.date().required(),
      craneDeliveryEnd: Joi.date().required(),
      responsiblePersons: Joi.array().min(1).required(),
      ParentCompanyId: Joi.any(),
      isAssociatedWithDeliveryRequest: Joi.boolean().required(),
      pickUpLocation: Joi.string().required(),
      dropOffLocation: Joi.string().required(),
      recurrenceId: Joi.number().optional().allow('', null),
      recurrenceEndDate: Joi.date().optional().allow('', null),
      previousSeriesRecurrenceEndDate: Joi.date().required().allow('', null),
      nextSeriesRecurrenceStartDate: Joi.date().required().allow('', null),
      recurrenceSeriesStartDate: Joi.date().required().allow('', null),
      recurrenceSeriesEndDate: Joi.date().required().allow('', null),
      seriesOption: Joi.number().required(),
      deliveryStartTime: Joi.string().required(),
      deliveryEndTime: Joi.string().required(),
      timezone: Joi.string().required(),
    }),
  },
  editConcreteRequest: {
    body: Joi.object({
      id: Joi.number().required(),
      // location: Joi.array().items({
      //   id: Joi.number().required().allow(null, ''),
      //   location: Joi.string().required(),
      //   chosenFromDropdown: Joi.boolean().required(),
      // }),
      location: Joi.string().optional().allow('', null),
      LocationDetailId: Joi.number().required(),
      LocationId: Joi.number().required(),
      mixDesign: Joi.array().items({
        id: Joi.number().optional().allow(null, ''),
        mixDesign: Joi.string().optional(),
        chosenFromDropdown: Joi.boolean().optional(),
      }).optional(),
      pumpSize: Joi.array().items({
        id: Joi.number().required().allow(null, ''),
        pumpSize: Joi.string().required(),
        chosenFromDropdown: Joi.boolean().required(),
      }),
      description: Joi.string().required(),
      concreteSupplier: Joi.array().min(1).required(),
      concreteOrderNumber: Joi.string().optional().allow(null, ''),
      truckSpacingHours: Joi.string().optional().allow(null, ''),
      notes: Joi.string().optional().allow('', null),
      slump: Joi.string().optional().allow(null, ''),
      concreteQuantityOrdered: Joi.string().optional().allow(null, ''),
      concreteConfirmedOn: Joi.date().required().allow('', null),
      isConcreteConfirmed: Joi.boolean().required(),
      pumpLocation: Joi.string().allow('', null),
      pumpOrderedDate: Joi.string().allow('', null),
      pumpWorkStart: Joi.string().allow('', null),
      pumpWorkEnd: Joi.string().allow('', null),
      pumpConfirmedOn: Joi.date().allow('', null),
      isPumpConfirmed: Joi.boolean().required(),
      isPumpRequired: Joi.boolean().required(),
      cubicYardsTotal: Joi.string().allow('', null),
      hoursToCompletePlacement: Joi.string().optional().allow(null, ''),
      minutesToCompletePlacement: Joi.string().optional().allow(null, ''),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      ConcreteRequestId: Joi.number().required(),
      requestType: Joi.string().required(),
      primerForPump: Joi.string().optional().allow(null, ''),
      concretePlacementStart: Joi.date().required(),
      concretePlacementEnd: Joi.date().required(),
      responsiblePersons: Joi.array().min(1).required(),
      recurrenceId: Joi.number().optional().allow('', null),
      recurrenceEndDate: Joi.date().optional().allow('', null),
      previousSeriesRecurrenceEndDate: Joi.date().required().allow('', null),
      nextSeriesRecurrenceStartDate: Joi.date().required().allow('', null),
      recurrenceSeriesStartDate: Joi.date().required().allow('', null),
      recurrenceSeriesEndDate: Joi.date().required().allow('', null),
      seriesOption: Joi.number().required(),
      deliveryStartTime: Joi.string().required(),
      deliveryEndTime: Joi.string().required(),
      timezone: Joi.string().required(),
      userId: Joi.number().required(),
    }),
  },
  createAttachement: {
    params: Joi.object({
      DeliveryRequestId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
      userId: Joi.number().required(),
    }),
  },
  addComment: {
    body: Joi.object({
      comment: Joi.string().required(),
      DeliveryRequestId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
      userId: Joi.number().required(),
    }),
  },
  createCraneRequestAttachement: {
    params: Joi.object({
      CraneRequestId: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      userId: Joi.number().required(),
    }),
  },
  createCraneRequestComment: {
    body: Joi.object({
      comment: Joi.string().required(),
      CraneRequestId: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
      userId: Joi.number().required(),
    }),
  },
  createConcreteRequestAttachment: {
    params: Joi.object({
      ConcreteRequestId: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      userId: Joi.number().required(),
    }),
  },
  createConcreteRequestComment: {
    body: Joi.object({
      comment: Joi.string().required(),
      ConcreteRequestId: Joi.number().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
      userId: Joi.number().required(),
    }),
  },
  requestMember: {
    body: Joi.object({
      userId: Joi.number().required(),
      ProjectId: Joi.number().required(),
    }),
  },
};
module.exports = guestUserValidation;
