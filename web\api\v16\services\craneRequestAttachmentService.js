const {
  Sequelize,
  Enterprise,
  NotificationPreference,
  Locations,
  LocationNotificationPreferences,
} = require('../models');

let {
  CraneRequest,
  CraneRequestHistory,
  Member,
  User,
  CraneRequestResponsiblePerson,
  DeliveryPersonNotification,
  Project,
  Notification,
  CraneRequestAttachment,
} = require('../models');
const helper = require('../helpers/domainHelper');
const notificationHelper = require('../helpers/notificationHelper');

const pushNotification = require('../config/fcm');

const awsConfig = require('../middlewares/awsConfig');

const { Op } = Sequelize;
let publicUser;
let publicMember;

const craneRequestAttachmentService = {
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    let enterpriseValue;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    const incomeData = inputData;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    CraneRequest = modelObj.CraneRequest;
    CraneRequestAttachment = modelObj.CraneRequestAttachment;
    CraneRequestHistory = modelObj.CraneRequestHistory;
    CraneRequestResponsiblePerson = modelObj.CraneRequestResponsiblePerson;
    Member = modelObj.Member;
    Project = modelObj.Project;
    User = modelObj.User;
    Notification = modelObj.Notification;
    DeliveryPersonNotification = modelObj.DeliveryPersonNotification;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return true;
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async getCraneRequestAttachements(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const exist = await CraneRequest.findOne({
        where: {
          CraneRequestId: inputData.params.CraneRequestId,
          ProjectId: +inputData.params.ProjectId,
        },
      });
      if (exist) {
        const attachementList = await CraneRequestAttachment.findAll({
          where: {
            CraneRequestId: exist.id,
            isDeleted: false,
          },
          order: [['id', 'DESC']],
        });
        done(attachementList, false);
      } else {
        done(null, { message: 'Crane Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async deleteCraneRequestAttachement(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const loginUser = inputData.user;
      const { params } = inputData;
      const attachement = await CraneRequestAttachment.findOne({
        where: { id: params.id, ProjectId: +params.ProjectId },
        include: [
          {
            association: 'CraneRequest',
          },
        ],
      });
      const ProjectId = +params.ProjectId;
      const memberDetail = await Member.findOne({
        where: [
          Sequelize.and({
            UserId: inputData.user.id,
            ProjectId,
            isDeleted: false,
          }),
        ],
      });
      await CraneRequestAttachment.update(
        { isDeleted: true },
        {
          where: {
            id: inputData.params.id,
          },
        },
      );
      const exist = await CraneRequest.findOne({
        where: { id: attachement.CraneRequestId, ProjectId: +params.ProjectId },
      });
      const locationChosen = await Locations.findOne({
        where: {
          ProjectId: exist.ProjectId,
          id: exist.LocationId,
        },
      });
      const memberLocationPreference = await LocationNotificationPreferences.findAll({
        where: {
          ProjectId: exist.ProjectId,
          LocationId: exist.LocationId,
          follow: true,
        },
        include: [
          {
            association: 'Member',
            attributes: ['id', 'RoleId'],
            where: {
              [Op.and]: [
                {
                  id: { [Op.ne]: memberDetail.id },
                },
              ],
            },
            include: [
              {
                association: 'User',
                attributes: ['id', 'firstName', 'lastName', 'email'],
              },
            ],
          },
        ],
      });
      const locationFollowMembers = [];
      memberLocationPreference.forEach(async (element) => {
        locationFollowMembers.push(element.Member.id);
      });
      const history = {
        CraneRequestId: attachement.CraneRequestId,
        MemberId: memberDetail.id,
        type: 'attachement',
        description: `${inputData.user.firstName} ${inputData.user.lastName} Removed the file in ${exist.description}`,
        locationFollowDescription: `${inputData.user.firstName} ${inputData.user.lastName} Removed the file in the Booking ${exist.description}. Location: ${locationChosen.locationPath}.`,
      };
      history.ProjectId = ProjectId;
      const notification = history;
      CraneRequestHistory.createInstance(history);
      notification.ProjectId = ProjectId;
      notification.title = 'Crane Booking Attachment';
      history.firstName = loginUser.firstName;
      history.profilePic = loginUser.profilePic;
      history.createdAt = new Date();
      history.ProjectId = ProjectId;
      const projectDetails = await Project.findByPk(ProjectId);
      history.projectName = projectDetails.projectName;
      notification.isDeliveryRequest = false;
      notification.requestType = 'craneRequest';
      const newNotification = await Notification.createInstance(notification);
      const personData = await CraneRequestResponsiblePerson.findAll({
        where: { CraneRequestId: attachement.CraneRequestId, isDeleted: false },
        include: [
          {
            association: 'Member',
            include: [
              {
                association: 'User',
                attributes: ['id', 'firstName', 'lastName'],
              },
            ],
            where: {
              [Op.and]: {
                RoleId: {
                  [Op.notIn]: [1, 2],
                },
                id: { [Op.notIn]: locationFollowMembers },
              },
            },
            attributes: ['id', 'RoleId'],
          },
        ],
        attributes: ['id'],
      });
      const adminData = await Member.findAll({
        where: {
          [Op.and]: [
            { ProjectId },
            { isDeleted: false },
            { id: { [Op.ne]: newNotification.MemberId } },
            { id: { [Op.notIn]: locationFollowMembers } },
          ],
        },
        include: [
          {
            association: 'User',
            attributes: ['id', 'firstName', 'lastName'],
          },
        ],
        attributes: ['id'],
      });
      if (memberLocationPreference && memberLocationPreference.length > 0) {
        await pushNotification.sendMemberLocationPreferencePushNotificationForCrane(
          memberLocationPreference,
          exist.CraneRequestId,
          history.locationFollowDescription,
          exist.requestType,
          exist.ProjectId,
          exist.id,
          2,
        );
        await notificationHelper.createMemberDeliveryLocationInAppNotification(
          DeliveryPersonNotification,
          exist.ProjectId,
          newNotification.id,
          memberLocationPreference,
          2,
        );
      }
      history.memberData = personData;
      history.adminData = adminData;
      const checkMemberNotification = await NotificationPreference.findAll({
        where: {
          ProjectId,
          isDeleted: false,
        },
        attributes: [
          'id',
          'MemberId',
          'ProjectId',
          'ParentCompanyId',
          'NotificationPreferenceItemId',
          'instant',
          'dailyDigest',
        ],
        include: [
          {
            association: 'NotificationPreferenceItem',
            where: {
              id: 2,
              isDeleted: false,
            },
            attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
          },
        ],
      });
      history.notificationPreference = checkMemberNotification;
      await notificationHelper.createDeliveryPersonNotification(
        adminData,
        personData,
        projectDetails,
        newNotification,
        DeliveryPersonNotification,
        memberDetail,
        loginUser,
        2,
        'deleted an attachment in a',
        'Crane Request',
        `crane Booking (${exist.CraneRequestId} - ${exist.description})`,
        attachement.CraneRequestId,
      );
      await pushNotification.sendPushNotificationForCrane(history, 2, ProjectId);
      done(history, false);
    } catch (e) {
      done(null, e);
    }
  },
  async createCraneRequestAttachement(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const incomeData = inputData.params;
      const loginUser = inputData.user;
      const bulkData = [];
      const exist = await CraneRequest.findOne({
        where: { CraneRequestId: incomeData.CraneRequestId, ProjectId: incomeData.ProjectId },
      });
      const memberDetail = await Member.findOne({
        where: [
          Sequelize.and({
            UserId: inputData.user.id,
            ProjectId: incomeData.ProjectId,
            isDeleted: false,
          }),
        ],
      });
      if (exist) {
        awsConfig.upload(inputData.files, async (result, err) => {
          if (!err) {
            const locationChosen = await Locations.findOne({
              where: {
                ProjectId: exist.ProjectId,
                id: exist.LocationId,
              },
            });
            const memberLocationPreference = await LocationNotificationPreferences.findAll({
              where: {
                ProjectId: exist.ProjectId,
                LocationId: exist.LocationId,
                follow: true,
              },
              include: [
                {
                  association: 'Member',
                  attributes: ['id', 'RoleId'],
                  where: {
                    [Op.and]: [
                      {
                        id: { [Op.ne]: memberDetail.id },
                      },
                    ],
                  },
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'firstName', 'lastName', 'email'],
                    },
                  ],
                },
              ],
            });
            const locationFollowMembers = [];
            memberLocationPreference.forEach(async (element) => {
              locationFollowMembers.push(element.Member.id);
            });
            result.forEach((element, i) => {
              const fileData = inputData.files[i];
              let fileName;
              let relativePath;
              let extension;
              if (fileData.originalname !== undefined) {
                fileName = fileData.originalname;
                relativePath = fileData.originalname.split('.');
                extension = relativePath[relativePath.length - 1];
              } else {
                fileName = fileData.name;
                relativePath = fileData.name.split('.');
                extension = relativePath[relativePath.length - 1];
              }

              const data = {
                attachement: element.Location,
                filename: fileName,
                extension,
                CraneRequestId: +exist.id,
                ProjectId: +incomeData.ProjectId,
                isDeleted: false,
              };
              bulkData.push(data);
            });
            if (bulkData.length > 0) {
              await CraneRequestAttachment.createMultipleInstance(bulkData);
              const history = {
                CraneRequestId: +exist.id,
                MemberId: memberDetail.id,
                type: 'attachement',
                description: `${inputData.user.firstName} ${inputData.user.lastName} Attached the file in ${exist.description}`,
                locationFollowDescription: `${inputData.user.firstName} ${inputData.user.lastName} Attached the file in the Booking, ${exist.description}. Location: ${locationChosen.locationPath}.`,
              };
              history.ProjectId = incomeData.ProjectId;
              const notification = history;
              notification.ProjectId = incomeData.ProjectId;
              notification.title = 'Crane Booking Attachment';
              const personData = await CraneRequestResponsiblePerson.findAll({
                where: { CraneRequestId: exist.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    {
                      ProjectId: incomeData.ProjectId,
                      isDeleted: false,
                      id: { [Op.notIn]: locationFollowMembers },
                    },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName'],
                  },
                ],
                attributes: ['id'],
              });
              CraneRequestHistory.createInstance(history);
              history.memberData = personData;
              history.adminData = adminData;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = incomeData.ProjectId;
              const projectDetails = await Project.findByPk(incomeData.ProjectId);
              history.projectName = projectDetails.projectName;
              notification.isDeliveryRequest = false;
              notification.requestType = 'craneRequest';
              const newNotification = await Notification.createInstance(notification);
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                await pushNotification.sendMemberLocationPreferencePushNotificationForCrane(
                  memberLocationPreference,
                  exist.CraneRequestId,
                  history.locationFollowDescription,
                  exist.requestType,
                  exist.ProjectId,
                  exist.id,
                  2,
                );
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  exist.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  2,
                );
              }
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                personData,
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberDetail,
                loginUser,
                1,
                'attached a file in a',
                'Crane Request',
                `crane Booking (${exist.CraneRequestId} - ${exist.description})`,
                incomeData.CraneRequestId,
              );
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: incomeData.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 1,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              await pushNotification.sendPushNotificationForCrane(history, 1, incomeData.ProjectId);
              done(history, false);
            }
          } else {
            done(null, err);
          }
        });
      } else {
        done(null, { message: 'Crane Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
};

module.exports = craneRequestAttachmentService;
