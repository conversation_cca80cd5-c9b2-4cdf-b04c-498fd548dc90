const { Router } = require('express');
const { validate } = require('express-validation');
const passportConfig = require('../config/passport');
const { CommentController } = require('../controllers');
const { commentValidation } = require('../middlewares/validations');

const commentRoute = {
  get router() {
    const router = Router();
    router.post(
      '/create_comment',
      validate(commentValidation.addComment, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      CommentController.createComment,
    );
    router.get(
      '/get_comment/:DeliveryRequestId/:ParentCompanyId/:ProjectId',
      validate(commentValidation.getComment, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      CommentController.getComment,
    );
    return router;
  },
};
module.exports = commentRoute;
