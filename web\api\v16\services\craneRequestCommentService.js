const moment = require('moment');
const Cryptr = require('cryptr');
const {
  Sequelize,
  Enterprise,
  NotificationPreference,
  DigestNotification,
  Locations,
  LocationNotificationPreferences,
} = require('../models');
let {
  CraneRequest,
  CraneRequestResponsiblePerson,
  CraneRequestComment,
  Member,
  CraneRequestHistory,
  User,
  DeliveryPersonNotification,
  Project,
  Notification,
} = require('../models');
const helper = require('../helpers/domainHelper');
const notificationHelper = require('../helpers/notificationHelper');
const pushNotification = require('../config/fcm');
const MAILER = require('../mailer');
// const mixpanelService = require('./mixpanelService');

const { Op } = Sequelize;
let publicUser;
let publicMember;

const craneRequestCommentService = {
  async getCraneRequestComments(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const exist = await CraneRequest.findOne({
        where: { CraneRequestId: +params.CraneRequestId, ProjectId: +params.ProjectId },
      });
      if (exist) {
        const commentList = await CraneRequestComment.findAndCountAll({
          include: [
            {
              association: 'Member',
              attributes: ['id'],
              include: [
                {
                  association: 'User',
                  attributes: ['email', 'firstName', 'lastName', 'profilePic'],
                },
              ],
            },
          ],
          where: {
            CraneRequestId: exist.id,
            isDeleted: false,
          },
        });
        done(commentList, false);
      } else {
        done(null, { message: 'Crane Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicUser = modelData.User;
    publicMember = modelData.Member;
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    let enterpriseValue;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    const incomeData = inputData;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    CraneRequest = modelObj.CraneRequest;
    CraneRequestResponsiblePerson = modelObj.CraneRequestResponsiblePerson;
    CraneRequestComment = modelObj.CraneRequestComment;
    Member = modelObj.Member;
    User = modelObj.User;
    CraneRequestHistory = modelObj.CraneRequestHistory;
    Project = modelObj.Project;
    DeliveryPersonNotification = modelObj.DeliveryPersonNotification;
    Notification = modelObj.Notification;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return true;
  },
  async createCraneRequestComment(inputData, done) {
    try {
      let resultedArray;
      await this.getDynamicModel(inputData);
      const incomeData = inputData.body;
      const loginUser = inputData.user;
      const exist = await CraneRequest.findOne({
        include: [
          {
            association: 'memberDetails',
            required: false,
            where: { isDeleted: false, isActive: true },
            attributes: ['id'],
            include: [
              {
                association: 'Member',
                attributes: ['id'],
                include: [
                  {
                    association: 'User',
                    attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                  },
                ],
              },
            ],
          },
        ],
        where: { CraneRequestId: +incomeData.CraneRequestId, ProjectId: +incomeData.ProjectId },
      });
      if (exist) {
        const memberData = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: incomeData.ProjectId,
            isDeleted: false,
          }),
        });
        const locationChosen = await Locations.findOne({
          where: {
            ProjectId: exist.ProjectId,
            id: exist.LocationId,
          },
        });
        const memberLocationPreference = await LocationNotificationPreferences.findAll({
          where: {
            ProjectId: exist.ProjectId,
            LocationId: exist.LocationId,
            follow: true,
          },
          include: [
            {
              association: 'Member',
              attributes: ['id', 'RoleId'],
              where: {
                [Op.and]: [
                  {
                    id: { [Op.ne]: memberData.id },
                  },
                ],
              },
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'firstName', 'lastName', 'email'],
                },
              ],
            },
          ],
        });
        const locationFollowMembers = [];
        memberLocationPreference.forEach(async (element) => {
          locationFollowMembers.push(element.Member.id);
        });
        const bookingMemberDetails = [];
        exist.memberDetails.forEach(async (element) => {
          bookingMemberDetails.push(element.Member.id);
        });
        incomeData.MemberId = memberData.id;
        const history = {
          CraneRequestId: +exist.id,
          MemberId: memberData.id,
          type: 'comment',
          description: `${loginUser.firstName} ${loginUser.lastName} Commented on ${exist.description} as ${incomeData.comment}`,
          locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Commented on ${exist.description} as ${incomeData.comment}. Location: ${locationChosen.locationPath}.`,
        };
        const notification = history;
        notification.ProjectId = exist.ProjectId;
        notification.title = 'Crane Booking Comment';
        const previousComments = await CraneRequestComment.findAll({
          where: {
            ProjectId: incomeData.ProjectId,
            CraneRequestId: incomeData.CraneRequestId,
          },
          attributes: ['id', 'comment'],
        });
        let commentsArray = [];
        commentsArray = previousComments.map((comment) => {
          return JSON.stringify(comment.comment);
        });
        resultedArray = commentsArray.join(',');
        const addCraneRequestCommentObject = {
          ProjectId: inputData.body.ProjectId,
          MemberId: memberData.id,
          CraneRequestId: exist.id,
          isDeleted: false,
          comment: inputData.body.comment,
        };
        await CraneRequestComment.createInstance(addCraneRequestCommentObject);
        await CraneRequestHistory.createInstance(history);
        history.firstName = loginUser.firstName;
        history.profilePic = loginUser.profilePic;
        history.createdAt = new Date();
        history.ProjectId = exist.ProjectId;
        const projectDetails = await Project.findByPk(exist.ProjectId);
        history.projectName = projectDetails.projectName;
        notification.isDeliveryRequest = false;
        notification.requestType = 'craneRequest';
        const newNotification = await Notification.createInstance(notification);
        const personData = await CraneRequestResponsiblePerson.findAll({
          where: { CraneRequestId: history.CraneRequestId, isDeleted: false },
          include: [
            {
              association: 'Member',
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
              where: {
                id: { [Op.ne]: newNotification.MemberId },
                [Op.and]: {
                  RoleId: {
                    [Op.notIn]: [1, 2],
                  },
                  id: { [Op.notIn]: locationFollowMembers },
                },
              },
              attributes: ['id', 'RoleId'],
            },
          ],
          attributes: ['id'],
        });

        const adminData = await Member.findAll({
          where: {
            [Op.and]: [
              { ProjectId: exist.ProjectId },
              { isDeleted: false },
              { id: { [Op.in]: bookingMemberDetails } },
              { id: { [Op.ne]: newNotification.MemberId } },
              { id: { [Op.notIn]: locationFollowMembers } },
            ],
          },
          include: [
            {
              association: 'User',
              attributes: ['id', 'email', 'firstName', 'lastName'],
            },
          ],
          attributes: ['id'],
        });
        if (memberLocationPreference && memberLocationPreference.length > 0) {
          await pushNotification.sendMemberLocationPreferencePushNotificationForCrane(
            memberLocationPreference,
            exist.CraneRequestId,
            history.locationFollowDescription,
            exist.requestType,
            exist.ProjectId,
            exist.id,
            4,
          );
          await notificationHelper.createMemberDeliveryLocationInAppNotification(
            DeliveryPersonNotification,
            exist.ProjectId,
            newNotification.id,
            memberLocationPreference,
            4,
          );
        }
        history.memberData = personData;
        history.adminData = adminData;
        if (memberLocationPreference && memberLocationPreference.length > 0) {
          history.memberData.push(...memberLocationPreference);
        }
        await notificationHelper.createDeliveryPersonNotification(
          adminData,
          personData,
          projectDetails,
          newNotification,
          DeliveryPersonNotification,
          memberData,
          loginUser,
          4,
          'commented in a',
          'Crane Request',
          `crane Booking (${exist.CraneRequestId} - ${exist.description})`,
          incomeData.CraneRequestId,
        );
        const checkMemberNotification = await NotificationPreference.findAll({
          where: {
            ProjectId: exist.ProjectId,
            isDeleted: false,
          },
          attributes: [
            'id',
            'MemberId',
            'ProjectId',
            'ParentCompanyId',
            'NotificationPreferenceItemId',
            'instant',
            'dailyDigest',
          ],
          include: [
            {
              association: 'NotificationPreferenceItem',
              where: {
                id: 4,
                isDeleted: false,
              },
              attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
            },
          ],
        });
        history.notificationPreference = checkMemberNotification;
        await pushNotification.sendPushNotificationForCrane(history, 4, exist.ProjectId);
        const userEmails = await this.getMemberDetailData(history, memberLocationPreference);
        if (userEmails.length > 0) {
          userEmails.forEach(async (element, i) => {
            const time = moment(exist.craneDeliveryStart).format('MM-DD-YYYY');
            const mailPayload = {
              craneId: exist.CraneRequestId,
              craneDescription: exist.description,
              craneDeliveryStart: time,
              craneDeliveryEnd: exist.craneDeliveryEnd,
              newComment: incomeData.comment,
              previousComments: resultedArray,
              toEmailUserName: element.firstName,
              email: element.email,
              commentedPersonname: loginUser.firstName,
              commentTimeStamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
            };
            if (!mailPayload.toEmailUserName) {
              mailPayload.toEmailUserName = element.email;
            }
            const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
              where: {
                MemberId: +element.MemberId,
                ProjectId: +exist.ProjectId,
                LocationId: +exist.LocationId,
                isDeleted: false,
                // follow: true,
              },
            });
            if (isMemberFollowLocation) {
              const memberNotification = await NotificationPreference.findOne({
                where: {
                  MemberId: +element.MemberId,
                  ProjectId: +exist.ProjectId,
                  isDeleted: false,
                },
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 7,
                      isDeleted: false,
                    },
                  },
                ],
              });
              if (memberNotification && memberNotification.instant) {
                await MAILER.sendMail(
                  mailPayload,
                  'cranecommentadded',
                  `Comments added by ${mailPayload.commentedPersonname} : ${mailPayload.newComment} on Crane ID ${exist.CraneRequestId}`,
                  'Comments added against a Crane Booking Notification',
                  async (info, err) => {
                    console.log(info, err);
                  },
                );
              }
              if (memberNotification && memberNotification.dailyDigest) {
                await this.createDailyDigestData(
                  +element.MemberId,
                  +exist.ProjectId,
                  +incomeData.ParentCompanyId,
                  loginUser,
                  'commented in a',
                  'Crane Request',
                  `crane Booking (${exist.CraneRequestId} - ${exist.description})`,
                  exist.CraneRequestId,
                );
              }
            }
            if (i === userEmails.length - 1) {
              done(history, false);
            }
          });
        } else {
          done(history, false);
        }
      } else {
        done(null, { message: 'Crane Booking does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getMemberDetailData(data, memberLocationPreference) {
    const emailArray = [];
    const existAdminData = [];
    if (data.memberData !== undefined) {
      data.memberData.forEach((element) => {
        const index = existAdminData.findIndex(
          (adminNew) => adminNew.email === element.Member.User.email,
        );
        if (index === -1) {
          existAdminData.push({ email: element.Member.User.email });
          emailArray.push({
            email: element.Member.User.email,
            firstName: element.Member.User.firstName,
            UserId: element.Member.User.id,
            MemberId: element.Member.id,
          });
        }
      });
    }
    if (data.adminData !== undefined) {
      data.adminData.forEach((element) => {
        const index = existAdminData.findIndex((adminNew) => adminNew.email === element.User.email);
        if (index === -1) {
          existAdminData.push({ email: element.User.email });
          emailArray.push({
            email: element.User.email,
            firstName: element.User.firstName,
            UserId: element.User.id,
            MemberId: element.id,
          });
        }
      });
    }
    if (memberLocationPreference !== undefined && memberLocationPreference.length > 0) {
      memberLocationPreference.forEach((element) => {
        const index = existAdminData.findIndex(
          (adminNew) => adminNew.email === element.Member.User.email,
        );
        if (index === -1) {
          existAdminData.push({ email: element.Member.User.email });
          emailArray.push({
            email: element.Member.User.email,
            firstName: element.Member.User.firstName,
            lastName: element.Member.User.lastName,
            UserId: element.Member.User.id,
            MemberId: element.Member.id,
            RoleId: element.Member.RoleId,
          });
        }
      });
    }
    return emailArray;
  },
  async createDailyDigestData(
    MemberId,
    ProjectId,
    ParentCompanyId,
    loginUser,
    dailyDigestMessage,
    requestType,
    messages,
    requestId,
  ) {
    const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
    const encryptedRequestId = cryptr.encrypt(requestId);
    const encryptedMemberId = cryptr.encrypt(MemberId);
    let imageUrl;
    let link;
    let height;
    if (requestType === 'Delivery Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/87758081-8ccd-4e4d-a4eb-6257466876da.png';
      link = 'delivery-request';
      height = 'height:18px;';
    }
    if (requestType === 'Crane Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/ab400721-520c-4f6f-941c-ac07acc28809.png';
      link = 'crane-request';
      height = 'height:32px;';
    }
    if (requestType === 'Concrete Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/c43e4c58-4c4d-44ff-b78c-fe9948ea53eb.png';
      link = 'concrete-request';
      height = 'height:18px;';
    }
    const object = {
      description: `<div>
	<ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
		<li style="display:flex;">
			<img src="${imageUrl}" alt="message-icon" style="${height}">
				<p style="margin:0px;font-size:12px;padding-left:10px;">
					<a href="#" target="" style="text-decoration: none;color:#4470FF;">
						${loginUser.firstName}  ${loginUser.lastName}
					</a>
					${dailyDigestMessage}
					<a href="${
            process.env.BASE_URL
          }/${link}?requestId=${encryptedRequestId}&memberId=${encryptedMemberId}" style="text-decoration: none;color:#4470FF;" >
      ${messages} 
					</a>
  <span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment()
        .utc()
        .format('hh:mm A zz')}</span>
				</p>
		</li>
	</ul>
</div> `,
      MemberId,
      ProjectId,
      isSent: false,
      isDeleted: false,
      ParentCompanyId,
    };
    await DigestNotification.create(object);
  },
};

module.exports = craneRequestCommentService;
