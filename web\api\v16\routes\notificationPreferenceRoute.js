const { Router } = require('express');
const { validate } = require('express-validation');
const { notificationPreferenceValidation } = require('../middlewares/validations');
const { NotificationPreferenceController } = require('../controllers');
const passportConfig = require('../config/passport');

const notificationPreferenceRoute = {
  get router() {
    const router = Router();
    router.post(
      '/set_notification_preference',
      validate(
        notificationPreferenceValidation.setNotificationPreference,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      NotificationPreferenceController.setNotificationPreference,
    );
    router.get(
      '/get_notification_preference_list',
      validate(
        notificationPreferenceValidation.listNotificationPreference,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      NotificationPreferenceController.listNotificationPreference,
    );
    router.get(
      '/get_notification_preference_items',
      passportConfig.isAuthenticated,
      NotificationPreferenceController.notificationPreferenceItems,
    );
    router.get(
      '/add_notification_preference_fields_to_all_members',
      passportConfig.isAuthenticated,
      NotificationPreferenceController.addNotificationPreferenceToAllMembers,
    );
    router.post(
      '/add_notification_preference_fields_to_a_member',
      passportConfig.isAuthenticated,
      NotificationPreferenceController.addNotificationPreferenceToAMember,
    );
    router.get(
      '/remove_notification_preference_deactivated_members',
      passportConfig.isAuthenticated,
      NotificationPreferenceController.removeNotificationPreferenceForDeactivatedMembers,
    );
    return router;
  },
};
module.exports = notificationPreferenceRoute;
