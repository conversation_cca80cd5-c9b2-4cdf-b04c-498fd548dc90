const { Router } = require('express');
const { validate } = require('express-validation');
const { gateValidation } = require('../middlewares/validations');
const { GateController } = require('../controllers');
const passportConfig = require('../config/passport');
const checkAdmin = require('../middlewares/checkAdmin');

const gateRoute = {
  get router() {
    const router = Router();
    router.post(
      '/add_gates',
      validate(gateValidation.addGates, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdmin,
      GateController.addGates,
    );
    router.post(
      '/update_gates',
      validate(gateValidation.updateGates, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdmin,
      GateController.updateGates,
    );
    router.post(
      '/gate_list/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId',
      validate(gateValidation.gateDetail, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      GateController.listGates,
    );
    router.post(
      '/delete_gates',
      validate(gateValidation.deleteGates, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      GateController.deleteGates,
      checkAdmin.isProjectAdmin,
    );
    router.post(
      '/get_mapped_requests',
      passportConfig.isAuthenticated,
      GateController.getMappedRequests,
    );
    router.post('/deactivate_gate', passportConfig.isAuthenticated, GateController.deactivateGate);
    return router;
  },
};
module.exports = gateRoute;
