const status = require('http-status');
const { overRideService } = require('../services');
const { OverRide } = require('../models');

const OverRideController = {
  async applyOverRide(req, res, next) {
    try {
      await overRideService.applyOverRide(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Your Booking sent Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async listOverRide(req, res, next) {
    try {
      const { params } = req;
      const offset = (+params.pageNo - 1) * +params.pageSize;
      const overRideList = await OverRide.getOverRides(+params.pageSize, offset);
      res.status(status.OK).json({
        message: 'Booking listed Successfully.',
        data: overRideList,
      });
    } catch (e) {
      next(e);
    }
  },
  async adminAction(req, res, next) {
    try {
      await overRideService.adminAction(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: `This booking ${req.body.status} successfully.`,
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = OverRideController;
