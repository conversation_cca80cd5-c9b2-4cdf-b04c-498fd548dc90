module.exports = (sequelize, DataTypes) => {
  const ConcreteRequestComment = sequelize.define(
    'ConcreteRequestComment',
    {
      ProjectId: DataTypes.INTEGER,
      MemberId: DataTypes.INTEGER,
      ConcreteRequestId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      isDeleted: DataTypes.BOOLEAN,
      comment: DataTypes.STRING,
    },
    {},
  );
  ConcreteRequestComment.associate = (models) => {
    ConcreteRequestComment.belongsTo(models.Member);
    ConcreteRequestComment.belongsTo(models.ConcreteRequest);
  };

  ConcreteRequestComment.getAll = async (attr) => {
    const newConcreteRequestComment = await ConcreteRequestComment.findAll({
      where: { ...attr },
    });
    return newConcreteRequestComment;
  };
  ConcreteRequestComment.createInstance = async (paramData) => {
    const newConcreteRequestComment = await ConcreteRequestComment.create(paramData);
    return newConcreteRequestComment;
  };
  return ConcreteRequestComment;
};
