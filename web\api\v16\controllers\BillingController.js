const status = require('http-status');

const { billingService } = require('../services');

const BillingController = {
  async payOffline(req, res, next) {
    await billingService.payOffline(req, (response, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.OK).json({
          message: 'Payment Request Sent Successfully.',
        });
      }
    });
  },
  async payOnline(req, res, next) {
    await billingService.payOnline(req, (response, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.OK).json({
          message: 'Payment Successfull.',
        });
      }
    });
  },
  async getBillingInfo(req, res, next) {
    await billingService.getBillingInfo(req, (response, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.OK).json({
          message: 'Billing listed successfully.',
          data: response,
        });
      }
    });
  },
};

module.exports = BillingController;
