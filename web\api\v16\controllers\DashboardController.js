const status = require('http-status');
const { dashboardService } = require('../services');

const DashboardController = {
  async getDashboardData(req, res, next) {
    try {
      await dashboardService.getDashboardData(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Dashboard data Listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getPADashboardData(req, res, next) {
    try {
      req.PAadmin = true;
      await dashboardService.getDashboardData(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Dashboard data Listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getGraphDelivery(req, res, next) {
    try {
      await dashboardService.getGraphDelivery(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Dashboard count Listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getCraneGraphData(req, res, next) {
    try {
      await dashboardService.getCraneGraphData(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Dashboard count Listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getConcreteGraphData(req, res, next) {
    try {
      await dashboardService.getConcreteGraphData(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Dashboard count Listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async upcomingDelivery(req, res, next) {
    try {
      await dashboardService.upcomingDelivery(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Delivery Listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getReleasenoteVersion(req, res, next) {
    try {
      await dashboardService.getReleasenoteVersion(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'release notes seen',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = DashboardController;
