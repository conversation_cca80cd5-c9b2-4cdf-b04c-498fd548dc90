/* eslint-disable no-loop-func */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-await-in-loop */
const moment = require('moment');
// const moment = require('moment');
// const moment = require('moment');
const Moment = require('moment');
const MomentRange = require('moment-range');
const httpStatus = require('http-status');

const momentRange = MomentRange.extendMoment(Moment);
const Cryptr = require('cryptr');
const ApiError = require('../helpers/apiError');

const {
  Sequelize,
  Enterprise,
  NotificationPreference,
  DigestNotification,
  TimeZone,
} = require('../models');
let {
  DeliveryRequest,
  Member,
  DeliveryPerson,
  DeliverGate,
  DeliverEquipment,
  DeliverCompany,
  Role,
  Gates,
  Equipments,
  DeliverDefineWork,
  Company,
  Project,
  DeliverDefine,
  DeliverHistory,
  VoidList,
  User,
  Notification,
  DeliveryPersonNotification,
  CraneRequestResponsible<PERSON>erson,
  CraneRequest,
} = require('../models');
const {
  CraneRequestDefinableFeatureOfWork,
  CraneRequestCompany,
  CraneRequestEquipment,
  CraneRequestHistory,
  ConcreteRequest,
  RequestRecurrenceSeries,
  LocationNotificationPreferences,
  Locations,
  ProjectSettings,
} = require('../models');
const MAILER = require('../mailer');
const helper = require('../helpers/domainHelper');
// const mixpanelService = require('./mixpanelService');
const notificationHelper = require('../helpers/notificationHelper');
const pushNotification = require('../config/fcm');
const concreteRequestService = require('./concreteRequestService');
const voidService = require('./voidService');

let publicUser;
let publicMember;
const { Op } = Sequelize;
const craneRequestService = {
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    // publicProject = modelData.Project;
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const incomeData = inputData;
    let enterpriseValue;
    let ProjectId;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    DeliveryRequest = modelObj.DeliveryRequest;
    Member = modelObj.Member;
    DeliveryPerson = modelObj.DeliveryPerson;
    DeliverGate = modelObj.DeliverGate;
    DeliverEquipment = modelObj.DeliverEquipment;
    DeliverCompany = modelObj.DeliverCompany;
    Role = modelObj.Role;
    Gates = modelObj.Gates;
    Equipments = modelObj.Equipments;
    DeliverDefineWork = modelObj.DeliverDefineWork;
    Company = modelObj.Company;
    Project = modelObj.Project;
    User = modelObj.User;
    DeliverDefine = modelObj.DeliverDefine;
    DeliverHistory = modelObj.DeliverHistory;
    VoidList = modelObj.VoidList;
    DeliveryPersonNotification = modelObj.DeliveryPersonNotification;
    CraneRequestResponsiblePerson = modelObj.CraneRequestResponsiblePerson;
    CraneRequest = modelObj.CraneRequest;
    Notification = modelObj.Notification;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return ProjectId;
  },
  async listCraneRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      const { sort } = inputData.body;
      const { sortByField } = inputData.body;
      let order;
      if (params.void >= 1 && params.void <= 0) {
        done(null, { message: 'Please enter void as 1 or 0' });
      } else {
        const memberDetails = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: params.ProjectId,
            isDeleted: false,
            isActive: true,
          }),
        });
        if (memberDetails) {
          const voidCraneDelivery = [];
          const voidDelivery = [];
          const voidDeliveryList = await VoidList.findAll({
            where: {
              ProjectId: params.ProjectId,
              isDeliveryRequest: true,
              DeliveryRequestId: { [Op.ne]: null },
            },
          });
          voidDeliveryList.forEach(async (element) => {
            voidDelivery.push(element.DeliveryRequestId);
          });
          const voidCraneRequestList = await VoidList.findAll({
            where: {
              ProjectId: params.ProjectId,
              isDeliveryRequest: false,
              CraneRequestId: { [Op.ne]: null },
            },
          });
          voidCraneRequestList.forEach(async (element) => {
            voidCraneDelivery.push(element.CraneRequestId);
          });
          const offset = (+params.pageNo - 1) * +params.pageSize;
          const craneCondition = {
            ProjectId: +params.ProjectId,
            isDeleted: false,
          };
          const condition = {
            ProjectId: +params.ProjectId,
            isDeleted: false,
          };
          if (params.void === '0' || params.void === 0) {
            condition['$DeliveryRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidDelivery }],
            };
          } else {
            condition['$DeliveryRequest.id$'] = {
              [Op.and]: [{ [Op.in]: voidDelivery }],
            };
          }
          if (params.void === '0' || params.void === 0) {
            craneCondition['$CraneRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidCraneDelivery }],
            };
          } else {
            craneCondition['$CraneRequest.id$'] = {
              [Op.and]: [{ [Op.in]: voidCraneDelivery }],
            };
          }
          const roleId = memberDetails.RoleId;
          const memberId = memberDetails.id;
          let craneRequestList;
          let deliveryRequest;
          if (
            (incomeData.gateFilter && incomeData.gateFilter > 0) ||
            (incomeData.statusFilter && incomeData.statusFilter === 'Delivered')
          ) {
            craneRequestList = [];
          } else {
            craneRequestList = await CraneRequest.getAll(
              inputData,
              roleId,
              memberId,
              craneCondition,
              incomeData.descriptionFilter,
              incomeData.startdate,
              incomeData.enddate,
              incomeData.companyFilter,
              incomeData.memberFilter,
              incomeData.equipmentFilter,
              incomeData.statusFilter,
              incomeData.idFilter,
              incomeData.pickFrom,
              incomeData.pickTo,
              incomeData.search,
              order,
              sort,
              sortByField,
              incomeData.dateFilter,
            );
          }
          if (incomeData.statusFilter && incomeData.statusFilter === 'Completed') {
            deliveryRequest = [];
          } else {
            deliveryRequest = await DeliveryRequest.getCraneAssociatedRequest(
              inputData,
              roleId,
              memberId,
              condition,
              incomeData.descriptionFilter,
              incomeData.startdate,
              incomeData.enddate,
              incomeData.companyFilter,
              incomeData.memberFilter,
              incomeData.equipmentFilter,
              incomeData.statusFilter,
              incomeData.idFilter,
              incomeData.pickFrom,
              incomeData.pickTo,
              incomeData.search,
              incomeData.gateFilter,
              order,
              sort,
              sortByField,
              params.void,
              incomeData.dateFilter,
            );
          }
          this.getSearchCraneData(
            inputData,
            incomeData,
            craneRequestList,
            [],
            +params.pageSize,
            0,
            0,
            memberDetails,
            async (checkResponse, checkError) => {
              if (!checkError) {
                craneRequestList = checkResponse;
                this.getSearchDeliveryData(
                  inputData,
                  incomeData,
                  deliveryRequest,
                  [],
                  +params.pageSize,
                  0,
                  0,
                  memberDetails,
                  async (checkResponse1, checkError1) => {
                    if (!checkError1) {
                      deliveryRequest = checkResponse1;
                      craneRequestList.push(...deliveryRequest);
                      this.getLimitData(
                        craneRequestList,
                        0,
                        +params.pageSize,
                        [],
                        incomeData,
                        inputData.headers.timezoneoffset,
                        async (newResponse, newError) => {
                          if (!newError) {
                            const newResult = { count: 0, rows: [] };
                            if (newResponse) {
                              if (sort === 'ASC') {
                                newResponse.sort(function (a, b) {
                                  // eslint-disable-next-line no-nested-ternary
                                  return a[sortByField] > b[sortByField]
                                    ? 1
                                    : b[sortByField] > a[sortByField]
                                      ? -1
                                      : 0;
                                });
                              } else {
                                newResponse.sort(function (a, b) {
                                  // eslint-disable-next-line no-nested-ternary
                                  return b[sortByField] > a[sortByField]
                                    ? 1
                                    : a[sortByField] > b[sortByField]
                                      ? -1
                                      : 0;
                                });
                              }
                            }

                            newResult.rows = newResponse.slice(offset, offset + +params.pageSize);
                            newResult.count = craneRequestList.length;
                            done(newResult, false);
                          } else {
                            done(null, { message: 'Something went wrong' });
                          }
                        },
                      );
                    } else {
                      done(null, { message: 'Something went wrong' });
                    }
                  },
                );
              } else {
                done(null, { message: 'Something went wrong' });
              }
            },
          );
        } else {
          done(null, { message: 'Project Id/Member does not exist' });
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getLimitData(result, index, limit, finalResult, incomeData, timezoneoffset, done) {
    if (index < limit) {
      finalResult.push(result);
      this.getLimitData(
        result,
        index + 1,
        limit,
        finalResult,
        incomeData,
        timezoneoffset,
        (response, err) => {
          if (!err) {
            done(result, false);
          } else {
            done(null, err);
          }
        },
      );
    } else {
      done(result, false);
    }
  },
  async lastCraneRequest(inputData, done) {
    try {
      const { params } = inputData;
      let data;
      let lastData = {};
      lastData = await CraneRequest.findOne({
        where: { ProjectId: params.ProjectId, isDeleted: false },
        order: [['CraneRequestId', 'DESC']],
      });
      const deliveryRequestList = await DeliveryRequest.findOne({
        where: {
          ProjectId: params.ProjectId,
          isDeleted: false,
          isAssociatedWithCraneRequest: true,
        },
        order: [['CraneRequestId', 'DESC']],
      });
      if (deliveryRequestList) {
        if (lastData) {
          if (deliveryRequestList.CraneRequestId > lastData.CraneRequestId) {
            lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
          }
        } else {
          lastData = {};
          lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
        }
      }
      if (lastData) {
        data = lastData.CraneRequestId + 1;
      } else {
        data = 1;
      }
      done({ CraneRequestId: data }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async newCraneRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const eventTimeZone = await TimeZone.findOne({
        where: {
          isDeleted: false,
          id: +inputData.body.TimeZoneId,
        },
        attributes: [
          'id',
          'location',
          'isDayLightSavingEnabled',
          'timeZoneOffsetInMinutes',
          'dayLightSavingTimeInMinutes',
          'timezone',
        ],
      });
      if (!eventTimeZone) {
        return done(null, { message: 'Provide a valid timezone' });
      }
      const craneRequestDetail = inputData.body;
      const loginUser = inputData.user;
      const projectDetails = await Project.getProjectAndSettings({
        isDeleted: false,
        id: +craneRequestDetail.ProjectId,
      });
      let startDate;
      let endDate;
      if (craneRequestDetail.recurrence) {
        startDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          craneRequestDetail.craneDeliveryStart,
          craneRequestDetail.startPicker,
          eventTimeZone.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
        endDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          craneRequestDetail.craneDeliveryEnd,
          craneRequestDetail.endPicker,
          eventTimeZone.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
      }
      if (craneRequestDetail.startPicker === craneRequestDetail.endPicker) {
        return done(null, { message: 'Delivery Start time and End time should not be the same' });
      }
      if (craneRequestDetail.startPicker > craneRequestDetail.endPicker) {
        return done(null, { message: 'Please enter From Time lesser than To Time' });
      }
      if (startDate || endDate) {
        if (projectDetails.ProjectSettings.deliveryWindowTime === 0) {
          if (craneRequestDetail.recurrence === 'Does Not Repeat') {
            return done(null, { message: 'Please enter Future Date/Time' });
          }
          return done(null, { message: 'Please enter Future Start or End Date/Time' });
        }
        return done(null, {
          message: `Bookings can not be submitted within ${projectDetails.ProjectSettings.deliveryWindowTime} ${projectDetails.ProjectSettings.deliveryWindowTimeUnit} prior to the event`,
        });
      }
      if (projectDetails && projectDetails.ProjectSettings) {
        this.checkInputDatas(inputData, async (checkResponse, checkError) => {
          if (checkError) {
            return done(null, checkError);
          }
          const memberDetails = await Member.getBy({
            UserId: loginUser.id,
            ProjectId: craneRequestDetail.ProjectId,
            isActive: true,
            isDeleted: false,
          });
          if (memberDetails) {
            let lastData = {};
            lastData = await CraneRequest.findOne({
              where: { ProjectId: +memberDetails.ProjectId, isDeleted: false },
              order: [['CraneRequestId', 'DESC']],
            });
            const deliveryRequestList = await DeliveryRequest.findOne({
              where: {
                ProjectId: +memberDetails.ProjectId,
                isDeleted: false,
                isAssociatedWithCraneRequest: true,
              },
              order: [['CraneRequestId', 'DESC']],
            });
            if (deliveryRequestList) {
              if (lastData) {
                if (deliveryRequestList.CraneRequestId > lastData.CraneRequestId) {
                  lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
                }
              } else {
                lastData = {};
                lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
              }
            }
            if (lastData) {
              const data = lastData.CraneRequestId;
              lastData.CraneRequestId = 0;
              lastData.CraneRequestId = data + 1;
            } else {
              lastData = {};
              lastData.CraneRequestId = 1;
            }
            let id = 0;
            const newValue = JSON.parse(JSON.stringify(lastData));
            if (
              newValue &&
              newValue.CraneRequestId !== null &&
              newValue.CraneRequestId !== undefined
            ) {
              id = newValue.CraneRequestId;
            }
            let craneRequestParam = {};
            const roleDetails = await Role.getBy('Project Admin');
            const accountRoleDetails = await Role.getBy('Account Admin');
            const range = momentRange.range(
              moment(craneRequestDetail.craneDeliveryStart),
              moment(craneRequestDetail.craneDeliveryEnd),
            );
            let totalDays = Array.from(range.by('day'));
            const eventsArray = [];
            if (craneRequestDetail.recurrence === 'Daily') {
              const startTime = craneRequestDetail.startPicker;
              const endTime = craneRequestDetail.endPicker;
              let dailyIndex = 0;
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                craneRequestDetail,
                inputData.user,
                'craneRequest',
                eventTimeZone.timezone,
              );
              while (dailyIndex < totalDays.length) {
                const data = totalDays[dailyIndex];
                if (
                  moment(data).isBetween(
                    moment(craneRequestDetail.craneDeliveryStart),
                    moment(craneRequestDetail.craneDeliveryEnd),
                    null,
                    '[]',
                  ) ||
                  moment(data).isSame(craneRequestDetail.craneDeliveryStart) ||
                  moment(data).isSame(craneRequestDetail.craneDeliveryEnd)
                ) {
                  id += 1;
                  const date = moment(data).format('MM/DD/YYYY');
                  const chosenTimezoneCraneDeliveryStart = moment.tz(
                    `${date} ${startTime}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  const chosenTimezoneCraneDeliveryEnd = moment.tz(
                    `${date} ${endTime}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
                    .clone()
                    .tz('UTC')
                    .format('YYYY-MM-DD HH:mm:ssZ');
                  const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
                    .clone()
                    .tz('UTC')
                    .format('YYYY-MM-DD HH:mm:ssZ');
                  craneRequestParam = {
                    description: craneRequestDetail.description,
                    isEscortNeeded: craneRequestDetail.isEscortNeeded,
                    LocationId: craneRequestDetail.LocationId,
                    additionalNotes: craneRequestDetail.additionalNotes,
                    CraneRequestId: id,
                    craneDeliveryStart,
                    craneDeliveryEnd,
                    ProjectId: craneRequestDetail.ProjectId,
                    createdBy: memberDetails.id,
                    isAssociatedWithDeliveryRequest:
                      craneRequestDetail.isAssociatedWithDeliveryRequest,
                    pickUpLocation: craneRequestDetail.pickUpLocation,
                    dropOffLocation: craneRequestDetail.dropOffLocation,
                    recurrenceId,
                  };
                  if (
                    memberDetails.RoleId === roleDetails.id ||
                    memberDetails.RoleId === accountRoleDetails.id ||
                    memberDetails.isAutoApproveEnabled ||
                    projectDetails.ProjectSettings.isAutoApprovalEnabled
                  ) {
                    craneRequestParam.status = 'Approved';
                    craneRequestParam.approvedBy = memberDetails.id;
                    craneRequestParam.approved_at = new Date();
                  }
                  eventsArray.push(craneRequestParam);
                  // eslint-disable-next-line no-const-assign
                  dailyIndex += +craneRequestDetail.repeatEveryCount;
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (craneRequestDetail.recurrence === 'Weekly') {
              const startTime = craneRequestDetail.startPicker;
              const endTime = craneRequestDetail.endPicker;
              const startDayWeek = moment(craneRequestDetail.craneDeliveryStart).startOf('week');
              const endDayWeek = moment(craneRequestDetail.craneDeliveryEnd).endOf('week');
              const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
              const totalDaysOfRecurrence = Array.from(range1.by('day'));
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                craneRequestDetail,
                inputData.user,
                'craneRequest',
                eventTimeZone.timezone,
              );
              totalDays = totalDaysOfRecurrence;
              let count;
              let weekIncrement;
              if (+craneRequestDetail.repeatEveryCount > 1) {
                count = +craneRequestDetail.repeatEveryCount - 1;
                weekIncrement = 7;
              } else {
                count = 1;
                weekIncrement = 0;
              }
              for (let indexba = 0; indexba < totalDays.length; indexba += weekIncrement * count) {
                const totalLength = indexba + 6;
                for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
                  const data = totalDays[indexb];
                  indexba += 1;
                  if (
                    data &&
                    !moment(data).isBefore(craneRequestDetail.craneDeliveryStart) &&
                    !moment(data).isAfter(craneRequestDetail.craneDeliveryEnd)
                  ) {
                    const day = moment(data).format('dddd');
                    const indexVal = craneRequestDetail.days.includes(day);
                    if (indexVal) {
                      id += 1;
                      const date = moment(data).format('MM/DD/YYYY');
                      const chosenTimezoneCraneDeliveryStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneCraneDeliveryEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      craneRequestParam = {
                        description: craneRequestDetail.description,
                        isEscortNeeded: craneRequestDetail.isEscortNeeded,
                        additionalNotes: craneRequestDetail.additionalNotes,
                        CraneRequestId: id,
                        craneDeliveryStart,
                        craneDeliveryEnd,
                        ProjectId: craneRequestDetail.ProjectId,
                        createdBy: memberDetails.id,
                        isAssociatedWithDeliveryRequest:
                          craneRequestDetail.isAssociatedWithDeliveryRequest,
                        pickUpLocation: craneRequestDetail.pickUpLocation,
                        dropOffLocation: craneRequestDetail.dropOffLocation,
                        recurrenceId,
                        LocationId: craneRequestDetail.LocationId,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        craneRequestParam.status = 'Approved';
                        craneRequestParam.approvedBy = memberDetails.id;
                        craneRequestParam.approved_at = new Date();
                      }
                      eventsArray.push(craneRequestParam);
                    }
                  }
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (craneRequestDetail.recurrence === 'Monthly') {
              const startTime = craneRequestDetail.startPicker;
              const endTime = craneRequestDetail.endPicker;
              const startMonth = moment(craneRequestDetail.craneDeliveryStart).startOf('month');
              const startMonthNumber = moment(startMonth).format('MM');
              const endMonth = moment(craneRequestDetail.craneDeliveryEnd).endOf('month');
              const endMonthNumber = moment(endMonth).format('MM');
              let startDate1 = moment(craneRequestDetail.craneDeliveryStart);
              const endDate1 = moment(craneRequestDetail.craneDeliveryEnd).endOf('month');
              const allMonthsInPeriod = [];
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                craneRequestDetail,
                inputData.user,
                'craneRequest',
                eventTimeZone.timezone,
              );
              while (startDate1.isBefore(endDate1)) {
                allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
                startDate1 = startDate1.add(1, 'month');
              }
              let currentMonthDates = [];
              let totalNumberOfMonths = endMonthNumber - startMonthNumber;
              if (totalNumberOfMonths < 0) {
                totalNumberOfMonths *= -1;
              }
              let k = 0;
              while (k < allMonthsInPeriod.length + 1) {
                currentMonthDates = Array.from(
                  { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                  (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                );
                if (craneRequestDetail.chosenDateOfMonth) {
                  const getDate = currentMonthDates.filter(
                    (value) => moment(value).format('DD') === craneRequestDetail.dateOfMonth,
                  );
                  if (getDate.length === 1) {
                    if (
                      moment(getDate[0]).isBetween(
                        moment(craneRequestDetail.craneDeliveryStart),
                        moment(craneRequestDetail.craneDeliveryEnd),
                        null,
                        '[]',
                      ) ||
                      moment(getDate[0]).isSame(craneRequestDetail.craneDeliveryStart) ||
                      moment(getDate[0]).isSame(craneRequestDetail.craneDeliveryEnd)
                    ) {
                      id += 1;
                      const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                      const chosenTimezoneCraneDeliveryStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneCraneDeliveryEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      craneRequestParam = {
                        description: craneRequestDetail.description,
                        isEscortNeeded: craneRequestDetail.isEscortNeeded,
                        additionalNotes: craneRequestDetail.additionalNotes,
                        CraneRequestId: id,
                        craneDeliveryStart,
                        craneDeliveryEnd,
                        ProjectId: craneRequestDetail.ProjectId,
                        createdBy: memberDetails.id,
                        isAssociatedWithDeliveryRequest:
                          craneRequestDetail.isAssociatedWithDeliveryRequest,
                        pickUpLocation: craneRequestDetail.pickUpLocation,
                        dropOffLocation: craneRequestDetail.dropOffLocation,
                        recurrenceId,
                        LocationId: craneRequestDetail.LocationId,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        craneRequestParam.status = 'Approved';
                        craneRequestParam.approvedBy = memberDetails.id;
                        craneRequestParam.approved_at = new Date();
                      }
                      eventsArray.push(craneRequestParam);
                    }
                  }
                } else if (allMonthsInPeriod[k]) {
                  const dayOfMonth = craneRequestDetail.monthlyRepeatType;
                  const week = dayOfMonth.split(' ')[0].toLowerCase();
                  const day = dayOfMonth.split(' ')[1].toLowerCase();
                  const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM')
                    .startOf('month')
                    .day(day);
                  const getAllDays = [];
                  if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                  const month = chosenDay.month();
                  while (month === chosenDay.month()) {
                    getAllDays.push(chosenDay.toString());
                    chosenDay.add(7, 'd');
                  }
                  let i = 0;
                  if (week === 'second') {
                    i += 1;
                  } else if (week === 'third') {
                    i += 2;
                  } else if (week === 'fourth') {
                    i += 3;
                  } else if (week === 'last') {
                    i = getAllDays.length - 1;
                  }
                  const finalDay = getAllDays[i];
                  if (
                    moment(finalDay).isBetween(
                      moment(craneRequestDetail.craneDeliveryStart),
                      moment(craneRequestDetail.craneDeliveryEnd),
                      null,
                      '[]',
                    ) ||
                    moment(finalDay).isSame(craneRequestDetail.craneDeliveryStart) ||
                    moment(finalDay).isSame(craneRequestDetail.craneDeliveryEnd)
                  ) {
                    id += 1;
                    const date = moment(finalDay).format('MM/DD/YYYY');
                    const chosenTimezoneCraneDeliveryStart = moment.tz(
                      `${date} ${startTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezoneCraneDeliveryEnd = moment.tz(
                      `${date} ${endTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ');
                    const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ');
                    craneRequestParam = {
                      description: craneRequestDetail.description,
                      isEscortNeeded: craneRequestDetail.isEscortNeeded,
                      additionalNotes: craneRequestDetail.additionalNotes,
                      CraneRequestId: id,
                      craneDeliveryStart,
                      craneDeliveryEnd,
                      ProjectId: craneRequestDetail.ProjectId,
                      createdBy: memberDetails.id,
                      isAssociatedWithDeliveryRequest:
                        craneRequestDetail.isAssociatedWithDeliveryRequest,
                      pickUpLocation: craneRequestDetail.pickUpLocation,
                      dropOffLocation: craneRequestDetail.dropOffLocation,
                      recurrenceId,
                      LocationId: craneRequestDetail.LocationId,
                    };
                    if (
                      memberDetails.RoleId === roleDetails.id ||
                      memberDetails.RoleId === accountRoleDetails.id ||
                      memberDetails.isAutoApproveEnabled ||
                      projectDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      craneRequestParam.status = 'Approved';
                      craneRequestParam.approvedBy = memberDetails.id;
                      craneRequestParam.approved_at = new Date();
                    }
                    eventsArray.push(craneRequestParam);
                  }
                }
                k += +craneRequestDetail.repeatEveryCount;
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (craneRequestDetail.recurrence === 'Yearly') {
              const startTime = craneRequestDetail.startPicker;
              const endTime = craneRequestDetail.endPicker;
              const startMonth = moment(craneRequestDetail.craneDeliveryStart).startOf('month');
              const startMonthNumber = moment(startMonth).format('MM');
              const endMonth = moment(craneRequestDetail.craneDeliveryEnd).endOf('month');
              const endMonthNumber = moment(endMonth).format('MM');
              let startDate1 = moment(craneRequestDetail.craneDeliveryStart);
              const endDate1 = moment(craneRequestDetail.craneDeliveryEnd).endOf('month');
              const allMonthsInPeriod = [];
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                craneRequestDetail,
                inputData.user,
                'craneRequest',
                eventTimeZone.timezone,
              );
              while (startDate1.isBefore(endDate1)) {
                allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
                startDate1 = startDate1.add(12, 'month');
              }
              let currentMonthDates = [];
              let totalNumberOfMonths = endMonthNumber - startMonthNumber;
              if (totalNumberOfMonths < 0) {
                totalNumberOfMonths *= -1;
              }
              for (let k = 0; k < allMonthsInPeriod.length + 1; k += 1) {
                currentMonthDates = Array.from(
                  { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                  (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                );
                if (craneRequestDetail.chosenDateOfMonth) {
                  const getDate = currentMonthDates.filter(
                    (value) => moment(value).format('DD') === craneRequestDetail.dateOfMonth,
                  );
                  if (getDate.length === 1) {
                    if (
                      moment(getDate[0]).isBetween(
                        moment(craneRequestDetail.craneDeliveryStart),
                        moment(craneRequestDetail.craneDeliveryEnd),
                        null,
                        '[]',
                      ) ||
                      moment(getDate[0]).isSame(craneRequestDetail.craneDeliveryStart) ||
                      moment(getDate[0]).isSame(craneRequestDetail.craneDeliveryEnd)
                    ) {
                      id += 1;
                      const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                      const chosenTimezoneCraneDeliveryStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneCraneDeliveryEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      craneRequestParam = {
                        description: craneRequestDetail.description,
                        isEscortNeeded: craneRequestDetail.isEscortNeeded,
                        additionalNotes: craneRequestDetail.additionalNotes,
                        CraneRequestId: id,
                        craneDeliveryStart,
                        craneDeliveryEnd,
                        ProjectId: craneRequestDetail.ProjectId,
                        createdBy: memberDetails.id,
                        isAssociatedWithDeliveryRequest:
                          craneRequestDetail.isAssociatedWithDeliveryRequest,
                        pickUpLocation: craneRequestDetail.pickUpLocation,
                        dropOffLocation: craneRequestDetail.dropOffLocation,
                        recurrenceId,
                        LocationId: craneRequestDetail.LocationId,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        craneRequestParam.status = 'Approved';
                        craneRequestParam.approvedBy = memberDetails.id;
                        craneRequestParam.approved_at = new Date();
                      }
                      eventsArray.push(craneRequestParam);
                    }
                  }
                } else if (allMonthsInPeriod[k]) {
                  const dayOfMonth = craneRequestDetail.monthlyRepeatType;
                  const week = dayOfMonth.split(' ')[0].toLowerCase();
                  const day = dayOfMonth.split(' ')[1].toLowerCase();
                  const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM')
                    .startOf('month')
                    .day(day);
                  const getAllDays = [];
                  if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                  const month = chosenDay.month();
                  while (month === chosenDay.month()) {
                    getAllDays.push(chosenDay.toString());
                    chosenDay.add(7, 'd');
                  }
                  let i = 0;
                  if (week === 'second') {
                    i += 1;
                  } else if (week === 'third') {
                    i += 2;
                  } else if (week === 'fourth') {
                    i += 3;
                  } else if (week === 'last') {
                    i = getAllDays.length - 1;
                  }
                  const finalDay = getAllDays[i];
                  if (
                    moment(finalDay).isBetween(
                      moment(craneRequestDetail.craneDeliveryStart),
                      moment(craneRequestDetail.craneDeliveryEnd),
                      null,
                      '[]',
                    ) ||
                    moment(finalDay).isSame(craneRequestDetail.craneDeliveryStart) ||
                    moment(finalDay).isSame(craneRequestDetail.craneDeliveryEnd)
                  ) {
                    id += 1;
                    const date = moment(finalDay).format('MM/DD/YYYY');
                    const chosenTimezoneCraneDeliveryStart = moment.tz(
                      `${date} ${startTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezoneCraneDeliveryEnd = moment.tz(
                      `${date} ${endTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ');
                    const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ');
                    craneRequestParam = {
                      description: craneRequestDetail.description,
                      isEscortNeeded: craneRequestDetail.isEscortNeeded,
                      additionalNotes: craneRequestDetail.additionalNotes,
                      CraneRequestId: id,
                      craneDeliveryStart,
                      craneDeliveryEnd,
                      ProjectId: craneRequestDetail.ProjectId,
                      createdBy: memberDetails.id,
                      isAssociatedWithDeliveryRequest:
                        craneRequestDetail.isAssociatedWithDeliveryRequest,
                      pickUpLocation: craneRequestDetail.pickUpLocation,
                      dropOffLocation: craneRequestDetail.dropOffLocation,
                      recurrenceId,
                      LocationId: craneRequestDetail.LocationId,
                    };
                    if (
                      memberDetails.RoleId === roleDetails.id ||
                      memberDetails.RoleId === accountRoleDetails.id ||
                      memberDetails.isAutoApproveEnabled ||
                      projectDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      craneRequestParam.status = 'Approved';
                      craneRequestParam.approvedBy = memberDetails.id;
                      craneRequestParam.approved_at = new Date();
                    }
                    eventsArray.push(craneRequestParam);
                  }
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (craneRequestDetail.recurrence === 'Does Not Repeat') {
              id += 1;
              const chosenTimezoneCraneDeliveryStart = moment.tz(
                `${craneRequestDetail.craneDeliveryStart} ${craneRequestDetail.startPicker}`,
                'YYYY MM DD 00:00:00 HH:mm',
                eventTimeZone.timezone,
              );
              const chosenTimezoneCraneDeliveryEnd = moment.tz(
                `${craneRequestDetail.craneDeliveryEnd} ${craneRequestDetail.endPicker}`,
                'YYYY MM DD 00:00:00 HH:mm',
                eventTimeZone.timezone,
              );
              const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');
              const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                craneRequestDetail,
                inputData.user,
                'craneRequest',
                eventTimeZone.timezone,
              );
              craneRequestParam = {
                description: craneRequestDetail.description,
                isEscortNeeded: craneRequestDetail.isEscortNeeded,
                additionalNotes: craneRequestDetail.additionalNotes,
                CraneRequestId: id,
                craneDeliveryStart,
                craneDeliveryEnd,
                ProjectId: craneRequestDetail.ProjectId,
                createdBy: memberDetails.id,
                isAssociatedWithDeliveryRequest: craneRequestDetail.isAssociatedWithDeliveryRequest,
                pickUpLocation: craneRequestDetail.pickUpLocation,
                dropOffLocation: craneRequestDetail.dropOffLocation,
                recurrenceId,
                LocationId: craneRequestDetail.LocationId,
              };
              if (
                memberDetails.RoleId === roleDetails.id ||
                memberDetails.RoleId === accountRoleDetails.id ||
                memberDetails.isAutoApproveEnabled ||
                projectDetails.ProjectSettings.isAutoApprovalEnabled
              ) {
                craneRequestParam.status = 'Approved';
                craneRequestParam.approvedBy = memberDetails.id;
                craneRequestParam.approved_at = new Date();
              }
              eventsArray.push(craneRequestParam);
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            let newCraneRequestData = {};
            if (eventsArray.length > 0) {
              for (let i = 0; i < eventsArray.length; i += 1) {
                newCraneRequestData = await CraneRequest.createInstance(eventsArray[i]);
                const { companies, responsiblePersons, definableFeatureOfWorks } =
                  craneRequestDetail;
                const equipments = [craneRequestDetail.EquipmentId];
                const updateParam = {
                  CraneRequestId: newCraneRequestData.id,
                  CraneRequestCode: newCraneRequestData.CraneRequestId,
                  ProjectId: craneRequestDetail.ProjectId,
                };
                companies.forEach(async (element) => {
                  const companyParam = updateParam;
                  companyParam.CompanyId = element;
                  await CraneRequestCompany.createInstance(companyParam);
                });
                equipments.forEach(async (element) => {
                  const equipmentParam = updateParam;
                  equipmentParam.EquipmentId = element;
                  await CraneRequestEquipment.createInstance(equipmentParam);
                });
                responsiblePersons.forEach(async (element) => {
                  const memberParam = updateParam;
                  memberParam.MemberId = element;
                  await CraneRequestResponsiblePerson.createInstance(memberParam);
                });
                definableFeatureOfWorks.forEach(async (element) => {
                  const defineParam = updateParam;
                  defineParam.DeliverDefineWorkId = element;
                  await CraneRequestDefinableFeatureOfWork.createInstance(defineParam);
                });
                const history = {
                  CraneRequestId: newCraneRequestData.id,
                  MemberId: memberDetails.id,
                  type: 'create',
                  description: `${loginUser.firstName} ${loginUser.lastName} Created Crane Booking, ${craneRequestDetail.description}.`,
                };
                const notification = history;
                notification.ProjectId = eventsArray[i].ProjectId;
                notification.title = 'Crane Booking Creation';
                await CraneRequestHistory.createInstance(history);
                if (newCraneRequestData.status === 'Approved') {
                  const object = {
                    ProjectId: craneRequestDetail.ProjectId,
                    MemberId: memberDetails.id,
                    CraneRequestId: newCraneRequestData.id,
                    isDeleted: false,
                    type: 'approved',
                    description: `${loginUser.firstName} ${loginUser.lastName} Approved Crane Booking, ${craneRequestDetail.description}.`,
                  };
                  await CraneRequestHistory.createInstance(object);
                }
              }
            }
            if (
              Object.keys(newCraneRequestData).length > 0 &&
              typeof newCraneRequestData === 'object'
            ) {
              const { responsiblePersons } = craneRequestDetail;
              const locationChosen = await Locations.findOne({
                where: {
                  ProjectId: craneRequestDetail.ProjectId,
                  id: craneRequestDetail.LocationId,
                },
              });
              const history = {
                CraneRequestId: newCraneRequestData.id,
                MemberId: memberDetails.id,
                type: 'create',
                description: `${loginUser.firstName} ${loginUser.lastName} Created Crane Booking, ${craneRequestDetail.description}.`,
                locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Created Crane Booking, ${craneRequestDetail.description}. Location: ${locationChosen.locationPath}.`,
                memberData: [],
              };
              const notification = history;
              notification.ProjectId = craneRequestDetail.ProjectId;
              notification.LocationId = craneRequestDetail.LocationId;
              notification.title = 'Crane Booking Creation';
              notification.isDeliveryRequest = false;
              notification.requestType = 'craneRequest';
              notification.recurrenceType = `${craneRequestDetail.recurrence} From ${moment(
                craneRequestDetail.craneDeliveryStart,
              ).format('MM/DD/YYYY')} to ${moment(craneRequestDetail.craneDeliveryEnd).format(
                'MM/DD/YYYY',
              )}`;
              const newNotification = await Notification.createInstance(notification);
              const memberLocationPreference = await LocationNotificationPreferences.findAll({
                where: {
                  ProjectId: craneRequestDetail.ProjectId,
                  LocationId: craneRequestDetail.LocationId,
                  follow: true,
                },
                include: [
                  {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                      [Op.and]: [
                        {
                          id: { [Op.ne]: memberDetails.id },
                        },
                      ],
                    },
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                  },
                ],
              });
              const locationFollowMembers = [];
              memberLocationPreference.forEach(async (element) => {
                locationFollowMembers.push(element.Member.id);
              });
              const personData = await CraneRequestResponsiblePerson.findAll({
                where: { CraneRequestId: newCraneRequestData.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    { ProjectId: craneRequestDetail.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.in]: responsiblePersons } },
                    { id: { [Op.ne]: newNotification.MemberId } },
                    { id: { [Op.notIn]: locationFollowMembers } },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                  },
                ],
                attributes: ['id', 'RoleId'],
              });

              if (memberLocationPreference && memberLocationPreference.length > 0) {
                await pushNotification.sendMemberLocationPreferencePushNotificationForCrane(
                  memberLocationPreference,
                  newCraneRequestData.CraneRequestId,
                  history.locationFollowDescription,
                  newCraneRequestData.requestType,
                  newCraneRequestData.ProjectId,
                  newCraneRequestData.id,
                  3,
                );
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  craneRequestDetail.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  3,
                );
              }
              history.memberData = personData;
              history.adminData = adminData;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = craneRequestDetail.ProjectId;
              history.projectName = projectDetails.projectName;
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                [],
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberDetails,
                loginUser,
                3,
                'created a',
                'Crane Request',
                `crane Booking (${newCraneRequestData.CraneRequestId} - ${newCraneRequestData.description})`,
                newCraneRequestData.CraneRequestId,
              );
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: craneRequestDetail.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 3,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              await pushNotification.sendPushNotificationForCrane(
                history,
                3,
                craneRequestDetail.ProjectId,
              );
              history.CraneRequestId = newCraneRequestData.CraneRequestId;
              await this.sendEmailNotificationToUser(
                history,
                memberDetails,
                loginUser,
                newCraneRequestData,
                craneRequestDetail,
                memberLocationPreference,
              );
              const memberLocationPreferenceNotify = await LocationNotificationPreferences.findAll({
                where: {
                  ProjectId: craneRequestDetail.ProjectId,
                  LocationId: craneRequestDetail.LocationId,
                  follow: true,
                },
                include: [
                  {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                      [Op.and]: [
                        {
                          id: { [Op.ne]: memberDetails.id },
                        }
                      ],
                    },
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                  },
                ],
              });
              if (+memberDetails.RoleId === 4 || +memberDetails.RoleId === 3) {
                const userEmails = await this.getMemberDetailData(
                  history,
                  memberLocationPreference,
                );
                if (userEmails.length > 0) {
                  userEmails.forEach(async (element) => {
                    if (+element.RoleId === 2) {
                      let name;
                      if (!element.firstName) {
                        name = 'user';
                      } else {
                        name = `${element.firstName} ${element.lastName}`;
                      }
                      const memberRole = await Role.findOne({
                        where: {
                          id: memberDetails.RoleId,
                          isDeleted: false,
                        },
                      });
                      const mailPayload = {
                        name,
                        email: element.email,
                        content: `We would like to inform you that 
                          ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has created a crane booking ${newCraneRequestData.CraneRequestId} and waiting for your approval.Kindly review the booking and update the status.`,
                      };
                      const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
                        where: {
                          MemberId: +element.MemberId,
                          ProjectId: +craneRequestDetail.ProjectId,
                          LocationId: +craneRequestDetail.LocationId,
                          isDeleted: false,
                          // follow: true,
                        },
                      });
                      if (isMemberFollowLocation) {
                        const memberNotification = await NotificationPreference.findOne({
                          where: {
                            MemberId: +element.MemberId,
                            ProjectId: +craneRequestDetail.ProjectId,
                            isDeleted: false,
                          },
                          include: [
                            {
                              association: 'NotificationPreferenceItem',
                              where: {
                                id: 8,
                                isDeleted: false,
                              },
                            },
                          ],
                        });

                        if (memberNotification && memberNotification.instant) {
                          await MAILER.sendMail(
                            mailPayload,
                            'notifyPAForApproval',
                            `Crane Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                            `Crane Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                            async (info, err) => {
                              console.log(info, err);
                            },
                          );
                        }
                        if (memberNotification && memberNotification.dailyDigest) {
                          await this.createDailyDigestDataApproval(
                            +memberDetails.RoleId,
                            +element.MemberId,
                            +craneRequestDetail.ProjectId,
                            +craneRequestDetail.ParentCompanyId,
                            loginUser,
                            'created a',
                            'Crane Request',
                            `crane Booking (${newCraneRequestData.CraneRequestId} - ${newCraneRequestData.description})`,
                            'and waiting for your approval',
                            newCraneRequestData.CraneRequestId,
                          );
                        }
                      }
                    }
                  });
                  if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                    history.memberData.push(...memberLocationPreferenceNotify);
                  }
                  return done(history, false);
                }
                if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                  history.memberData.push(...memberLocationPreferenceNotify);
                }
                return done(history, false);
              }
              if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                history.memberData.push(...memberLocationPreferenceNotify);
              }
              return done(history, false);
            }
            return done(null, {
              message: 'Bookings will not be created for the scheduled date/time',
            });
          }
          return done(null, {
            message: 'You are not allowed create Crane Booking for this project.',
          });
        });
      } else {
        return done(null, { message: 'Project does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async compareDeliveryDateWithDeliveryWindowDate(
    dateStr,
    timeStr,
    timezoneStr,
    deliveryWindowTime,
    deliveryWindowTimeUnit,
  ) {
    const datetimeStr = `${moment(dateStr).format('YYYY-MM-DD')}T${timeStr}`;
    const datetime = moment.tz(datetimeStr, timezoneStr);
    const currentDatetime = moment
      .tz(timezoneStr)
      .add(deliveryWindowTime, deliveryWindowTimeUnit)
      .startOf('minute');
    return datetime.isSameOrBefore(currentDatetime);
  },
  async checkInputDatas(inputData, done) {
    await this.getDynamicModel(inputData);
    const craneRequestData = inputData.body;
    const { companies, responsiblePersons, definableFeatureOfWorks } = craneRequestData;
    const equipments = [craneRequestData.EquipmentId];
    const inputProjectId = craneRequestData.ProjectId;
    const memberList = await Member.count({
      where: { id: { [Op.in]: responsiblePersons }, ProjectId: inputProjectId, isDeleted: false },
    });
    const equipmentList = await Equipments.count({
      where: { id: { [Op.in]: equipments }, ProjectId: inputProjectId, isDeleted: false },
    });
    const defineList = await DeliverDefineWork.count({
      where: {
        id: { [Op.in]: definableFeatureOfWorks },
        ProjectId: inputProjectId,
        isDeleted: false,
      },
    });
    const companyList = await Company.count({
      where: {
        [Op.or]: [
          {
            id: { [Op.in]: companies },
            ProjectId: +inputProjectId,
            isDeleted: false,
          },
          {
            id: {
              [Op.in]: companies,
            },
            isParent: true,
            ParentCompanyId: +craneRequestData.ParentCompanyId,
            isDeleted: false,
          },
        ],
      },
    });
    if (
      craneRequestData.responsiblePersons &&
      craneRequestData.responsiblePersons.length > 0 &&
      memberList !== responsiblePersons.length
    ) {
      return done(null, { message: 'Some Member is not in the project' });
    }
    if (craneRequestData.EquipmentId && equipmentList !== equipments.length) {
      return done(null, { message: 'Mentioned Equipment is not in the project' });
    }
    if (
      craneRequestData.companies &&
      craneRequestData.companies.length > 0 &&
      companyList !== companies.length
    ) {
      return done(null, { message: 'Some Company is not in the project' });
    }
    if (
      craneRequestData.definableFeatureOfWorks &&
      craneRequestData.definableFeatureOfWorks.length > 0 &&
      defineList !== definableFeatureOfWorks.length
    ) {
      return done(null, { message: 'Some Definable Feature of Work is not in the project' });
    }
    return done(true, false);
  },
  async updateValues(condition, done) {
    try {
      await CraneRequestCompany.update({ isDeleted: true }, { where: condition });
      await CraneRequestResponsiblePerson.update({ isDeleted: true }, { where: condition });
      await CraneRequestEquipment.update({ isDeleted: true }, { where: condition });
      await CraneRequestDefinableFeatureOfWork.update({ isDeleted: true }, { where: condition });
      done({ status: 'ok' }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async editCraneRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const craneRequestData = inputData.body;
      const loginUser = inputData.user;
      const { recurrenceId } = craneRequestData;
      let editSeriesRequests;
      let newRecurrenceId;
      let previousRecordInSeries;
      const projectSettingDetails = await Project.getProjectAndSettings({
        isDeleted: false,
        id: +craneRequestData.ProjectId,
      });
      if (projectSettingDetails) {
        const requestData = await CraneRequest.getSingleCraneRequestData({
          id: craneRequestData.id,
        });
        if (craneRequestData.seriesOption === 1) {
          const requestArray = [];
          requestArray.push({
            ProjectId: craneRequestData.ProjectId,
            craneDeliveryStart: craneRequestData.craneDeliveryStart,
            craneDeliveryEnd: craneRequestData.craneDeliveryEnd,
            id: craneRequestData.id,
          });
          const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
            requestArray,
            projectSettingDetails,
            'edit',
          );
          if (isOverlapping && isOverlapping.error) {
            return done(null, {
              message: isOverlapping.message,
            });
          }
        }
        if (craneRequestData.seriesOption === 2 || craneRequestData.seriesOption === 3) {
          let requestSeries = [];
          if (craneRequestData.seriesOption === 2) {
            requestSeries = await CraneRequest.findAll({
              where: [
                Sequelize.and({
                  recurrenceId,
                  id: {
                    [Op.gte]: craneRequestData.id,
                  },
                }),
              ],
            });
          }
          if (craneRequestData.seriesOption === 3) {
            requestSeries = await CraneRequest.findAll({
              where: [
                Sequelize.and({
                  recurrenceId,
                  craneDeliveryStart: {
                    [Op.gte]: moment
                      .tz(craneRequestData.timeZone)
                      .utc()
                      .format('YYYY-MM-DD HH:mm:ssZ'),
                  },
                }),
              ],
            });
          }
          const requestArray = [];
          for (let i = 0; i < requestSeries.length; i += 1) {
            const deliveryStartDate = await concreteRequestService.convertTimezoneToUtc(
              moment
                .utc(requestSeries[i].craneDeliveryStart)
                .tz(craneRequestData.timezone)
                .format('MM/DD/YYYY'),
              craneRequestData.timezone,
              craneRequestData.deliveryStartTime,
            );
            const deliveryEndDate = await concreteRequestService.convertTimezoneToUtc(
              moment
                .utc(requestSeries[i].craneDeliveryEnd)
                .tz(craneRequestData.timezone)
                .format('MM/DD/YYYY'),
              craneRequestData.timezone,
              craneRequestData.deliveryEndTime,
            );
            requestArray.push({
              ProjectId: craneRequestData.ProjectId,
              craneDeliveryStart: !moment(deliveryStartDate).isSame(
                moment(requestSeries[i].craneDeliveryStart),
              )
                ? deliveryStartDate
                : requestSeries[i].craneDeliveryStart,
              craneDeliveryEnd: !moment(deliveryEndDate).isSame(
                moment(requestSeries[i].craneDeliveryEnd),
              )
                ? deliveryEndDate
                : requestSeries[i].craneDeliveryEnd,
              id: requestSeries[i].id,
            });
          }
          const utcRecurrenceEndDate = requestData.recurrence.recurrenceEndDate;
          const existingRecurrenceEndDate = moment(utcRecurrenceEndDate)
            .tz(craneRequestData.timezone)
            .format('YYYY-MM-DD');
          const newRecurrenceEndDate = craneRequestData.recurrenceEndDate;
          if (!moment(existingRecurrenceEndDate).isSame(moment(newRecurrenceEndDate))) {
            const startDate = moment(existingRecurrenceEndDate).add(1, 'day');
            const endDate = moment(newRecurrenceEndDate);
            for (let date = startDate; date.isSameOrBefore(endDate); date.add(1, 'day')) {
              requestArray.push({
                ProjectId: craneRequestData.ProjectId,
                craneDeliveryStart: await concreteRequestService.convertTimezoneToUtc(
                  moment(date).format('MM/DD/YYYY'),
                  craneRequestData.timezone,
                  craneRequestData.deliveryStartTime,
                ),
                craneDeliveryEnd: await concreteRequestService.convertTimezoneToUtc(
                  moment(date).format('MM/DD/YYYY'),
                  craneRequestData.timezone,
                  craneRequestData.deliveryEndTime,
                ),
              });
            }
            if (requestArray.length > 0) {
              const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                requestArray,
                projectSettingDetails,
                'edit',
              );
              if (isOverlapping && isOverlapping.error) {
                return done(null, {
                  message: isOverlapping.message,
                });
              }
            }
          } else if (requestArray.length > 0) {
            const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
              requestArray,
              projectSettingDetails,
              'edit',
            );
            if (isOverlapping && isOverlapping.error) {
              return done(null, {
                message: isOverlapping.message,
              });
            }
          }
        }
      }
      // This event
      if (craneRequestData.seriesOption === 1) {
        editSeriesRequests = await CraneRequest.findAll({
          where: [
            Sequelize.and({
              id: craneRequestData.id,
            }),
          ],
        });
        if (editSeriesRequests && editSeriesRequests[0] && craneRequestData.recurrenceId) {
          const previousRecordInThisEventSeries = await CraneRequest.findAll({
            where: [
              Sequelize.and({
                recurrenceId,
                id: {
                  [Op.lt]: craneRequestData.id,
                },
              }),
            ],
            order: [['id', 'DESC']],
          });
          const NextSeriesLastRecord = await CraneRequest.findAll({
            where: [
              Sequelize.and({
                recurrenceId,
                id: {
                  [Op.gt]: craneRequestData.id,
                },
              }),
            ],
            order: [['id', 'DESC']],
          });
          if (
            ((NextSeriesLastRecord && NextSeriesLastRecord.length > 0) ||
              (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0)) &&
            !(
              NextSeriesLastRecord &&
              NextSeriesLastRecord.length > 0 &&
              previousRecordInThisEventSeries &&
              previousRecordInThisEventSeries.length > 0
            )
          ) {
            if (NextSeriesLastRecord && NextSeriesLastRecord.length > 0) {
              const chosenTimezoneDeliveryStart = moment.tz(
                `${craneRequestData.nextSeriesRecurrenceStartDate}  '00:00'`,
                'YYYY-MM-DD HH:mm',
                craneRequestData.timezone,
              );
              const utcDate = chosenTimezoneDeliveryStart
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');
              await RequestRecurrenceSeries.update(
                {
                  recurrenceStartDate: utcDate,
                },
                {
                  where: {
                    id: NextSeriesLastRecord[0].recurrenceId,
                  },
                },
              );
            }
            if (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0) {
              // const chosenTimezoneDeliveryStart = moment.tz(
              //   `${craneRequestData.previousSeriesRecurrenceEndDate}  '00:00'`,
              //   'YYYY-MM-DD HH:mm',
              //   craneRequestData.timezone,
              // );
              // const utcDate = chosenTimezoneDeliveryStart
              //   .clone()
              //   .tz('UTC')
              //   .format('YYYY-MM-DD HH:mm:ssZ');
              await RequestRecurrenceSeries.update(
                {
                  recurrenceEndDate: previousRecordInThisEventSeries[0].craneDeliveryStart,
                },
                {
                  where: {
                    id: previousRecordInThisEventSeries[0].recurrenceId,
                  },
                },
              );
            }
          }
        }
      }
      // This and all following events
      if (craneRequestData.seriesOption === 2) {
        editSeriesRequests = await CraneRequest.findAll({
          where: [
            Sequelize.and({
              recurrenceId,
              id: {
                [Op.gte]: craneRequestData.id,
              },
            }),
          ],
        });
        previousRecordInSeries = await CraneRequest.findOne({
          where: [
            Sequelize.and({
              recurrenceId,
              id: {
                [Op.lt]: craneRequestData.id,
              },
            }),
          ],
          order: [['id', 'DESC']],
        });
      }
      // All events in the series
      if (craneRequestData.seriesOption === 3) {
        editSeriesRequests = await CraneRequest.findAll({
          where: [
            Sequelize.and({
              recurrenceId,
              craneDeliveryStart: {
                [Op.gte]: moment.tz(craneRequestData.timeZone).utc().format('YYYY-MM-DD HH:mm:ssZ'),
              },
            }),
          ],
        });
      }
      if (editSeriesRequests && editSeriesRequests[0] && editSeriesRequests.length > 0) {
        const requestData = await CraneRequest.getSingleCraneRequestData({
          id: editSeriesRequests[0].id,
        });
        if (requestData && requestData.recurrence) {
          requestData.recurrence.ParentCompanyId = craneRequestData.ParentCompanyId;
          requestData.recurrence.ProjectId = craneRequestData.ProjectId;
          if (craneRequestData.seriesOption === 1) {
            requestData.recurrence.craneDeliveryStart = craneRequestData.recurrenceSeriesStartDate;
            requestData.recurrence.craneDeliveryEnd = craneRequestData.recurrenceSeriesEndDate;
          }
          if (craneRequestData.seriesOption === 2) {
            requestData.recurrence.craneDeliveryStart = craneRequestData.recurrenceSeriesStartDate;
            requestData.recurrence.craneDeliveryEnd = craneRequestData.recurrenceEndDate;
          }
          if (craneRequestData.seriesOption === 2 && previousRecordInSeries) {
            newRecurrenceId = await concreteRequestService.insertRecurrenceSeries(
              requestData.recurrence,
              loginUser,
              requestData.requestType,
              craneRequestData.timezone,
            );
          }
        }
        if (craneRequestData.seriesOption === 2 || craneRequestData.seriesOption === 3) {
          const utcRecurrenceEndDate = requestData.recurrence.recurrenceEndDate;
          let existingRecurrenceEndDate = moment(utcRecurrenceEndDate)
            .tz(craneRequestData.timezone)
            .format('YYYY-MM-DD');
          const newRecurrenceEndDate = craneRequestData.recurrenceEndDate;
          if (!moment(existingRecurrenceEndDate).isSame(moment(newRecurrenceEndDate))) {
            const dates = [];
            const chosenTimezoneDeliveryStart = moment.tz(
              `${craneRequestData.recurrenceEndDate}  '00:00'`,
              'YYYY-MM-DD HH:mm',
              craneRequestData.timezone,
            );
            const utcDate = chosenTimezoneDeliveryStart
              .clone()
              .tz('UTC')
              .format('YYYY-MM-DD HH:mm:ssZ');
            await RequestRecurrenceSeries.update(
              {
                recurrenceEndDate: utcDate,
              },
              {
                where: {
                  id: craneRequestData.recurrenceId,
                },
              },
            );
            while (moment(existingRecurrenceEndDate).isBefore(moment(newRecurrenceEndDate))) {
              existingRecurrenceEndDate = moment(existingRecurrenceEndDate).add(1, 'day');
              dates.push(moment(existingRecurrenceEndDate).format('MM/DD/YYYY'));
            }
            await this.createCopyofCraneRequest(
              requestData,
              craneRequestData,
              dates,
              loginUser,
              newRecurrenceId || craneRequestData.recurrenceId,
            );
          }
        }
        if (craneRequestData.seriesOption === 2 && previousRecordInSeries) {
          // const chosenTimezoneDeliveryStart = moment.tz(
          //   `${craneRequestData.previousSeriesRecurrenceEndDate}  '00:00'`,
          //   'YYYY-MM-DD HH:mm',
          //   craneRequestData.timezone,
          // );
          // const utcDate = chosenTimezoneDeliveryStart
          //   .clone()
          //   .tz('UTC')
          //   .format('YYYY-MM-DD HH:mm:ssZ');
          await RequestRecurrenceSeries.update(
            {
              recurrenceEndDate: previousRecordInSeries.craneDeliveryStart,
            },
            {
              where: {
                id: previousRecordInSeries.recurrenceId,
              },
            },
          );
        }
      }
      for (let indexLoop = 0; indexLoop < editSeriesRequests.length; indexLoop += 1) {
        const seriesData = editSeriesRequests[indexLoop];
        const isCraneRequestExists = await CraneRequest.findOne({
          where: [
            Sequelize.and({
              id: seriesData.id,
            }),
          ],
        });
        if (!isCraneRequestExists) {
          return done(null, { message: 'Crane Booking id is not available' });
        }
        const existsCraneRequest = await CraneRequest.getSingleCraneRequestData({
          id: +seriesData.id,
        });
        this.checkInputDatas(inputData, async (checkResponse, checkError) => {
          if (checkError) {
            return done(null, checkError);
          }
          const memberData = await Member.getBy({
            UserId: loginUser.id,
            ProjectId: craneRequestData.ProjectId,
          });
          const history = {
            CraneRequestId: isCraneRequestExists.id,
            MemberId: memberData.id,
            type: 'edit',
            description: `${loginUser.firstName} ${loginUser.lastName} Edited this Crane Booking.`,
          };
          const notification = history;
          const craneRequestParam = {
            description: craneRequestData.description,
            isEscortNeeded: craneRequestData.isEscortNeeded,
            additionalNotes: craneRequestData.additionalNotes,
            isAssociatedWithDeliveryRequest: craneRequestData.isAssociatedWithDeliveryRequest,
            pickUpLocation: craneRequestData.pickUpLocation,
            dropOffLocation: craneRequestData.dropOffLocation,
            recurrenceId: craneRequestData.seriesOption !== 1 ? newRecurrenceId : null,
            LocationId: craneRequestData.LocationId,
          };
          if (craneRequestData.seriesOption === 1) {
            craneRequestParam.craneDeliveryStart = craneRequestData.craneDeliveryStart;
            craneRequestParam.craneDeliveryEnd = craneRequestData.craneDeliveryEnd;
          }
          if (craneRequestData.seriesOption === 2 || craneRequestData.seriesOption === 3) {
            const utcDeliveryStartTimestamp = moment.utc(isCraneRequestExists.craneDeliveryStart);
            const localStartTimestamp = utcDeliveryStartTimestamp.tz(craneRequestData.timezone);
            const utcDeliveryEndTimestamp = moment.utc(isCraneRequestExists.craneDeliveryEnd);
            const localEndTimestamp = utcDeliveryEndTimestamp.tz(craneRequestData.timezone);
            craneRequestParam.craneDeliveryStart =
              await concreteRequestService.convertTimezoneToUtc(
                moment(localStartTimestamp).format('MM/DD/YYYY'),
                craneRequestData.timezone,
                craneRequestData.deliveryStartTime,
              );
            craneRequestParam.craneDeliveryEnd = await concreteRequestService.convertTimezoneToUtc(
              moment(localEndTimestamp).format('MM/DD/YYYY'),
              craneRequestData.timezone,
              craneRequestData.deliveryEndTime,
            );
          }
          if (
            ((memberData.RoleId === 2 || memberData.RoleId === 1) &&
              isCraneRequestExists.status === 'Approved') ||
            memberData.isAutoApproveEnabled ||
            projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
          ) {
            craneRequestParam.status = 'Approved';
            craneRequestParam.approvedBy = memberData.id;
            craneRequestParam.approved_at = new Date();
          }
          await CraneRequest.update(craneRequestParam, {
            where: { id: isCraneRequestExists.id },
          });
          const { companies, responsiblePersons, definableFeatureOfWorks } = craneRequestData;
          const equipments = [craneRequestData.EquipmentId];
          const condition = Sequelize.and({
            ProjectId: craneRequestData.ProjectId,
            CraneRequestId: isCraneRequestExists.id,
          });
          const updateParam = {
            CraneRequestId: +isCraneRequestExists.id,
            CraneRequestCode: +isCraneRequestExists.CraneRequestId,
            ProjectId: +craneRequestData.ProjectId,
            isDeleted: false,
          };
          const existCompanies = await CraneRequestCompany.findAll({ where: condition });
          const existEquipments = await CraneRequestEquipment.findAll({ where: condition });
          const existResponsiblePersons = await CraneRequestResponsiblePerson.findAll({
            where: condition,
          });
          const existDefinableFeatureOfWorks = await CraneRequestDefinableFeatureOfWork.findAll({
            where: condition,
          });
          this.updateValues(condition, async (response, error) => {
            if (!error) {
              companies.forEach(async (element, i) => {
                const index = existCompanies.findIndex((item) => item.CompanyId === element);
                const companyParam = updateParam;
                companyParam.CompanyId = element;
                if (index !== -1) {
                  await CraneRequestCompany.update(companyParam, {
                    where: { id: existCompanies[index].id },
                  });
                } else {
                  await CraneRequestCompany.createInstance(companyParam);
                }
              });

              equipments.forEach(async (element, i) => {
                const index = existEquipments.findIndex((item) => item.EquipmentId === element);
                const equipmentParam = updateParam;
                equipmentParam.EquipmentId = element;
                if (index !== -1) {
                  await CraneRequestEquipment.update(equipmentParam, {
                    where: { id: existEquipments[index].id },
                  });
                } else {
                  await CraneRequestEquipment.createInstance(equipmentParam);
                }
              });

              responsiblePersons.forEach(async (element, i) => {
                const index = existResponsiblePersons.findIndex(
                  (item) => item.MemberId === element,
                );
                const memberParam = updateParam;
                memberParam.MemberId = element;
                if (index !== -1) {
                  await CraneRequestResponsiblePerson.update(memberParam, {
                    where: { id: existResponsiblePersons[index].id },
                  });
                } else {
                  await CraneRequestResponsiblePerson.createInstance(memberParam);
                }
              });

              definableFeatureOfWorks.forEach(async (element, i) => {
                const index = existDefinableFeatureOfWorks.findIndex(
                  (item) => item.DeliverDefineWorkId === element,
                );
                const defineParam = updateParam;
                defineParam.DeliverDefineWorkId = element;
                if (index !== -1) {
                  await CraneRequestDefinableFeatureOfWork.update(defineParam, {
                    where: { id: existDefinableFeatureOfWorks[index].id },
                  });
                } else {
                  await CraneRequestDefinableFeatureOfWork.createInstance(defineParam);
                }
              });
              const locationChosen = await Locations.findOne({
                where: {
                  ProjectId: craneRequestData.ProjectId,
                  id: craneRequestData.LocationId,
                },
              });
              history.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Crane Booking ${craneRequestData.description}`;
              history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Updated the Crane Booking, ${craneRequestData.description}. Location: ${locationChosen.locationPath}.`;
              history.MemberId = memberData.id;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = craneRequestData.ProjectId;
              const projectDetails = await Project.findByPk(craneRequestData.ProjectId);
              history.projectName = projectDetails.projectName;
              notification.ProjectId = isCraneRequestExists.ProjectId;
              if (
                existsCraneRequest &&
                existsCraneRequest.recurrence &&
                existsCraneRequest.recurrence.recurrence
              ) {
                notification.recurrenceType = `${existsCraneRequest.recurrence.recurrence
                  } From ${moment(existsCraneRequest.recurrence.recurrenceStartDate).format(
                    'MM/DD/YYYY',
                  )} to ${moment(existsCraneRequest.recurrence.recurrenceEndDate).format(
                    'MM/DD/YYYY',
                  )}`;
              }
              notification.title = `Crane Booking Updated by ${loginUser.firstName} ${loginUser.lastName}`;
              notification.isDeliveryRequest = false;
              notification.requestType = 'craneRequest';
              const newNotification = await Notification.createInstance(notification);
              const memberLocationPreference = await LocationNotificationPreferences.findAll({
                where: {
                  ProjectId: craneRequestData.ProjectId,
                  LocationId: craneRequestData.LocationId,
                  follow: true,
                },
                include: [
                  {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                      [Op.and]: [
                        {
                          id: { [Op.ne]: memberData.id },
                        },
                      ],
                    },
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                  },
                ],
              });
              const locationFollowMembers = [];
              memberLocationPreference.forEach(async (element) => {
                locationFollowMembers.push(element.Member.id);
              });
              const personData = await CraneRequestResponsiblePerson.findAll({
                where: { CraneRequestId: isCraneRequestExists.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    { ProjectId: craneRequestData.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.ne]: newNotification.MemberId } },
                    { id: { [Op.in]: responsiblePersons } },
                    { id: { [Op.notIn]: locationFollowMembers } },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                  },
                ],
                attributes: ['id', 'RoleId'],
              });
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                await pushNotification.sendMemberLocationPreferencePushNotificationForCrane(
                  memberLocationPreference,
                  craneRequestData.CraneRequestId,
                  history.locationFollowDescription,
                  craneRequestData.requestType,
                  craneRequestData.ProjectId,
                  craneRequestData.id,
                  5,
                );
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  craneRequestData.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  5,
                );
              }
              history.adminData = adminData;
              history.memberData = personData;
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                history.memberData.push(...memberLocationPreference);
              }
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: craneRequestData.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 5,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                [],
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberData,
                loginUser,
                5,
                'updated a',
                'Crane Request',
                `crane Booking (${existsCraneRequest.CraneRequestId} - ${existsCraneRequest.description})`,
                existsCraneRequest.CraneRequestId,
              );
              const updatedCraneRequest = await CraneRequest.getSingleCraneRequestData({
                id: +isCraneRequestExists.id,
              });
              if (
                updatedCraneRequest.status === 'Approved' &&
                isCraneRequestExists.status !== 'Approved'
              ) {
                const object = {
                  ProjectId: craneRequestData.ProjectId,
                  MemberId: memberData.id,
                  CraneRequestId: updatedCraneRequest.id,
                  isDeleted: false,
                  type: 'approved',
                  description: history.description,
                };
                await CraneRequestHistory.createInstance(object);
              }

              this.updateEditCraneRequestHistory(
                craneRequestData,
                existsCraneRequest,
                updatedCraneRequest,
                history,
                loginUser,
              );
              await pushNotification.sendPushNotificationForCrane(
                history,
                5,
                craneRequestData.ProjectId,
              );
              history.CraneRequestId = isCraneRequestExists.CraneRequestId;

              let tagsUpdated = false;
              let fieldsChanged = false;
              if (
                updatedCraneRequest.defineWorkDetails.length > 0 &&
                existsCraneRequest.defineWorkDetails.length > 0
              ) {
                const addedDfow1 = updatedCraneRequest.defineWorkDetails.filter((el) => {
                  return !existsCraneRequest.defineWorkDetails.find((element) => {
                    return element.id === el.id;
                  });
                });
                const deletedDfow1 = existsCraneRequest.defineWorkDetails.filter((el) => {
                  return !existsCraneRequest.defineWorkDetails.find((element) => {
                    return element.id === el.id;
                  });
                });
                if (addedDfow1.length > 0) {
                  tagsUpdated = true;
                }
                if (deletedDfow1.length > 0) {
                  tagsUpdated = true;
                }
              }
              if (
                updatedCraneRequest.equipmentDetails.length > 0 &&
                existsCraneRequest.equipmentDetails.length > 0
              ) {
                const addedEquipment1 = updatedCraneRequest.equipmentDetails.filter((el) => {
                  return !existsCraneRequest.equipmentDetails.find((element) => {
                    return element.Equipment.id === el.Equipment.id;
                  });
                });
                const deletedEquipment1 = existsCraneRequest.equipmentDetails.filter((el) => {
                  return !existsCraneRequest.equipmentDetails.find((element) => {
                    return element.Equipment.id === el.Equipment.id;
                  });
                });
                if (addedEquipment1.length > 0) {
                  tagsUpdated = true;
                }
                if (deletedEquipment1.length > 0) {
                  tagsUpdated = true;
                }
              }
              if (
                updatedCraneRequest.companyDetails.length > 0 &&
                existsCraneRequest.companyDetails.length > 0
              ) {
                const addedCompany1 = updatedCraneRequest.companyDetails.filter((el) => {
                  return !existsCraneRequest.companyDetails.find((element) => {
                    return element.Company.id === el.Company.id;
                  });
                });
                const deletedCompany1 = existsCraneRequest.companyDetails.filter((el) => {
                  return !existsCraneRequest.companyDetails.find((element) => {
                    return element.Company.id === el.Company.id;
                  });
                });
                if (addedCompany1.length > 0) {
                  tagsUpdated = true;
                }
                if (deletedCompany1.length > 0) {
                  tagsUpdated = true;
                }
              }
              if (
                updatedCraneRequest.memberDetails.length > 0 &&
                existsCraneRequest.memberDetails.length > 0
              ) {
                const addedMember1 = updatedCraneRequest.memberDetails.filter((el) => {
                  return !existsCraneRequest.memberDetails.find((element) => {
                    return element.Member.id === el.Member.id;
                  });
                });
                const deletedMember1 = existsCraneRequest.memberDetails.filter((el) => {
                  return !updatedCraneRequest.memberDetails.find((element) => {
                    return element.Member.id === el.Member.id;
                  });
                });
                if (addedMember1.length > 0) {
                  tagsUpdated = true;
                }
                if (deletedMember1.length > 0) {
                  tagsUpdated = true;
                }
              }
              if (
                existsCraneRequest.description !== updatedCraneRequest.description ||
                existsCraneRequest.CraneRequestId !== updatedCraneRequest.CraneRequestId ||
                existsCraneRequest.LocationId !== updatedCraneRequest.LocationId ||
                existsCraneRequest.requestType !== updatedCraneRequest.requestType ||
                existsCraneRequest.additionalNotes !== updatedCraneRequest.additionalNotes ||
                existsCraneRequest.isAssociatedWithDeliveryRequest !==
                updatedCraneRequest.isAssociatedWithDeliveryRequest ||
                existsCraneRequest.isEscortNeeded !== updatedCraneRequest.isEscortNeeded ||
                existsCraneRequest.dropOffLocation !== updatedCraneRequest.dropOffLocation ||
                existsCraneRequest.pickUpLocation !== updatedCraneRequest.pickUpLocation ||
                tagsUpdated
              ) {
                fieldsChanged = true;
              }
              let deliveryDateTimeChanged = false;
              if (
                moment(existsCraneRequest.craneDeliveryStart).format('h:mm a') !==
                moment(updatedCraneRequest.craneDeliveryStart).format('h:mm a') ||
                moment(existsCraneRequest.craneDeliveryEnd).format('h:mm a') !==
                moment(updatedCraneRequest.craneDeliveryEnd).format('h:mm a')
              ) {
                deliveryDateTimeChanged = true;
              }
              if (existsCraneRequest.status === 'Completed') {
                if (fieldsChanged && memberData.RoleId === 2) {
                  await CraneRequest.update(
                    { status: 'Approved' },
                    {
                      where: { id: updatedCraneRequest.id },
                    },
                  );
                }
                if (
                  (fieldsChanged || deliveryDateTimeChanged) &&
                  memberData.RoleId !== 2 &&
                  !memberData.isAutoApproveEnabled &&
                  !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  await CraneRequest.update(
                    { status: 'Pending' },
                    {
                      where: { id: updatedCraneRequest.id },
                    },
                  );
                }
              }
              if (existsCraneRequest.status === 'Approved') {
                if (
                  (fieldsChanged || deliveryDateTimeChanged) &&
                  memberData.RoleId !== 2 &&
                  !memberData.isAutoApproveEnabled &&
                  !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  await CraneRequest.update(
                    { status: 'Pending' },
                    {
                      where: { id: updatedCraneRequest.id },
                    },
                  );
                }
                if (
                  ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId === 2) ||
                  memberData.isAutoApproveEnabled ||
                  projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  await CraneRequest.update(
                    { status: 'Approved' },
                    {
                      where: { id: updatedCraneRequest.id },
                    },
                  );
                }
              }
              if (
                existsCraneRequest.status === 'Expired' ||
                existsCraneRequest.status === 'Declined'
              ) {
                if (
                  (fieldsChanged || deliveryDateTimeChanged) &&
                  memberData.RoleId !== 2 &&
                  !memberData.isAutoApproveEnabled &&
                  !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  await CraneRequest.update(
                    { status: 'Pending' },
                    {
                      where: { id: updatedCraneRequest.id },
                    },
                  );
                }
                if (
                  ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId === 2) ||
                  memberData.isAutoApproveEnabled ||
                  projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  const isStatusDataUpdated = await CraneRequest.update(
                    { status: 'Approved' },
                    {
                      where: { id: updatedCraneRequest.id },
                    },
                  );
                  if (isStatusDataUpdated) {
                    const object = {
                      ProjectId: craneRequestData.ProjectId,
                      MemberId: memberData.id,
                      DeliveryRequestId: updatedCraneRequest.id,
                      isDeleted: false,
                      type: 'approved',
                      description: `${loginUser.firstName} ${loginUser.lastName} Approved the Crane Booking, ${craneRequestData.description}`,
                    };
                    await CraneRequestHistory.createInstance(object);
                  }
                }
              }
              if (existsCraneRequest.status === 'Pending') {
                if (
                  (fieldsChanged || deliveryDateTimeChanged) &&
                  memberData.RoleId !== 2 &&
                  (memberData.isAutoApproveEnabled ||
                    projectSettingDetails.ProjectSettings.isAutoApprovalEnabled)
                ) {
                  const isStatusDataUpdated = await CraneRequest.update(
                    { status: 'Approved' },
                    {
                      where: { id: updatedCraneRequest.id },
                    },
                  );
                  if (isStatusDataUpdated) {
                    const object = {
                      ProjectId: craneRequestData.ProjectId,
                      MemberId: memberData.id,
                      DeliveryRequestId: updatedCraneRequest.id,
                      isDeleted: false,
                      type: 'approved',
                      description: `${loginUser.firstName} ${loginUser.lastName} Approved the Crane Request, ${craneRequestData.description}`,
                    };
                    await CraneRequestHistory.createInstance(object);
                  }
                }
                if (
                  ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId === 2) ||
                  memberData.isAutoApproveEnabled ||
                  projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  const isStatusDataUpdated = await CraneRequest.update(
                    { status: 'Approved' },
                    {
                      where: { id: updatedCraneRequest.id },
                    },
                  );
                  if (isStatusDataUpdated) {
                    const object = {
                      ProjectId: craneRequestData.ProjectId,
                      MemberId: memberData.id,
                      DeliveryRequestId: updatedCraneRequest.id,
                      isDeleted: false,
                      type: 'approved',
                      description: `${loginUser.firstName} ${loginUser.lastName} Approved the Crane Request, ${craneRequestData.description}`,
                    };
                    await CraneRequestHistory.createInstance(object);
                  }
                }
              }
              if (+memberData.RoleId === 4 || +memberData.RoleId === 3) {
                const userEmails = await this.getMemberDetailData(
                  history,
                  memberLocationPreference,
                );
                if (userEmails.length > 0) {
                  userEmails.forEach(async (element) => {
                    if (element.RoleId === 2) {
                      let name;
                      if (!element.firstName) {
                        name = 'user';
                      } else {
                        name = `${element.firstName} ${element.lastName}`;
                      }
                      const memberRole = await Role.findOne({
                        where: {
                          id: memberData.RoleId,
                          isDeleted: false,
                        },
                      });
                      const mailPayload = {
                        name,
                        email: element.email,
                        content: `We would like to inform you that 
                          ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has updated a crane booking ${isCraneRequestExists.CraneRequestId} and waiting for your approval.Kindly review the booking and update the status.`,
                      };
                      const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
                        where: {
                          MemberId: +element.MemberId,
                          ProjectId: +craneRequestData.ProjectId,
                          LocationId: +craneRequestData.LocationId,
                          isDeleted: false,
                          // follow: true,
                        },
                      });
                      if (isMemberFollowLocation) {
                        const memberNotification = await NotificationPreference.findOne({
                          where: {
                            MemberId: +element.MemberId,
                            ProjectId: +craneRequestData.ProjectId,
                            isDeleted: false,
                          },
                          include: [
                            {
                              association: 'NotificationPreferenceItem',
                              where: {
                                id: 9,
                                isDeleted: false,
                              },
                            },
                          ],
                        });
                        if (memberNotification && memberNotification.instant) {
                          await MAILER.sendMail(
                            mailPayload,
                            'notifyPAForReApproval',
                            `Crane Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                            `Crane Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                            async (info, err) => {
                              console.log(info, err);
                            },
                          );
                        }
                        if (memberNotification && memberNotification.dailyDigest) {
                          await this.createDailyDigestDataApproval(
                            +memberData.RoleId,
                            +element.MemberId,
                            +craneRequestData.ProjectId,
                            +craneRequestData.ParentCompanyId,
                            loginUser,
                            'updated a',
                            'Crane Request',
                            `crane Booking (${existsCraneRequest.CraneRequestId} - ${existsCraneRequest.description})`,
                            'and waiting for your approval',
                            existsCraneRequest.CraneRequestId,
                          );
                        }
                      }
                    }
                  });
                  return done(history, false);
                }
              } else {
                return done(history, false);
              }
            } else {
              return done(null, error);
            }
          });
        });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getSingleCraneRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const getCraneRequest = await CraneRequest.findOne({
        where: { CraneRequestId: params.CraneRequestId, ProjectId: params.ProjectId },
      });
      const craneRequest = await CraneRequest.getSingleCraneRequestData({
        id: +getCraneRequest.id,
      });
      done(craneRequest, false);
    } catch (e) {
      done(null, e);
    }
  },
  async updateCraneRequestStatus(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const updateData = inputData.body;
      const loginUser = inputData.user;
      const statusValue = await CraneRequest.findOne({
        where: { id: updateData.id },
        include: [
          {
            association: 'recurrence',
            required: false,
            attributes: [
              'id',
              'recurrence',
              'recurrenceStartDate',
              'recurrenceEndDate',
              'dateOfMonth',
              'monthlyRepeatType',
              'repeatEveryCount',
              'days',
              'requestType',
              'repeatEveryType',
              'chosenDateOfMonth',
              'createdBy',
            ],
          },
        ],
      });
      const NDRData = await CraneRequest.getSingleCraneRequestData({ id: updateData.id });
      if (!statusValue) {
        done(null, { message: 'Id does not exist.' });
      } else {
        const memberValue = await Member.findOne({
          where: Sequelize.and({
            UserId: inputData.user.id,
            ProjectId: statusValue.ProjectId,
            isDeleted: false,
          }),
        });
        if (memberValue) {
          if (
            memberValue.RoleId === 2 ||
            memberValue.RoleId === 3 ||
            memberValue.RoleId === 1 ||
            memberValue.RoleId === 4
          ) {
            if (memberValue.RoleId === 4) {
              if (+loginUser.id !== +NDRData.createdUserDetails.User.id) {
                return done(null, {
                  message:
                    'SC can able to deliver the Crane Booking which was created by him only.',
                });
              }
            }
            const locationChosen = await Locations.findOne({
              where: {
                ProjectId: statusValue.ProjectId,
                id: statusValue.LocationId,
              },
            });
            const memberLocationPreference = await LocationNotificationPreferences.findAll({
              where: {
                ProjectId: statusValue.ProjectId,
                LocationId: statusValue.LocationId,
                follow: true,
              },
              include: [
                {
                  association: 'Member',
                  attributes: ['id', 'RoleId'],
                  where: {
                    [Op.and]: [
                      {
                        id: { [Op.ne]: memberValue.id },
                      },
                    ],
                  },
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'firstName', 'lastName', 'email'],
                    },
                  ],
                },
              ],
            });
            const locationFollowMembers = [];
            memberLocationPreference.forEach(async (element) => {
              locationFollowMembers.push(element.Member.id);
            });
            const bookingMemberDetails = [];
            NDRData.memberDetails.forEach(async (element) => {
              bookingMemberDetails.push(element.Member.id);
            });
            const history = {
              CraneRequestId: statusValue.id,
              MemberId: memberValue.id,
            };
            if (updateData.status === 'Approved') {
              if (updateData.statuschange && updateData.statuschange === 'Reverted') {
                history.type = 'approved';
                history.description = `${loginUser.firstName} ${loginUser.lastName} Reverted the status from completed to approved for Crane Booking, ${statusValue.description}`;
                history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Reverted the status from completed to approved for Crane Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
              } else {
                history.type = 'approved';
                history.description = `${loginUser.firstName} ${loginUser.lastName} Approved the Crane Booking`;
                history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Approved the Crane Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
              }
              const notification = history;
              notification.ProjectId = statusValue.ProjectId;
              if (statusValue && statusValue.recurrence && statusValue.recurrence.recurrence) {
                notification.recurrenceType = `${statusValue.recurrence.recurrence} From ${moment(
                  statusValue.recurrence.recurrenceStartDate,
                ).format('MM/DD/YYYY')} to ${moment(
                  statusValue.recurrence.recurrenceEndDate,
                ).format('MM/DD/YYYY')}`;
              }
              notification.title = `Crane Booking Approved by ${loginUser.firstName} ${loginUser.lastName}`;
              await CraneRequest.update(
                { status: updateData.status, approvedBy: memberValue.id, approved_at: new Date() },
                { where: { id: updateData.id } },
              );
              await CraneRequestHistory.createInstance(history);
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = statusValue.ProjectId;
              const projectDetails = await Project.findByPk(statusValue.ProjectId);
              history.projectName = projectDetails.projectName;
              notification.isDeliveryRequest = false;
              notification.requestType = 'craneRequest';
              const newNotification = await Notification.createInstance(notification);
              const personData = await CraneRequestResponsiblePerson.findAll({
                where: { CraneRequestId: statusValue.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    { ProjectId: statusValue.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.in]: bookingMemberDetails } },
                    { id: { [Op.ne]: newNotification.MemberId } },
                    { id: { [Op.notIn]: locationFollowMembers } },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                  },
                ],
                attributes: ['id'],
              });
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                await pushNotification.sendMemberLocationPreferencePushNotificationForCrane(
                  memberLocationPreference,
                  statusValue.CraneRequestId,
                  history.locationFollowDescription,
                  statusValue.requestType,
                  statusValue.ProjectId,
                  statusValue.id,
                  6,
                );
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  statusValue.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  6,
                );
              }
              history.memberData = personData;
              history.adminData = adminData;
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: statusValue.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 6,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                personData,
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberValue,
                loginUser,
                6,
                'approved a',
                'Crane Request',
                `crane Booking (${statusValue.CraneRequestId} - ${statusValue.description})`,
                statusValue.CraneRequestId,
              );
              await pushNotification.sendPushNotificationForCrane(
                history,
                6,
                statusValue.ProjectId,
              );
              history.CraneRequestId = statusValue.CraneRequestId;
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                history.memberData.push(...memberLocationPreference);
              }
              return done(history, false);
            }
            if (updateData.status === 'Declined' || updateData.status === 'Completed') {
              const craneRequestStatus = updateData.status;
              history.type = updateData.status.toLowerCase();
              history.description = `${loginUser.firstName} ${loginUser.lastName} ${craneRequestStatus} the Crane Booking`;
              history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} ${craneRequestStatus} the Crane Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
              const notification = history;
              notification.ProjectId = statusValue.ProjectId;
              if (statusValue && statusValue.recurrence && statusValue.recurrence.recurrence) {
                notification.recurrenceType = `${statusValue.recurrence} From ${moment(
                  statusValue.recurrence.recurrenceStartDate,
                ).format('MM/DD/YYYY')} to ${moment(
                  statusValue.recurrence.recurrenceEndDate,
                ).format('MM/DD/YYYY')}`;
              }
              notification.title = `Crane Booking ${craneRequestStatus} by ${loginUser.firstName} ${loginUser.lastName} `;
              await CraneRequest.update(
                { status: updateData.status },
                { where: { id: updateData.id } },
              );
              await CraneRequestHistory.createInstance(history);
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = statusValue.ProjectId;
              const projectDetails = await Project.findByPk(statusValue.ProjectId);
              history.projectName = projectDetails.projectName;
              notification.isDeliveryRequest = false;
              notification.requestType = 'craneRequest';
              const newNotification = await Notification.createInstance(notification);
              const personData = await CraneRequestResponsiblePerson.findAll({
                where: { CraneRequestId: statusValue.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    { ProjectId: statusValue.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.in]: bookingMemberDetails } },
                    { id: { [Op.ne]: newNotification.MemberId } },
                    { id: { [Op.notIn]: locationFollowMembers } },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                  },
                ],
                attributes: ['id'],
              });
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                await pushNotification.sendMemberLocationPreferencePushNotificationForCrane(
                  memberLocationPreference,
                  statusValue.CraneRequestId,
                  history.locationFollowDescription,
                  statusValue.requestType,
                  statusValue.ProjectId,
                  statusValue.id,
                  6,
                );
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  statusValue.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  6,
                );
              }
              history.memberData = personData;
              history.adminData = adminData;
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                history.memberData.push(...memberLocationPreference);
              }
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                personData,
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberValue,
                loginUser,
                6,
                `${craneRequestStatus.toLowerCase()} a`,
                'Crane Request',
                `crane Booking (${statusValue.CraneRequestId} - ${statusValue.description})`,
                statusValue.CraneRequestId,
              );
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: statusValue.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 6,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              await pushNotification.sendPushNotificationForCrane(
                history,
                6,
                statusValue.ProjectId,
              );
              history.CraneRequestId = statusValue.CraneRequestId;
              if (updateData.status === 'Completed') {
                const userEmails = await this.getMemberDetailData(
                  history,
                  memberLocationPreference,
                );
                if (userEmails.length > 0) {
                  userEmails.forEach(async (element) => {
                    let name;
                    if (!element.firstName) {
                      name = 'user';
                    } else {
                      name = `${element.firstName} ${element.lastName}`;
                    }
                    const time = moment(statusValue.craneDeliveryStart).format('MM-DD-YYYY');
                    const mailPayload = {
                      userName: name,
                      email: element.email,
                      description: statusValue.description,
                      userName1: `${loginUser.firstName} ${loginUser.lastName}`,
                      craneID: statusValue.CraneRequestId,
                      craneId: statusValue.CraneRequestId,
                      status_timestamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
                      timestamp: time,
                    };
                    const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
                      where: {
                        MemberId: +element.MemberId,
                        ProjectId: +statusValue.ProjectId,
                        LocationId: +statusValue.LocationId,
                        isDeleted: false,
                        // follow: true,
                      },
                    });
                    if (isMemberFollowLocation) {
                      const memberNotification = await NotificationPreference.findOne({
                        where: {
                          MemberId: +element.MemberId,
                          ProjectId: +statusValue.ProjectId,
                          isDeleted: false,
                        },
                        include: [
                          {
                            association: 'NotificationPreferenceItem',
                            where: {
                              id: 11,
                              isDeleted: false,
                            },
                          },
                        ],
                      });
                      if (memberNotification && memberNotification.instant) {
                        await MAILER.sendMail(
                          mailPayload,
                          'completedCraneRequest',
                          'Crane Booking status updated',
                          'Crane Booking status updated',
                          async (info, err) => {
                            console.log(info, err);
                          },
                        );
                      }
                      if (memberNotification && memberNotification.dailyDigest) {
                        await this.createDailyDigestData(
                          +memberValue.RoleId,
                          +element.MemberId,
                          +statusValue.ProjectId,
                          +statusValue.ParentCompanyId,
                          loginUser,
                          'completed a',
                          'Crane Request',
                          `crane Booking (${statusValue.CraneRequestId} - ${statusValue.description})`,
                          statusValue.CraneRequestId,
                        );
                      }
                    }
                  });
                  done(history, false);
                } else {
                  done(history, false);
                }
              } else {
                return done(history, false);
              }
            } else {
              done(null, { message: 'Invalid Status' });
            }
          } else {
            done(null, {
              message: 'Only Project Admin and General Contracter allowed to update the status',
            });
          }
        } else {
          done(null, {
            message: 'Not a Valid Member for this Crane Booking',
          });
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getSearchCraneData(
    req,
    incomeData,
    deliveryList,
    result,
    limit,
    index,
    count,
    memberDetails,
    done,
  ) {
    const elementValue = deliveryList[index];
    if (elementValue) {
      const element = JSON.parse(JSON.stringify(elementValue));
      const status = { companyCondition: true, memberCondition: true };
      if (Number(req.params.void) === 1) {
        if (incomeData.companyFilter > 0) {
          const data = element.companyDetails.find((ele) => {
            return ele.Company.companyName === incomeData.companyFilter;
          });
          if (!data) {
            status.companyCondition = false;
          }
        }
      } else if (Number(req.params.void) === 0) {
        if (incomeData.companyFilter > 0) {
          const data = await CraneRequestCompany.findOne({
            where: {
              CraneRequestId: element.id,
              CompanyId: +incomeData.companyFilter,
              isDeleted: false,
            },
          });
          if (!data) {
            status.companyCondition = false;
          }
        }
      }
      // if (memberDetails.RoleId === 4 || memberDetails.RoleId === 3) {
      //   const data = await CraneRequestResponsiblePerson.findOne({
      //     where: {
      //       CraneRequestId: element.id,
      //       MemberId: memberDetails.id,
      //       isDeleted: false,
      //       isActive: true,
      //     },
      //   });
      //   if (!data) {
      //     status.memberCondition = false;
      //   }
      // }
      if (status.memberCondition && status.companyCondition) {
        result.push(element);
      }
      if (index < deliveryList.length - 1) {
        this.getSearchCraneData(
          req,
          incomeData,
          deliveryList,
          result,
          limit,
          index + 1,
          count + 1,
          memberDetails,
          (response, err) => {
            if (!err) {
              done(response, false);
            } else {
              done(null, err);
            }
          },
        );
      } else {
        done(result, false);
      }
    } else {
      done(result, false);
    }
  },
  async getSearchDeliveryData(
    req,
    incomeData,
    deliveryList,
    result,
    limit,
    index,
    count,
    memberDetails,
    done,
  ) {
    const elementValue = deliveryList[index];
    if (elementValue) {
      const element = JSON.parse(JSON.stringify(elementValue));
      const status = { companyCondition: true, memberCondition: true };
      if (Number(req.params.void) === 1) {
        if (incomeData.companyFilter > 0) {
          const data = element.companyDetails.find((ele) => {
            return ele.Company.companyName === incomeData.companyFilter;
          });
          if (!data) {
            status.companyCondition = false;
          }
        }
      } else if (Number(req.params.void) === 0) {
        if (incomeData.companyFilter > 0) {
          const data = await DeliverCompany.findOne({
            where: {
              DeliveryId: element.id,
              CompanyId: +incomeData.companyFilter,
              isDeleted: false,
            },
          });
          if (!data) {
            status.companyCondition = false;
          }
        }
      }

      // if (memberDetails.RoleId === 4 || memberDetails.RoleId === 3) {
      //   const data = await DeliveryPerson.findOne({
      //     where: {
      //       DeliveryId: element.id,
      //       MemberId: memberDetails.id,
      //       isDeleted: false,
      //       isActive: true,
      //     },
      //   });
      //   if (!data) {
      //     status.memberCondition = false;
      //   }
      // }
      if (status.memberCondition && status.companyCondition) {
        result.push(element);
      }
      if (index < deliveryList.length - 1) {
        this.getSearchDeliveryData(
          req,
          incomeData,
          deliveryList,
          result,
          limit,
          index + 1,
          count + 1,
          memberDetails,
          (response, err) => {
            if (!err) {
              done(response, false);
            } else {
              done(null, err);
            }
          },
        );
      } else {
        done(result, false);
      }
    } else {
      done(result, false);
    }
  },
  async updateEditCraneRequestHistory(
    userEditedCraneRequestData,
    existsCraneRequestData,
    updatedCraneRequest,
    history,
    loginUser,
  ) {
    const historyObject = history;
    if (
      (userEditedCraneRequestData.description &&
        userEditedCraneRequestData.description.toLowerCase()) !==
      (existsCraneRequestData.description && existsCraneRequestData.description.toLowerCase())
    ) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Description ${userEditedCraneRequestData.description}`;
      CraneRequestHistory.createInstance(historyObject);
    }
    if (
      userEditedCraneRequestData.LocationId !== existsCraneRequestData.LocationId &&
      updatedCraneRequest &&
      updatedCraneRequest.location &&
      updatedCraneRequest.location.locationPath
    ) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Location, ${updatedCraneRequest.location.locationPath} `;
      CraneRequestHistory.createInstance(historyObject);
    }
    if (
      new Date(userEditedCraneRequestData.craneDeliveryStart).getTime() !==
      new Date(existsCraneRequestData.craneDeliveryStart).getTime()
    ) {
      historyObject.description = `${loginUser.firstName} 
      ${loginUser.lastName} Updated the Crane Pick From Time ${userEditedCraneRequestData.craneDeliveryStart}`;
      CraneRequestHistory.createInstance(historyObject);
    }
    if (
      new Date(userEditedCraneRequestData.craneDeliveryEnd).getTime() !==
      new Date(existsCraneRequestData.craneDeliveryEnd).getTime()
    ) {
      historyObject.description = `${loginUser.firstName} 
      ${loginUser.lastName} Updated the Crane Pick To Time ${userEditedCraneRequestData.craneDeliveryEnd}`;
      CraneRequestHistory.createInstance(historyObject);
    }
    if (userEditedCraneRequestData.additionalNotes) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Notes ${userEditedCraneRequestData.additionalNotes}`;
      CraneRequestHistory.createInstance(historyObject);
    }
    if (!userEditedCraneRequestData.additionalNotes) {
      if (existsCraneRequestData.additionalNotes) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Removed the Notes ${existsCraneRequestData.additionalNotes}`;
        CraneRequestHistory.createInstance(historyObject);
      }
    }
    if (userEditedCraneRequestData.pickUpLocation !== existsCraneRequestData.pickUpLocation) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Picking From ${userEditedCraneRequestData.pickUpLocation}`;
      CraneRequestHistory.createInstance(historyObject);
    }
    if (userEditedCraneRequestData.dropOffLocation !== existsCraneRequestData.dropOffLocation) {
      historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Picking To ${userEditedCraneRequestData.dropOffLocation}`;
      CraneRequestHistory.createInstance(historyObject);
    }
    if (userEditedCraneRequestData.escort !== existsCraneRequestData.escort) {
      if (userEditedCraneRequestData.escort === true) {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} enabled the Escort`;
      } else {
        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} disabled the Escort`;
      }
      CraneRequestHistory.createInstance(historyObject);
    }
    if (
      updatedCraneRequest.memberDetails.length > 0 &&
      existsCraneRequestData.memberDetails.length > 0
    ) {
      const addedMember = updatedCraneRequest.memberDetails.filter((el) => {
        return !existsCraneRequestData.memberDetails.find((element) => {
          return element.id === el.id;
        });
      });
      const deletedMember = existsCraneRequestData.memberDetails.filter((el) => {
        return !updatedCraneRequest.memberDetails.find((element) => {
          return element.id === el.id;
        });
      });
      if (addedMember.length > 0) {
        addedMember.forEach(async (element) => {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the member ${element.Member.User.firstName} ${element.Member.User.lastName}`;
          await CraneRequestHistory.createInstance(historyObject);
        });
      }
      if (deletedMember.length > 0) {
        deletedMember.forEach(async (element) => {
          if (element.Member.User.firstName) {
            historyObject.description = `${loginUser.firstName} ${loginUser.lastName} deleted the member ${element.Member.User.firstName} ${element.Member.User.lastName}`;
          } else {
            historyObject.description = `${loginUser.firstName} ${loginUser.lastName} deleted the member`;
          }
          await CraneRequestHistory.createInstance(historyObject);
        });
      }
    }
    if (
      updatedCraneRequest.companyDetails.length > 0 &&
      existsCraneRequestData.companyDetails.length > 0
    ) {
      const addedCompany = updatedCraneRequest.companyDetails.filter((el) => {
        return !existsCraneRequestData.companyDetails.find((element) => {
          return element.id === el.id;
        });
      });
      const deletedCompany = existsCraneRequestData.companyDetails.filter((el) => {
        return !updatedCraneRequest.companyDetails.find((element) => {
          return element.id === el.id;
        });
      });
      if (addedCompany.length > 0) {
        addedCompany.forEach(async (element) => {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the company ${element.Company.companyName}`;
          await CraneRequestHistory.createInstance(historyObject);
        });
      }
      if (deletedCompany.length > 0) {
        deletedCompany.forEach(async (element) => {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} deleted the company ${element.Company.companyName}`;
          await CraneRequestHistory.createInstance(historyObject);
        });
      }
    }
    if (
      updatedCraneRequest.defineWorkDetails.length > 0 &&
      existsCraneRequestData.defineWorkDetails.length > 0
    ) {
      const addedDfow = updatedCraneRequest.defineWorkDetails.filter((el) => {
        return !existsCraneRequestData.defineWorkDetails.find((element) => {
          return element.id === el.id;
        });
      });
      const deletedDfow = existsCraneRequestData.defineWorkDetails.filter((el) => {
        return !updatedCraneRequest.defineWorkDetails.find((element) => {
          return element.id === el.id;
        });
      });
      if (addedDfow.length > 0) {
        addedDfow.forEach(async (element) => {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the Definable feature of work ${element.DeliverDefineWork.DFOW}`;
          await CraneRequestHistory.createInstance(historyObject);
        });
      }
      if (deletedDfow.length > 0) {
        deletedDfow.forEach(async (element) => {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} deleted the Definable feature of work ${element.DeliverDefineWork.DFOW}`;
          await CraneRequestHistory.createInstance(historyObject);
        });
      }
    }
    if (
      updatedCraneRequest.equipmentDetails.length > 0 &&
      existsCraneRequestData.equipmentDetails.length > 0
    ) {
      const addedEquipment = updatedCraneRequest.equipmentDetails.filter((el) => {
        return !existsCraneRequestData.equipmentDetails.find((element) => {
          return element.Equipment.id === el.Equipment.id;
        });
      });
      const deletedEquipment = existsCraneRequestData.equipmentDetails.filter((el) => {
        return !updatedCraneRequest.equipmentDetails.find((element) => {
          return element.Equipment.id === el.Equipment.id;
        });
      });
      if (addedEquipment.length > 0) {
        addedEquipment.forEach(async (element) => {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the Equipment ${element.Equipment.equipmentName}`;
          await CraneRequestHistory.createInstance(historyObject);
        });
      }
      if (deletedEquipment.length > 0) {
        deletedEquipment.forEach(async (element) => {
          historyObject.description = `${loginUser.firstName} ${loginUser.lastName} deleted the Equipment ${element.Equipment.equipmentName}`;
          await CraneRequestHistory.createInstance(historyObject);
        });
      }
    }
  },
  async upcomingCraneRequest(req) {
    try {
      req.body.ParentCompanyId = req.query.ParentCompanyId;
      await this.getDynamicModel(req);
      const data = req.query;
      const loginUser = req.user;
      const memberDetails = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: data.ProjectId,
          isDeleted: false,
          isActive: true,
        }),
      });
      if (memberDetails) {
        let craneRequestList = await CraneRequest.upcomingCraneRequest(data.ProjectId);
        const deliveryRequestCondition = {
          ProjectId: +data.ProjectId,
          isDeleted: false,
          deliveryStart: {
            [Op.gt]: new Date(),
          },
          isQueued: false,
          isAssociatedWithCraneRequest: true,
        };
        if (!data.limit) {
          delete deliveryRequestCondition.isAssociatedWithCraneRequest;
        }

        const deliveryRequestList = await DeliveryRequest.findAll({
          subQuery: false,
          distinct: true,
          required: false,
          include: [
            {
              association: 'memberDetails',
              where: { isDeleted: false, isActive: true },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  attributes: ['id', 'UserId'],
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'email', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
            {
              association: 'companyDetails',
              required: false,
              where: { isDeleted: false },
              attributes: ['id'],
              include: [
                {
                  association: 'Company',
                  attributes: ['companyName', 'id'],
                },
              ],
            },
          ],
          where: deliveryRequestCondition,
        });
        const concreteRequestCondition = {
          ProjectId: +data.ProjectId,
          isDeleted: false,
          concretePlacementStart: {
            [Op.gt]: new Date(),
          },
        };
        const concreteRequestList = await ConcreteRequest.findAll({
          subQuery: false,
          distinct: true,
          required: false,
          include: [
            {
              association: 'memberDetails',
              where: { isDeleted: false, isActive: true },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  attributes: ['id', 'UserId'],
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'email', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
            {
              association: 'concreteSupplierDetails',
              required: false,
              where: { isDeleted: false },
              attributes: ['id'],
              include: [
                {
                  association: 'Company',
                  attributes: ['companyName', 'id'],
                },
              ],
            },
          ],
          where: concreteRequestCondition,
        });
        if (deliveryRequestList.length > 0) {
          craneRequestList.push(...deliveryRequestList);
        }
        if (craneRequestList.length > 0) {
          craneRequestList.sort((a, b) =>
            (a.craneDeliveryStart ? a.craneDeliveryStart : a.deliveryStart) >
              (b.craneDeliveryStart ? b.craneDeliveryStart : b.deliveryStart)
              ? 1
              : -1,
          );
        }
        if (concreteRequestList.length > 0) {
          craneRequestList.push(...concreteRequestList);
        }
        if (data.limit) {
          craneRequestList = craneRequestList.slice(0, +data.limit);
        }
        return { status: 200, data: craneRequestList };
      }
      return { status: 422, msg: 'Project Id/ParendCompany Id/Member does not exist' };
    } catch (e) {
      return e;
    }
  },
  async upcomingRequestList(req) {
    try {
      let upcomingList = [];
      const craneRequestData = await this.upcomingCraneRequest(req);
      if (craneRequestData && craneRequestData.data && craneRequestData.data.length > 0) {
        upcomingList.push(...craneRequestData.data);
      }
      if (upcomingList.length > 0) {
        upcomingList.sort((a, b) =>
          (a.craneDeliveryStart ? a.craneDeliveryStart : a.deliveryStart) >
            (b.craneDeliveryStart ? b.craneDeliveryStart : b.deliveryStart)
            ? 1
            : -1,
        );
      }
      upcomingList = upcomingList.slice(0, 10);
      return { status: 200, data: upcomingList };
    } catch (e) {
      return e;
    }
  },
  async getMemberDetailData(data, memberLocationPreference) {
    const emailArray = [];
    const existAdminData = [];
    if (data.memberData !== undefined) {
      data.memberData.forEach((element) => {
        const index = existAdminData.findIndex(
          (adminNew) => adminNew.email === element.Member.User.email,
        );
        if (index === -1) {
          existAdminData.push({ email: element.Member.User.email });
          emailArray.push({
            email: element.Member.User.email,
            firstName: element.Member.User.firstName,
            lastName: element.Member.User.lastName,
            UserId: element.Member.User.id,
            MemberId: element.Member.id,
            RoleId: element.Member.RoleId,
          });
        }
      });
    }
    if (data.adminData !== undefined) {
      data.adminData.forEach((element) => {
        const index = existAdminData.findIndex((adminNew) => adminNew.email === element.User.email);
        if (index === -1) {
          existAdminData.push({ email: element.User.email });
          emailArray.push({
            email: element.User.email,
            firstName: element.User.firstName,
            lastName: element.User.lastName,
            UserId: element.User.id,
            MemberId: element.id,
            RoleId: element.RoleId,
          });
        }
      });
    }
    if (memberLocationPreference !== undefined && memberLocationPreference.length > 0) {
      memberLocationPreference.forEach((element) => {
        const index = existAdminData.findIndex(
          (adminNew) => adminNew.email === element.Member.User.email,
        );
        if (index === -1) {
          existAdminData.push({ email: element.Member.User.email });
          emailArray.push({
            email: element.Member.User.email,
            firstName: element.Member.User.firstName,
            lastName: element.Member.User.lastName,
            UserId: element.Member.User.id,
            MemberId: element.Member.id,
            RoleId: element.Member.RoleId,
          });
        }
      });
    }
    return emailArray;
  },
  // prettier-ignore
  async createDailyDigestData(
    RoleId,
    MemberId,
    ProjectId,
    ParentCompanyId,
    loginUser,
    dailyDigestMessage,
    requestType,
    messages,
    requestId,
  ) {
    const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
    const encryptedRequestId = cryptr.encrypt(requestId);
    const encryptedMemberId = cryptr.encrypt(MemberId);
    let imageUrl;
    let link;
    let height;
    if (requestType === 'Delivery Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/87758081-8ccd-4e4d-a4eb-6257466876da.png';
      link = 'delivery-request';
      height = 'height:18px;';
    }
    if (requestType === 'Crane Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/ab400721-520c-4f6f-941c-ac07acc28809.png';
      link = 'crane-request';
      height = 'height:32px;';
    }
    if (requestType === 'Concrete Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/c43e4c58-4c4d-44ff-b78c-fe9948ea53eb.png';
      link = 'concrete-request';
      height = 'height:18px;';
    }
    const object = {
      description: `<div>
	<ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
		<li style="display:flex;">
			<img src="${imageUrl}" alt="message-icon" style="${height}">
				<p style="margin:0px;font-size:12px;padding-left:10px;">
					<a href="#" target="" style="text-decoration: none;color:#4470FF;">
						${loginUser.firstName}  ${loginUser.lastName}
					</a>
					${dailyDigestMessage}
				<a href="${process.env.BASE_URL}/${link}?requestId=${encryptedRequestId}&memberId=${encryptedMemberId}" style="text-decoration: none;color:#4470FF;" >${messages}</a>
					<span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment()
          .utc()
          .format('hh:mm A zz')}</span>
				</p>
		</li>
	</ul>
</div>`,
      MemberId,
      ProjectId,
      isSent: false,
      isDeleted: false,
      ParentCompanyId,
    };
    await DigestNotification.create(object);
  },
  async getVoidRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      const { sort } = inputData.body;
      const { sortByField } = inputData.body;
      let order;
      if (params.void >= 1 && params.void <= 0) {
        done(null, { message: 'Please enter void as 1 or 0' });
      } else {
        const memberDetails = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: params.ProjectId,
            isDeleted: false,
            isActive: true,
          }),
        });
        if (memberDetails) {
          const voidCraneDelivery = [];
          const voidDelivery = [];
          const voidDeliveryList = await VoidList.findAll({
            where: {
              ProjectId: +params.ProjectId,
              isDeliveryRequest: true,
              DeliveryRequestId: { [Op.ne]: null },
            },
          });
          voidDeliveryList.forEach(async (element) => {
            voidDelivery.push(element.DeliveryRequestId);
          });
          const voidCraneRequestList = await VoidList.findAll({
            where: {
              ProjectId: +params.ProjectId,
              isDeliveryRequest: false,
              CraneRequestId: { [Op.ne]: null },
            },
          });
          voidCraneRequestList.forEach(async (element) => {
            voidCraneDelivery.push(element.CraneRequestId);
          });
          const offset = (+params.pageNo - 1) * +params.pageSize;
          const craneCondition = {
            ProjectId: +params.ProjectId,
            isDeleted: false,
          };
          const condition = {
            ProjectId: +params.ProjectId,
            isDeleted: false,
          };
          if (params.void === '0' || params.void === 0) {
            condition['$DeliveryRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidDelivery }],
            };
          } else {
            condition['$DeliveryRequest.id$'] = {
              [Op.and]: [{ [Op.in]: voidDelivery }],
            };
          }
          if (params.void === '0' || params.void === 0) {
            craneCondition['$CraneRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidCraneDelivery }],
            };
          } else {
            craneCondition['$CraneRequest.id$'] = {
              [Op.and]: [{ [Op.in]: voidCraneDelivery }],
            };
          }
          const roleId = memberDetails.RoleId;
          const memberId = memberDetails.id;
          let craneRequestList;
          let deliveryRequest;
          if (
            (incomeData.gateFilter && incomeData.gateFilter > 0) ||
            (incomeData.statusFilter && incomeData.statusFilter === 'Delivered')
          ) {
            craneRequestList = [];
          } else {
            craneRequestList = await CraneRequest.getAll(
              inputData,
              roleId,
              memberId,
              craneCondition,
              incomeData.descriptionFilter,
              incomeData.startdate,
              incomeData.enddate,
              incomeData.companyFilter,
              incomeData.memberFilter,
              incomeData.equipmentFilter,
              incomeData.statusFilter,
              incomeData.idFilter,
              incomeData.pickFrom,
              incomeData.pickTo,
              incomeData.search,
              order,
              sort,
              sortByField,
              incomeData.dateFilter,
            );
          }
          if (incomeData.statusFilter && incomeData.statusFilter === 'Completed') {
            deliveryRequest = [];
          } else {
            deliveryRequest = await DeliveryRequest.getCraneAssociatedRequest(
              inputData,
              roleId,
              memberId,
              condition,
              incomeData.descriptionFilter,
              incomeData.startdate,
              incomeData.enddate,
              incomeData.companyFilter,
              incomeData.memberFilter,
              incomeData.equipmentFilter,
              incomeData.statusFilter,
              incomeData.idFilter,
              incomeData.pickFrom,
              incomeData.pickTo,
              incomeData.search,
              incomeData.gateFilter,
              order,
              sort,
              sortByField,
              params.void,
              incomeData.dateFilter,
            );
          }
          this.getSearchCraneData(
            inputData,
            incomeData,
            craneRequestList,
            [],
            +params.pageSize,
            0,
            0,
            memberDetails,
            async (checkResponse, checkError) => {
              if (!checkError) {
                craneRequestList = checkResponse;
                this.getSearchDeliveryData(
                  inputData,
                  incomeData,
                  deliveryRequest,
                  [],
                  +params.pageSize,
                  0,
                  0,
                  memberDetails,
                  async (checkResponse1, checkError1) => {
                    if (!checkError1) {
                      deliveryRequest = checkResponse1;
                      craneRequestList.push(...deliveryRequest);
                      this.getLimitData(
                        craneRequestList,
                        0,
                        +params.pageSize,
                        [],
                        incomeData,
                        inputData.headers.timezoneoffset,
                        async (newResponse, newError) => {
                          if (!newError) {
                            done(newResponse, false);
                          } else {
                            done(null, { message: 'Something went wrong' });
                          }
                        },
                      );
                    } else {
                      done(null, { message: 'Something went wrong' });
                    }
                  },
                );
              } else {
                done(null, { message: 'Something went wrong' });
              }
            },
          );
        } else {
          done(null, { message: 'Project Id/Member does not exist' });
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async sendEmailNotificationToUser(
    history,
    memberDetails,
    loginUser,
    newDeliverData,
    deliveryData,
    memberLocationPreference,
  ) {
    const userEmails = await this.getMemberDetailData(history, memberLocationPreference);
    if (userEmails.length > 0) {
      userEmails.forEach(async (element) => {
        let name;
        if (!element.firstName) {
          name = 'user';
        } else {
          name = `${element.firstName} ${element.lastName}`;
        }
        if (+element.MemberId !== +memberDetails.id) {
          const memberRole = await Role.findOne({
            where: {
              id: memberDetails.RoleId,
              isDeleted: false,
            },
          });
          const time = moment(newDeliverData.craneDeliveryStart).format('MM-DD-YYYY');
          const mailPayload = {
            userName: name,
            email: element.email,
            craneId: newDeliverData.CraneRequestId,
            description: newDeliverData.description,
            timestamp: time,
            createdTimestamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
            content: ` ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has created the crane booking ${newDeliverData.CraneRequestId}.Please see below for more details`,
          };
          const memberNotification = await NotificationPreference.findOne({
            where: {
              MemberId: +element.MemberId,
              ProjectId: +deliveryData.ProjectId,
              isDeleted: false,
            },
            include: [
              {
                association: 'NotificationPreferenceItem',
                where: {
                  id: 12,
                  isDeleted: false,
                },
              },
            ],
          });
          if (memberNotification && memberNotification.instant) {
            await MAILER.sendMail(
              mailPayload,
              'craneRequestCreated',
              `Crane Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
              `Crane Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
              async (info, err) => {
                console.log(info, err);
              },
            );
          }
          if (memberNotification && memberNotification.dailyDigest) {
            await this.createDailyDigestData(
              +memberDetails.RoleId,
              +element.MemberId,
              +deliveryData.ProjectId,
              +deliveryData.ParentCompanyId,
              loginUser,
              'created a',
              'Crane Request',
              `crane Booking (${newDeliverData.CraneRequestId} - ${newDeliverData.description})`,
              newDeliverData.CraneRequestId,
            );
          }
        }
      });
    }
    return true;
  },
  async createDailyDigestDataApproval(
    RoleId,
    MemberId,
    ProjectId,
    ParentCompanyId,
    loginUser,
    dailyDigestMessage,
    requestType,
    messages,
    messages2,
    requestId,
  ) {
    const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
    const encryptedRequestId = cryptr.encrypt(requestId);
    const encryptedMemberId = cryptr.encrypt(MemberId);
    let imageUrl;
    let link;
    let height;
    if (requestType === 'Delivery Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/87758081-8ccd-4e4d-a4eb-6257466876da.png';
      link = 'delivery-request';
      height = 'height:18px;';
    }
    if (requestType === 'Crane Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/ab400721-520c-4f6f-941c-ac07acc28809.png';
      link = 'crane-request';
      height = 'height:32px;';
    }
    if (requestType === 'Concrete Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/c43e4c58-4c4d-44ff-b78c-fe9948ea53eb.png';
      link = 'concrete-request';
      height = 'height:18px;';
    }
    const object = {
      description: `<div>
	<ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
		<li style="display:flex;">
			<img src="${imageUrl}" alt="message-icon" style="${height}">
				<p style="margin:0px;font-size:12px;padding-left:10px;">
					<a href="#" target="" style="text-decoration: none;color:#4470FF;">
						${loginUser.firstName}  ${loginUser.lastName}
					</a>
					${dailyDigestMessage}
				<a href="${process.env.BASE_URL
        }/${link}?requestId=${encryptedRequestId}&memberId=${encryptedMemberId}" style="text-decoration: none;color:#4470FF;" >
      ${messages}
					</a>
             ${messages2}
					<span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment()
          .utc()
          .format('hh:mm A zz')}</span>
				</p>
		</li>
	</ul>
</div>`,
      MemberId,
      ProjectId,
      isSent: false,
      isDeleted: false,
      ParentCompanyId,
    };
    await DigestNotification.create(object);
  },
  async updateCompanyHistory(addedCompany, deletedCompany, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Company`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Company`;
    addedCompany.forEach(async (element, i) => {
      const newCompanyData = await Company.findOne({
        where: { id: element.CompanyId },
      });
      if (i === 0) {
        if (i === addedCompany.length - 1) {
          addDesc += ` ${newCompanyData.companyName}`;
          newHistory.description = addDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else {
          addDesc += ` ${newCompanyData.companyName}`;
        }
      } else if (i === addedCompany.length - 1) {
        addDesc += `,${newCompanyData.companyName}`;
        newHistory.description = addDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else {
        addDesc += `,${newCompanyData.companyName}`;
      }
    });
    deletedCompany.forEach(async (element, i) => {
      const newCompanyData = await Company.findOne({
        where: { id: element.CompanyId },
      });
      if (i === 0) {
        if (i === deletedCompany.length - 1) {
          deleteDesc += ` ${newCompanyData.companyName}`;
          newHistory.description = deleteDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else {
          deleteDesc += ` ${newCompanyData.companyName}`;
        }
      } else if (i === deletedCompany.length - 1) {
        deleteDesc += `,${newCompanyData.companyName}`;
        newHistory.description = deleteDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else {
        deleteDesc += `,${newCompanyData.companyName}`;
      }
    });
  },
  async updateEquipmentHistory(addedEquipment, deletedEquipment, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Equipment`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Equipment`;
    addedEquipment.forEach(async (element, i) => {
      const newEquipmentData = await Equipments.findOne({
        where: { id: element.EquipmentId },
      });
      if (i === 0) {
        if (i === addedEquipment.length - 1) {
          addDesc += ` ${newEquipmentData.equipmentName}`;
          newHistory.description = addDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else {
          addDesc += ` ${newEquipmentData.equipmentName}`;
        }
      } else if (i === addedEquipment.length - 1) {
        addDesc += `,${newEquipmentData.equipmentName}`;
        newHistory.description = addDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else {
        addDesc += `,${newEquipmentData.equipmentName}`;
      }
    });
    deletedEquipment.forEach(async (element, i) => {
      const newEquipmentData = await Equipments.findOne({
        where: { id: element.EquipmentId },
      });
      if (i === 0) {
        if (i === deletedEquipment.length - 1) {
          deleteDesc += ` ${newEquipmentData.equipmentName}`;
          newHistory.description = deleteDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else {
          deleteDesc += ` ${newEquipmentData.equipmentName}`;
        }
      } else if (i === deletedEquipment.length - 1) {
        deleteDesc += `,${newEquipmentData.equipmentName}`;
        newHistory.description = deleteDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else {
        deleteDesc += `,${newEquipmentData.equipmentName}`;
      }
    });
  },
  async updatePersonHistory(addedPerson, deletedPerson, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Member`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Member`;
    addedPerson.forEach(async (element, i) => {
      const newMemberData = await Member.findOne({
        where: { id: element.MemberId, isDeleted: false },
        include: [
          {
            association: 'User',
            attributes: ['firstName', 'lastName'],
          },
        ],
      });
      if (i === 0) {
        if (i === addedPerson.length - 1) {
          if (newMemberData.User.firstName) {
            addDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
          }
          newHistory.description = addDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else if (newMemberData.User.firstName) {
          addDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
      } else if (i === addedPerson.length - 1) {
        if (newMemberData.User.firstName) {
          addDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
        newHistory.description = addDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else if (newMemberData.User.firstName) {
        addDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
      }
    });
    deletedPerson.forEach(async (element, i) => {
      const newMemberData = await Member.findOne({
        where: { id: element.MemberId, isDeleted: false },
        include: [
          {
            association: 'User',
            attributes: ['firstName', 'lastName'],
          },
        ],
      });
      if (i === 0) {
        if (i === deletedPerson.length - 1) {
          if (newMemberData.User.firstName) {
            deleteDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
          }
          newHistory.description = deleteDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else if (newMemberData.User.firstName) {
          deleteDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
      } else if (i === deletedPerson.length - 1) {
        if (newMemberData.User.firstName) {
          deleteDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
        newHistory.description = deleteDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else if (newMemberData.User.firstName) {
        deleteDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
      }
    });
  },
  async updateDefineHistory(addedDefine, deletedDefine, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the DFOW`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the DFOW`;
    addedDefine.forEach(async (element, i) => {
      const newDefineData = await DeliverDefineWork.findOne({
        where: { id: element.DeliverDefineWorkId },
      });
      if (i === 0) {
        if (i === addedDefine.length - 1) {
          addDesc += ` ${newDefineData.DFOW}`;
          newHistory.description = addDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else {
          addDesc += ` ${newDefineData.DFOW}`;
        }
      } else if (i === addedDefine.length - 1) {
        addDesc += `,${newDefineData.DFOW}`;
        newHistory.description = addDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else {
        addDesc += `,${newDefineData.DFOW}`;
      }
    });
    deletedDefine.forEach(async (element, i) => {
      const newDefineData = await DeliverDefineWork.findOne({
        where: { id: element.DeliverDefineWorkId },
      });
      if (i === 0) {
        if (i === deletedDefine.length - 1) {
          deleteDesc += ` ${newDefineData.DFOW}`;
          newHistory.description = deleteDesc;
          CraneRequestHistory.createInstance(newHistory);
        } else {
          deleteDesc += ` ${newDefineData.DFOW}`;
        }
      } else if (i === deletedDefine.length - 1) {
        deleteDesc += `,${newDefineData.DFOW}`;
        newHistory.description = deleteDesc;
        CraneRequestHistory.createInstance(newHistory);
      } else {
        deleteDesc += `,${newDefineData.DFOW}`;
      }
    });
  },
  async editMultipleDeliveryRequest(req) {
    try {
      const payload = req.body;
      if (payload.craneRequestIds && payload.craneRequestIds.length > 0) {
        await this.getDynamicModel(req);
        const loginUser = req.user;
        let history = {};
        let memberData = {};
        let addedPerson = [];
        for (let mainIndex = 1; mainIndex <= payload.craneRequestIds.length; mainIndex += 1) {
          const craneRequestId = payload.craneRequestIds[mainIndex - 1];
          if (craneRequestId) {
            let geCraneRequestDetail = {};
            geCraneRequestDetail = await CraneRequest.findOne({
              where: [
                Sequelize.and({
                  id: craneRequestId,
                }),
              ],
            });
            if (geCraneRequestDetail) {
              const condition = Sequelize.and({
                ProjectId: payload.ProjectId,
                CraneRequestId: craneRequestId,
              });
              const updateParam = {
                CraneRequestId: geCraneRequestDetail.id,
                CraneRequestCode: geCraneRequestDetail.craneRequestId,
                ProjectId: geCraneRequestDetail.ProjectId,
                isDeleted: false,
              };
              memberData = await Member.getBy({
                UserId: loginUser.id,
                ProjectId: payload.ProjectId,
              });
              history = {
                CraneRequestId: craneRequestId,
                MemberId: memberData.id,
                type: 'edit',
                description: `${loginUser.firstName} ${loginUser.lastName} Edited this Crane Booking.`,
              };
              if (payload.companies && payload.companies.length > 0) {
                const addedCompany = [];
                const existCompanies = await CraneRequestCompany.findAll({ where: condition });
                const deletedCompany = existCompanies.filter((e) => {
                  return payload.companies.indexOf(e.CompanyId) === -1 && e.isDeleted === false;
                });
                await CraneRequestCompany.update({ isDeleted: true }, { where: condition });
                payload.companies.forEach(async (element, companyIndexValue) => {
                  const index = existCompanies.findIndex((item) => item.CompanyId === element);
                  const companyParam = updateParam;
                  companyParam.CompanyId = element;
                  if (index !== -1) {
                    await CraneRequestCompany.update(companyParam, {
                      where: { id: existCompanies[index].id },
                    });
                    if (existCompanies[index].isDeleted !== false) {
                      addedCompany.push(existCompanies[index]);
                    }
                  } else {
                    const newCompanyData = await CraneRequestCompany.createInstance(companyParam);
                    addedCompany.push(newCompanyData);
                  }
                  if (companyIndexValue === payload.companies.length - 1) {
                    this.updateCompanyHistory(addedCompany, deletedCompany, history, loginUser);
                  }
                });
              }
              if (payload.persons && payload.persons.length > 0) {
                addedPerson = [];
                const existPerson = await CraneRequestResponsiblePerson.findAll({
                  where: condition,
                });
                const deletedPerson = existPerson.filter((e) => {
                  return payload.persons.indexOf(e.MemberId) === -1 && e.isDeleted === false;
                });
                await CraneRequestResponsiblePerson.update(
                  { isDeleted: true },
                  { where: condition },
                );
                payload.persons.forEach(async (element, personIndexValue) => {
                  const index = existPerson.findIndex((item) => item.MemberId === element);
                  const memberParam = updateParam;
                  memberParam.MemberId = element;
                  if (index !== -1) {
                    await CraneRequestResponsiblePerson.update(memberParam, {
                      where: { id: existPerson[index].id },
                    });
                    if (existPerson[index].isDeleted !== false) {
                      addedPerson.push(existPerson[index]);
                    }
                  } else {
                    const newPersonData = await CraneRequestResponsiblePerson.createInstance(
                      memberParam,
                    );
                    addedPerson.push(newPersonData);
                  }
                  if (personIndexValue === payload.persons.length - 1) {
                    this.updatePersonHistory(addedPerson, deletedPerson, history, loginUser);
                  }
                });
              }
              if (payload.define && payload.define.length > 0) {
                const addedDefineData = [];
                const existDefine = await CraneRequestDefinableFeatureOfWork.findAll({
                  where: condition,
                });
                const deletedDefine = existDefine.filter((e) => {
                  return (
                    payload.define.indexOf(e.DeliverDefineWorkId) === -1 && e.isDeleted === false
                  );
                });
                await CraneRequestDefinableFeatureOfWork.update(
                  { isDeleted: true },
                  { where: condition },
                );
                payload.define.forEach(async (element, defineIndexValue) => {
                  const index = existDefine.findIndex(
                    (item) => item.DeliverDefineWorkId === element,
                  );
                  const defineParam = updateParam;
                  defineParam.DeliverDefineWorkId = element;
                  if (index !== -1) {
                    await CraneRequestDefinableFeatureOfWork.update(defineParam, {
                      where: { id: existDefine[index].id },
                    });
                    if (existDefine[index].isDeleted !== false) {
                      addedDefineData.push(existDefine[index]);
                    }
                  } else {
                    const newDefineData = await CraneRequestDefinableFeatureOfWork.createInstance(
                      defineParam,
                    );
                    addedDefineData.push(newDefineData);
                  }
                  if (defineIndexValue === payload.define.length - 1) {
                    this.updateDefineHistory(addedDefineData, deletedDefine, history, loginUser);
                  }
                });
              }
              if (payload.escort) {
                await CraneRequest.update(
                  { escort: payload.escort, status: payload.status ? payload.status : 'Pending' },
                  {
                    where: { id: craneRequestId },
                  },
                );
              }
              if (payload.EquipmentId) {
                const addedEquipment = [];

                const equipments = [payload.EquipmentId];
                await CraneRequest.update(
                  { escort: payload.escort, status: payload.status },
                  {
                    where: { id: craneRequestId },
                  },
                );
                const existEquipment = await CraneRequestEquipment.findAll({ where: condition });
                const deletedEquipment = existEquipment.filter((e) => {
                  return equipments.indexOf(e.EquipmentId) === -1 && e.isDeleted === false;
                });
                await CraneRequestEquipment.update({ isDeleted: true }, { where: condition });
                equipments.forEach(async (element, equipmentIndexValue) => {
                  const index = existEquipment.findIndex((item) => item.EquipmentId === element);
                  const equipmentParam = updateParam;
                  equipmentParam.EquipmentId = element;
                  if (index !== -1) {
                    await CraneRequestEquipment.update(equipmentParam, {
                      where: { id: existEquipment[index].id },
                    });
                    if (existEquipment[index].isDeleted !== false) {
                      addedEquipment.push(existEquipment[index]);
                    }
                  } else {
                    const newEquipmentData = await CraneRequestEquipment.createInstance(
                      equipmentParam,
                    );
                    addedEquipment.push(newEquipmentData);
                  }
                  if (equipmentIndexValue === equipments.length - 1) {
                    this.updateEquipmentHistory(
                      addedEquipment,
                      deletedEquipment,
                      history,
                      loginUser,
                    );
                  }
                });
              }
              if (payload.deliveryStart && payload.deliveryEnd) {
                const startDate = new Date(payload.deliveryStart).getTime();
                const currentDate = new Date().getTime();
                const endDate = new Date(payload.deliveryEnd).getTime();
                if (startDate > currentDate && endDate > currentDate) {
                  const DeliverParam = {
                    status: payload.status,
                    craneDeliveryStart: payload.deliveryStart,
                    craneDeliveryEnd: payload.deliveryEnd,
                  };
                  await CraneRequest.update(DeliverParam, {
                    where: { id: craneRequestId },
                  });
                }
              }

              if (payload.void === true) {
                const existVoid = await VoidList.findOne({
                  where: Sequelize.and({
                    CraneRequestId: craneRequestId,
                  }),
                });
                if (!existVoid) {
                  const voidcreate = {
                    CraneRequestId: craneRequestId,
                    ProjectId: payload.ProjectId,
                    ParentCompanyId: payload.ParentCompanyId,
                  };
                  await VoidList.createInstance(voidcreate);
                  await voidService.craneRequestVoidHistory(existVoid, memberData, loginUser);
                }
              }
              if (payload.status) {
                if (memberData.RoleId === 2 || memberData.RoleId === 1) {
                  const DeliverParam = {};
                  DeliverParam.status = payload.status;
                  DeliverParam.approvedBy = memberData.id;
                  DeliverParam.approved_at = new Date();
                  await CraneRequest.update(DeliverParam, {
                    where: { id: craneRequestId },
                  });
                }
              }
            }
          }
          if (mainIndex === payload.craneRequestIds.length) {
            return { message: 'Success.!' };
          }
        }
      } else {
        return { message: 'Please select Delivery booking to update.!' };
      }
    } catch (e) {
      return e;
    }
  },
  async createCopyofCraneRequest(dataInSeries, payload, dates, loginUser, newRecurrenceId) {
    const eventsArray = [];
    const memberDetails = await Member.getBy({
      UserId: loginUser.id,
      ProjectId: payload.ProjectId,
      isActive: true,
      isDeleted: false,
    });
    const projectDetails = await Project.getProjectAndSettings({
      isDeleted: false,
      id: +payload.ProjectId,
    });
    if (dataInSeries && dataInSeries.recurrence && dataInSeries.recurrence.recurrence) {
      let startDate;
      let endDate;
      if (payload.recurrence && dates && dates.length > 0) {
        startDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          dates[0],
          payload.deliveryStartTime,
          payload.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
        endDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          dates[0],
          payload.deliveryEndTime,
          payload.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
      }
      if (startDate || endDate) {
        throw new Error(
          `Bookings can not be submitted within ${projectDetails.ProjectSettings.deliveryWindowTime} ${projectDetails.ProjectSettings.deliveryWindowTimeUnit} prior to the event`,
        );
      }
      let craneRequestParam = {};
      let lastData = {};
      lastData = await CraneRequest.findOne({
        where: { ProjectId: +memberDetails.ProjectId, isDeleted: false },
        order: [['CraneRequestId', 'DESC']],
      });
      const deliveryRequestList = await DeliveryRequest.findOne({
        where: {
          ProjectId: +memberDetails.ProjectId,
          isDeleted: false,
          isAssociatedWithCraneRequest: true,
        },
        order: [['CraneRequestId', 'DESC']],
      });
      if (deliveryRequestList) {
        if (lastData) {
          if (deliveryRequestList.CraneRequestId > lastData.CraneRequestId) {
            lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
          }
        } else {
          lastData = {};
          lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
        }
      }
      if (lastData) {
        const data = lastData.CraneRequestId;
        lastData.CraneRequestId = 0;
        lastData.CraneRequestId = data + 1;
      } else {
        lastData = {};
        lastData.CraneRequestId = 1;
      }
      let id = 0;
      const newValue = JSON.parse(JSON.stringify(lastData));
      if (newValue && newValue.CraneRequestId !== null && newValue.CraneRequestId !== undefined) {
        id = newValue.CraneRequestId;
      }
      const roleDetails = await Role.getBy('Project Admin');
      const accountRoleDetails = await Role.getBy('Account Admin');
      if (dataInSeries.recurrence.recurrence === 'Daily') {
        let dailyIndex = 0;
        while (dailyIndex < dates.length) {
          const data = dates[dailyIndex];
          if (
            moment(data).isBetween(moment(dates[0]), moment(dates[dates.length - 1]), null, '[]') ||
            moment(data).isSame(dates[0]) ||
            moment(data).isSame(dates[dates.length - 1])
          ) {
            id += 1;
            craneRequestParam = {
              description: payload.description,
              isEscortNeeded: payload.isEscortNeeded,
              additionalNotes: payload.additionalNotes,
              CraneRequestId: id,
              craneDeliveryStart: await concreteRequestService.convertTimezoneToUtc(
                data,
                payload.timezone,
                payload.deliveryStartTime,
              ),
              craneDeliveryEnd: await concreteRequestService.convertTimezoneToUtc(
                data,
                payload.timezone,
                payload.deliveryEndTime,
              ),
              ProjectId: payload.ProjectId,
              createdBy: memberDetails.id,
              isAssociatedWithDeliveryRequest: payload.isAssociatedWithDeliveryRequest,
              pickUpLocation: payload.pickUpLocation,
              dropOffLocation: payload.dropOffLocation,
              recurrenceId: newRecurrenceId,
            };
            if (
              memberDetails.RoleId === roleDetails.id ||
              memberDetails.RoleId === accountRoleDetails.id ||
              memberDetails.isAutoApproveEnabled ||
              projectDetails.ProjectSettings.isAutoApprovalEnabled
            ) {
              craneRequestParam.status = 'Approved';
              craneRequestParam.approvedBy = memberDetails.id;
              craneRequestParam.approved_at = new Date();
            }
            eventsArray.push(craneRequestParam);
            // eslint-disable-next-line no-const-assign
            dailyIndex += +dataInSeries.recurrence.repeatEveryCount;
          }
        }
      }
      if (dataInSeries.recurrence.recurrence === 'Weekly') {
        const startDayWeek = moment(dates[0]).startOf('week');
        const endDayWeek = moment(dates[dates.length - 1]).endOf('week');
        const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
        const totalDaysOfRecurrence = Array.from(range1.by('day'));
        let count;
        let weekIncrement;
        if (+dataInSeries.recurrence.repeatEveryCount > 1) {
          count = +dataInSeries.recurrence.repeatEveryCount - 1;
          weekIncrement = 7;
        } else {
          count = 1;
          weekIncrement = 0;
        }
        for (
          let indexba = 0;
          indexba < totalDaysOfRecurrence.length;
          indexba += weekIncrement * count
        ) {
          const totalLength = indexba + 6;
          for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
            const data = totalDaysOfRecurrence[indexb];
            indexba += 1;
            if (
              data &&
              !moment(data).isBefore(dates[0]) &&
              !moment(data).isAfter(dates[dates.length - 1])
            ) {
              const day = moment(data).format('dddd');
              const indexVal = dataInSeries.recurrence.days.includes(day);
              if (indexVal) {
                id += 1;
                const date = moment(`${data}`).format('MM/DD/YYYY');
                craneRequestParam = {
                  description: payload.description,
                  isEscortNeeded: payload.isEscortNeeded,
                  additionalNotes: payload.additionalNotes,
                  CraneRequestId: id,
                  craneDeliveryStart: await concreteRequestService.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryStartTime,
                  ),
                  craneDeliveryEnd: await concreteRequestService.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryEndTime,
                  ),
                  ProjectId: payload.ProjectId,
                  createdBy: memberDetails.id,
                  isAssociatedWithDeliveryRequest: payload.isAssociatedWithDeliveryRequest,
                  pickUpLocation: payload.pickUpLocation,
                  dropOffLocation: payload.dropOffLocation,
                  recurrenceId: newRecurrenceId,
                };
                if (
                  memberDetails.RoleId === roleDetails.id ||
                  memberDetails.RoleId === accountRoleDetails.id ||
                  memberDetails.isAutoApproveEnabled ||
                  projectDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  craneRequestParam.status = 'Approved';
                  craneRequestParam.approvedBy = memberDetails.id;
                  craneRequestParam.approved_at = new Date();
                }
                eventsArray.push(craneRequestParam);
              }
            }
          }
        }
      }
      if (dataInSeries.recurrence.recurrence === 'Monthly') {
        const startMonth = moment(dates[0]).startOf('month');
        const startMonthNumber = moment(startMonth).format('MM');
        const endMonth = moment(dates[dates.length - 1]).endOf('month');
        const endMonthNumber = moment(endMonth).format('MM');
        let startDate1 = moment(dates[0]);
        const endDate1 = moment(dates[dates.length - 1]).endOf('month');
        const allMonthsInPeriod = [];
        while (startDate1.isBefore(endDate1)) {
          allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
          startDate1 = startDate1.add(1, 'month');
        }
        let currentMonthDates = [];
        let totalNumberOfMonths = endMonthNumber - startMonthNumber;
        if (totalNumberOfMonths < 0) {
          totalNumberOfMonths *= -1;
        }
        let k = 0;
        while (k < allMonthsInPeriod.length + 1) {
          currentMonthDates = Array.from(
            { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
            (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
          );

          if (dataInSeries.recurrence.chosenDateOfMonth) {
            const getDate = currentMonthDates.filter(
              (value) => moment(value).format('DD') === dataInSeries.recurrence.dateOfMonth,
            );
            if (getDate.length === 1) {
              if (
                moment(getDate[0]).isBetween(
                  moment(dates[0]),
                  moment(dates[dates.length - 1]),
                  null,
                  '[]',
                ) ||
                moment(getDate[0]).isSame(dates[0]) ||
                moment(getDate[0]).isSame(dates[dates.length - 1])
              ) {
                id += 1;
                const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                craneRequestParam = {
                  description: payload.description,
                  isEscortNeeded: payload.isEscortNeeded,
                  additionalNotes: payload.additionalNotes,
                  CraneRequestId: id,
                  craneDeliveryStart: await concreteRequestService.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryStartTime,
                  ),
                  craneDeliveryEnd: await concreteRequestService.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryEndTime,
                  ),
                  ProjectId: payload.ProjectId,
                  createdBy: memberDetails.id,
                  isAssociatedWithDeliveryRequest: payload.isAssociatedWithDeliveryRequest,
                  pickUpLocation: payload.pickUpLocation,
                  dropOffLocation: payload.dropOffLocation,
                  recurrenceId: newRecurrenceId,
                };
                if (
                  memberDetails.RoleId === roleDetails.id ||
                  memberDetails.RoleId === accountRoleDetails.id ||
                  memberDetails.isAutoApproveEnabled ||
                  projectDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  craneRequestParam.status = 'Approved';
                  craneRequestParam.approvedBy = memberDetails.id;
                  craneRequestParam.approved_at = new Date();
                }
                eventsArray.push(craneRequestParam);
              }
            }
          } else if (allMonthsInPeriod[k]) {
            const dayOfMonth = dataInSeries.recurrence.monthlyRepeatType;
            const week = dayOfMonth.split(' ')[0].toLowerCase();
            const day = dayOfMonth.split(' ')[1].toLowerCase();
            const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').day(day);
            const getAllDays = [];
            if (chosenDay.date() > 7) chosenDay.add(7, 'd');
            const month = chosenDay.month();
            while (month === chosenDay.month()) {
              getAllDays.push(chosenDay.toString());
              chosenDay.add(7, 'd');
            }
            let i = 0;
            if (week === 'second') {
              i += 1;
            } else if (week === 'third') {
              i += 2;
            } else if (week === 'fourth') {
              i += 3;
            } else if (week === 'last') {
              i = getAllDays.length - 1;
            }
            const finalDay = getAllDays[i];
            if (
              moment(finalDay).isBetween(
                moment(dates[0]),
                moment(dates[dates.length - 1]),
                null,
                '[]',
              ) ||
              moment(finalDay).isSame(dates[0]) ||
              moment(finalDay).isSame(dates[dates.length - 1])
            ) {
              id += 1;
              const date = moment(finalDay).format('MM/DD/YYYY');
              craneRequestParam = {
                description: payload.description,
                isEscortNeeded: payload.isEscortNeeded,
                additionalNotes: payload.additionalNotes,
                CraneRequestId: id,
                craneDeliveryStart: await concreteRequestService.convertTimezoneToUtc(
                  date,
                  payload.timezone,
                  payload.deliveryStartTime,
                ),
                craneDeliveryEnd: await concreteRequestService.convertTimezoneToUtc(
                  date,
                  payload.timezone,
                  payload.deliveryEndTime,
                ),
                ProjectId: payload.ProjectId,
                createdBy: memberDetails.id,
                isAssociatedWithDeliveryRequest: payload.isAssociatedWithDeliveryRequest,
                pickUpLocation: payload.pickUpLocation,
                dropOffLocation: payload.dropOffLocation,
                recurrenceId: newRecurrenceId,
              };
              if (
                memberDetails.RoleId === roleDetails.id ||
                memberDetails.RoleId === accountRoleDetails.id ||
                memberDetails.isAutoApproveEnabled ||
                projectDetails.ProjectSettings.isAutoApprovalEnabled
              ) {
                craneRequestParam.status = 'Approved';
                craneRequestParam.approvedBy = memberDetails.id;
                craneRequestParam.approved_at = new Date();
              }
              eventsArray.push(craneRequestParam);
            }
          }
          k += +dataInSeries.recurrence.repeatEveryCount;
        }
      }
      if (dataInSeries.recurrence.recurrence === 'Yearly') {
        const startMonth = moment(dates[0]).startOf('month');
        const startMonthNumber = moment(startMonth).format('MM');
        const endMonth = moment(dates[dates.length - 1]).endOf('month');
        const endMonthNumber = moment(endMonth).format('MM');
        let startDate1 = moment(dates[0]);
        const endDate1 = moment(dates[dates.length - 1]).endOf('month');
        const allMonthsInPeriod = [];
        while (startDate1.isBefore(endDate1)) {
          allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
          startDate1 = startDate1.add(12, 'month');
        }
        let currentMonthDates = [];
        let totalNumberOfMonths = endMonthNumber - startMonthNumber;
        if (totalNumberOfMonths < 0) {
          totalNumberOfMonths *= -1;
        }
        for (let k = 0; k < allMonthsInPeriod.length + 1; k += 1) {
          currentMonthDates = Array.from(
            { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
            (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
          );
          if (dataInSeries.recurrence.chosenDateOfMonth) {
            const getDate = currentMonthDates.filter(
              (value) => moment(value).format('DD') === dataInSeries.recurrence.dateOfMonth,
            );
            if (getDate.length === 1) {
              if (
                moment(getDate[0]).isBetween(
                  moment(dates[0]),
                  moment(dates[dates.length - 1]),
                  null,
                  '[]',
                ) ||
                moment(getDate[0]).isSame(dates[0]) ||
                moment(getDate[0]).isSame(dates[dates.length - 1])
              ) {
                id += 1;
                const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                craneRequestParam = {
                  description: payload.description,
                  isEscortNeeded: payload.isEscortNeeded,
                  additionalNotes: payload.additionalNotes,
                  CraneRequestId: id,
                  craneDeliveryStart: await concreteRequestService.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryStartTime,
                  ),
                  craneDeliveryEnd: await concreteRequestService.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryEndTime,
                  ),
                  ProjectId: payload.ProjectId,
                  createdBy: memberDetails.id,
                  isAssociatedWithDeliveryRequest: payload.isAssociatedWithDeliveryRequest,
                  pickUpLocation: payload.pickUpLocation,
                  dropOffLocation: payload.dropOffLocation,
                  recurrenceId: newRecurrenceId,
                };
                if (
                  memberDetails.RoleId === roleDetails.id ||
                  memberDetails.RoleId === accountRoleDetails.id ||
                  memberDetails.isAutoApproveEnabled ||
                  projectDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  craneRequestParam.status = 'Approved';
                  craneRequestParam.approvedBy = memberDetails.id;
                  craneRequestParam.approved_at = new Date();
                }
                eventsArray.push(craneRequestParam);
              }
            }
          } else if (allMonthsInPeriod[k]) {
            const dayOfMonth = dataInSeries.recurrence.monthlyRepeatType;
            const week = dayOfMonth.split(' ')[0].toLowerCase();
            const day = dayOfMonth.split(' ')[1].toLowerCase();
            const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').day(day);
            const getAllDays = [];
            if (chosenDay.date() > 7) chosenDay.add(7, 'd');
            const month = chosenDay.month();
            while (month === chosenDay.month()) {
              getAllDays.push(chosenDay.toString());
              chosenDay.add(7, 'd');
            }
            let i = 0;
            if (week === 'second') {
              i += 1;
            } else if (week === 'third') {
              i += 2;
            } else if (week === 'fourth') {
              i += 3;
            } else if (week === 'last') {
              i = getAllDays.length - 1;
            }
            const finalDay = getAllDays[i];
            if (
              moment(finalDay).isBetween(
                moment(dates[0]),
                moment(dates[dates.length - 1]),
                null,
                '[]',
              ) ||
              moment(finalDay).isSame(dates[0]) ||
              moment(finalDay).isSame(dates[dates.length - 1])
            ) {
              id += 1;
              const date = moment(finalDay).format('MM/DD/YYYY');
              craneRequestParam = {
                description: payload.description,
                isEscortNeeded: payload.isEscortNeeded,
                additionalNotes: payload.additionalNotes,
                CraneRequestId: id,
                craneDeliveryStart: await concreteRequestService.convertTimezoneToUtc(
                  date,
                  payload.timezone,
                  payload.deliveryStartTime,
                ),
                craneDeliveryEnd: await concreteRequestService.convertTimezoneToUtc(
                  date,
                  payload.timezone,
                  payload.deliveryEndTime,
                ),
                ProjectId: payload.ProjectId,
                createdBy: memberDetails.id,
                isAssociatedWithDeliveryRequest: payload.isAssociatedWithDeliveryRequest,
                pickUpLocation: payload.pickUpLocation,
                dropOffLocation: payload.dropOffLocation,
                recurrenceId: newRecurrenceId,
              };
              if (
                memberDetails.RoleId === roleDetails.id ||
                memberDetails.RoleId === accountRoleDetails.id ||
                memberDetails.isAutoApproveEnabled ||
                projectDetails.ProjectSettings.isAutoApprovalEnabled
              ) {
                craneRequestParam.status = 'Approved';
                craneRequestParam.approvedBy = memberDetails.id;
                craneRequestParam.approved_at = new Date();
              }
              eventsArray.push(craneRequestParam);
            }
          }
        }
      }
    }

    if (eventsArray.length > 0) {
      for (let i = 0; i < eventsArray.length; i += 1) {
        const newCraneRequestData = await CraneRequest.createInstance(eventsArray[i]);
        const { companies, responsiblePersons, definableFeatureOfWorks } = payload;
        const equipments = [payload.EquipmentId];
        const updateParam = {
          CraneRequestId: newCraneRequestData.id,
          CraneRequestCode: newCraneRequestData.CraneRequestId,
          ProjectId: payload.ProjectId,
        };
        companies.forEach(async (element) => {
          const companyParam = updateParam;
          companyParam.CompanyId = element;
          await CraneRequestCompany.createInstance(companyParam);
        });
        equipments.forEach(async (element) => {
          const equipmentParam = updateParam;
          equipmentParam.EquipmentId = element;
          await CraneRequestEquipment.createInstance(equipmentParam);
        });
        responsiblePersons.forEach(async (element) => {
          const memberParam = updateParam;
          memberParam.MemberId = element;
          await CraneRequestResponsiblePerson.createInstance(memberParam);
        });
        definableFeatureOfWorks.forEach(async (element) => {
          const defineParam = updateParam;
          defineParam.DeliverDefineWorkId = element;
          await CraneRequestDefinableFeatureOfWork.createInstance(defineParam);
        });
        const history = {
          CraneRequestId: newCraneRequestData.id,
          MemberId: memberDetails.id,
          ProjectId: payload.ProjectId,
          isDeleted: false,
          type: 'create',
          description: `${loginUser.firstName} ${loginUser.lastName} Created Crane Booking, ${payload.description}.`,
        };
        await CraneRequestHistory.createInstance(history);
        if (newCraneRequestData.status === 'Approved') {
          const object = {
            ProjectId: payload.ProjectId,
            MemberId: memberDetails.id,
            CraneRequestId: newCraneRequestData.id,
            isDeleted: false,
            type: 'approved',
            description: `${loginUser.firstName} ${loginUser.lastName} Approved Crane Booking, ${payload.description}.`,
          };
          await CraneRequestHistory.createInstance(object);
        }
      }
    }
  },
  async checkDoubleBookingAllowedOrNot(eventsArray, projectDetails, type) {
    if (!projectDetails.ProjectSettings.craneAllowOverlappingBooking) {
      const checkOverlapping = await this.checkCraneConflictsWithAlreadyScheduled(
        eventsArray,
        type,
      );
      if (checkOverlapping) {
        return {
          error: true,
          message:
            'This booking clashes with another booking. Overlapping is disabled by the administrator.',
        };
      }
    }
    if (
      projectDetails.ProjectSettings &&
      !projectDetails.ProjectSettings.craneAllowOverlappingCalenderEvents
    ) {
      const checkCalenderOverlap =
        await concreteRequestService.checkCalenderEventsOverlappingWithBooking(
          eventsArray,
          'crane',
          type,
        );
      if (checkCalenderOverlap) {
        return {
          error: true,
          message:
            'This booking clashes with a scheduled calendar event. Overlapping is disabled by the administrator',
        };
      }
    }
  },
  async checkCraneConflictsWithAlreadyScheduled(craneData, type) {
    const datesStartArr = [];
    const datesEndArr = [];
    const requestIds = [];
    craneData.forEach((data) => {
      datesStartArr.push(new Date(data.craneDeliveryStart));
      datesEndArr.push(new Date(data.craneDeliveryEnd));
      if (type === 'edit' && data.id) {
        requestIds.push(data.id);
      }
    });
    let condition = {
      ProjectId: craneData[0].ProjectId,
      status: {
        [Op.notIn]: ['Completed', 'Expired'],
      },
    };
    if (type === 'edit') {
      condition = {
        ...condition,
        id: {
          [Op.notIn]: requestIds,
        },
      };
    }
    const craneExist = await CraneRequest.findAll({
      where: {
        ...condition,
        [Op.or]: [
          {
            [Op.or]: datesStartArr.map((date) => ({
              craneDeliveryStart: { [Op.lte]: date },
              craneDeliveryEnd: { [Op.gte]: date },
            })),
          },
          {
            [Op.or]: datesEndArr.map((date) => ({
              craneDeliveryStart: { [Op.lte]: date },
              craneDeliveryEnd: { [Op.gte]: date },
            })),
          },
        ],
      },
    });

    const deliveryExist = await DeliveryRequest.findAll({
      where: {
        ProjectId: craneData[0].ProjectId,
        requestType: 'deliveryRequestWithCrane',
        status: {
          [Op.notIn]: ['Delivered', 'Expired'],
        },
        [Op.or]: [
          {
            [Op.or]: datesStartArr.map((date) => ({
              deliveryStart: { [Op.lte]: date },
              deliveryEnd: { [Op.gte]: date },
            })),
          },
          {
            [Op.or]: datesEndArr.map((date) => ({
              deliveryStart: { [Op.lte]: date },
              deliveryEnd: { [Op.gte]: date },
            })),
          },
        ],
      },
    });
    if (craneExist.length || deliveryExist.length) {
      return true;
    }
    return false;
  },
  async upcomingRequestListForMobile(req) {
    try {
      const requestList = [];
      const data = req.query;
      const loginUser = req.user;
      const statusData = await ProjectSettings.getCalendarStatusColor(req.query.ProjectId);
      const cardData = await ProjectSettings.getCalendarCard(req.query.ProjectId);
      const memberDetails = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: data.ProjectId,
          isDeleted: false,
          isActive: true,
        }),
      });
      if (memberDetails) {
        const concreteCondition = {
          isDeleted: false,
          concretePlacementStart: {
            [Op.gt]: new Date(),
          },
        };
        const craneCondition = {
          isDeleted: false,
          craneDeliveryStart: {
            [Op.gt]: new Date(),
          },
        };
        const deliveryCondition = {
          isDeleted: false,
          deliveryStart: {
            [Op.gt]: new Date(),
          },
        };
        const voidDelivery = [];
        const voidConcrete = [];
        const voidCrane = [];
        const voidList = await VoidList.findAll({
          where: {
            ProjectId: +data.ProjectId,
            isDeliveryRequest: true,
            DeliveryRequestId: { [Op.ne]: null },
          },
        });
        voidList.forEach(async (element) => {
          voidDelivery.push(element.DeliveryRequestId);
        });
        deliveryCondition['$DeliveryRequest.id$'] = {
          [Op.and]: [{ [Op.notIn]: voidDelivery }],
        };
        const voidConcreteRequestList = await VoidList.findAll({
          where: {
            ProjectId: +data.ProjectId,
            isDeliveryRequest: false,
            ConcreteRequestId: { [Op.ne]: null },
          },
        });
        voidConcreteRequestList.forEach(async (element) => {
          voidConcrete.push(element.ConcreteRequestId);
        });
        concreteCondition['$ConcreteRequest.id$'] = {
          [Op.and]: [{ [Op.notIn]: voidConcrete }],
        };
        const voidCraneList = await VoidList.findAll({
          where: {
            ProjectId: +data.ProjectId,
            isDeliveryRequest: false,
            CraneRequestId: { [Op.ne]: null },
          },
        });
        voidCraneList.forEach(async (element) => {
          voidCrane.push(element.CraneRequestId);
        });
        craneCondition['$CraneRequest.id$'] = {
          [Op.and]: [{ [Op.notIn]: voidCrane }],
        };
        const concreteRequestList = await ConcreteRequest.upcomingConcreteRequestForMobile(
          concreteCondition,
          data.ProjectId,
        );
        let craneRequestList = await CraneRequest.upcomingCraneRequestForMobile(
          craneCondition,
          data.ProjectId,
        );
        const deliveryRequestList = await DeliveryRequest.upcomingDeliveryRequestForMobile(
          deliveryCondition,
          data.ProjectId,
        );
        if (deliveryRequestList.length > 0) {
          craneRequestList.push(...deliveryRequestList);
        }
        if (craneRequestList.length > 0) {
          craneRequestList.sort((a, b) =>
            (a.craneDeliveryStart ? a.craneDeliveryStart : a.deliveryStart) >
              (b.craneDeliveryStart ? b.craneDeliveryStart : b.deliveryStart)
              ? 1
              : -1,
          );
        }
        craneRequestList = craneRequestList.slice(0, 2);
        requestList.push({
          craneList: craneRequestList,
          concreteList: concreteRequestList,
          cardData,
          statusData,
        });
        return { status: 200, data: requestList };
      }
      return { status: 422, msg: 'Project Id/ParendCompany Id/Member does not exist' };
    } catch (e) {
      console.log(e);
      return e;
    }
  },
};
module.exports = craneRequestService;
