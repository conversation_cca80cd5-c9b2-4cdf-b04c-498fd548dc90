module.exports = (sequelize, DataTypes) => {
  const TimeZone = sequelize.define(
    'TimeZone',
    {
      location: DataTypes.STRING,
      timezone: DataTypes.TEXT,
      timeZoneOffsetInMinutes: DataTypes.STRING,
      isDeleted: DataTypes.BOOLEAN,
      offset: DataTypes.STRING,
      isDayLightSavingEnabled: DataTypes.BOOLEAN,
      dayLightSavingTimeInMinutes: DataTypes.STRING,
    },
    {},
  );
  TimeZone.associate = (models) => {
    TimeZone.hasMany(models.Project);
    TimeZone.hasMany(models.CalendarSetting);
    return TimeZone;
  };
  TimeZone.getAll = async () => {
    const timeZone = await TimeZone.findAll({ where: { isDeleted: false } });
    return timeZone;
  };

  return TimeZone;
};
