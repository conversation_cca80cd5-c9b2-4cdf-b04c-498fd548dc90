module.exports = (sequelize, DataTypes) => {
  const CompanyDefine = sequelize.define(
    'CompanyDefine',
    {
      CompanyId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      DeliverDefineWorkId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ProjectId: DataTypes.INTEGER,
    },
    {},
  );
  CompanyDefine.associate = (models) => {
    // associations can be defined here
    CompanyDefine.belongsTo(models.Company);
    CompanyDefine.belongsTo(models.DeliverDefineWork);
  };
  CompanyDefine.createInstance = async (paramData) => {
    const newCompanyDefine = await CompanyDefine.create(paramData);
    return newCompanyDefine;
  };
  return CompanyDefine;
};
