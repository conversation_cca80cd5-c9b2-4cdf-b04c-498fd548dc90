const status = require('http-status');
const { restrictMailService } = require('../services');
const { RestrictEmail } = require('../models');

const RestrictMailController = {
  async restrictMailList(req, res, next) {
    try {
      const { params } = req;
      const offset = (+params.pageNo - 1) * +params.pageSize;
      const mailList = await RestrictEmail.getMailList(+params.pageSize, offset);
      res.status(status.OK).json({
        message: 'Mail listed Successfully.',
        data: mailList,
      });
    } catch (e) {
      next(e);
    }
  },
  async updateRestrictMail(req, res, next) {
    try {
      await restrictMailService.updateRestrictMail(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Mail Details Updated Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async addRestrictMail(req, res, next) {
    try {
      await restrictMailService.addRestrictMail(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Mail list added Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async deleteRestrictMail(req, res, next) {
    try {
      await restrictMailService.deleteRestrictMail(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Mail list Deleted Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = RestrictMailController;
