const status = require('http-status');
const ApiError = require('../helpers/apiError');
const {
  User,
  StripePlan,
  Sequelize,
  ParentCompany,
  Enterprise,
  RestrictEmail,
  Member,
} = require('../models');
const { UserSerializer } = require('../serializers');
const { authService } = require('../services');
const MAILER = require('../mailer');

const { Op } = Sequelize;

const AuthController = {
  /**
   * Create new user
   * @params {string} req.body.email
   * @params {string} req.body.username
   * @params {string} req.body.phone
   * @params {string} req.body.password
   * @returns {User}
   */
  async register(req, res, next) {
    // await sequelize.transaction(async (t) => {
    const { timezoneoffset } = req.headers;
    const origin = req.get('origin');
    if (origin && origin.indexOf('//') >= 0) {
      const splittedOrigin = origin.split('//');
      let splittedDomainOrigin = splittedOrigin[1].split('.')[0];
      if (splittedDomainOrigin) splittedDomainOrigin = splittedDomainOrigin.split('-');
      if (splittedDomainOrigin.length === 2) {
        const err = new ApiError('Not allowed to register', status.BAD_REQUEST);
        next(err);
      }
    }
    const userData = req.body;
    const userExist = await User.findOne({
      where: {
        [Op.and]: [
          {
            isDeleted: false,
            [Op.or]: Sequelize.or(
              { email: userData.basicDetails.email },
              { phoneNumber: userData.basicDetails.phoneNumber },
            ),
          },
        ],
      },
    });
    if (userExist) {
      const err = new ApiError('Email/Mobile Number already exist', status.BAD_REQUEST);
      next(err);
    } else {
      try {
        const planRecord = await StripePlan.getBy({ id: userData.planData.id });
        userData.planData = planRecord;
        userData.timezoneoffset = timezoneoffset;
        authService.createUser(userData, async (userDetail, error) => {
          if (error) {
            next(error);
          } else {
            res.status(status.CREATED).json({
              message: 'Registered Successfully.',
              user: UserSerializer.serialize(userDetail),
            });
          }
        });
      } catch (err) {
        next(err);
      }
    }
    // });
  },
  async existEmail(req, res, next) {
    // await sequelize.transaction(async (t) => {
    const userData = req.body;
    const email = userData.email.toLowerCase();
    const userExist = await User.findOne({
      where: {
        [Op.and]: [
          {
            isDeleted: false,
            [Op.or]: Sequelize.or(
              Sequelize.where(
                Sequelize.fn('lower', Sequelize.col('email')),
                Sequelize.fn('lower', email),
              ),
              { phoneNumber: userData.phoneNumber },
            ),
          },
        ],
      },
    });
    const checkUserEmail = await User.findOne({
      where: {
        [Op.and]: [
          {
            isDeleted: false,
            email: { [Sequelize.Op.iLike]: `${email}` },
          },
        ],
      },
    });
    if (checkUserEmail) {
      const userProjects = await Member.findAll({
        where: { UserId: userExist.id, isDeleted: false },
        include: [
          {
            association: 'Project',
            where: { isDeleted: false },
            required: true,
          },
        ],
      });
      if (userProjects && userProjects.length === 1 && userProjects[0].status === 'pending') {
        res.status(status.CONFLICT).json({
          status: status.CONFLICT,
          message: `You have already been invited to the ${userProjects[0].Project.projectName}`,
        });
      }
    }
    if (userExist) {
      const err = new ApiError('Email/Mobile Number already exist', status.BAD_REQUEST);
      return next(err);
    }
    let getCompanyDetail = {};
    const firstSplit = userData.email.split('@')[1];
    const secondSplit = firstSplit.split('.');
    let emailDomainName;
    if (secondSplit.length === 2) {
      emailDomainName = firstSplit;
    } else if (secondSplit.length > 2) {
      const str = firstSplit.substring(firstSplit.indexOf('.') + 1);
      emailDomainName = str;
    }
    const restrict = await RestrictEmail.getBy({ domainName: emailDomainName, isActive: true });
    if (restrict) {
      const err = new ApiError(
        'Please use your work email address to register',
        status.BAD_REQUEST,
      );
      return next(err);
    }
    const existParentCompany = await ParentCompany.getBy({ emailDomainName });
    if (existParentCompany) {
      const enterpriseValue = await Enterprise.findOne({
        where: { ParentCompanyId: existParentCompany.id, status: 'completed' },
      });
      getCompanyDetail = await ParentCompany.getCompany({ id: existParentCompany.id });
      if (enterpriseValue) {
        const err = new ApiError(
          'This Email Domain already is in Enterprise level, Please Contact Account Admin',
          status.BAD_REQUEST,
        );
        next(err);
      } else {
        res.status(status.OK).json({ company: getCompanyDetail, message: 'Not exist' });
      }
    } else {
      res.status(status.OK).json({ company: getCompanyDetail, message: 'Not exist' });
    }
    // });
  },

  /**
   * Returns jwt token if valid email and password is provided
   * @params {string} req.body.email
   * @params {string} req.body.password
   * @returns { token }
   */
  async login(req, res, next) {
    try {
      const userData = req;
      await authService.login(userData, async (userDetail, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: `Let's get to work!`,
            token: userDetail.token,
            userDetails: UserSerializer.serialize(userDetail),
          });
        }
      });
    } catch (error) {
      next(error);
    }
  },
  async checkResetToken(req, res, next) {
    try {
      const UserModel = await authService.getDynamicModel(req);
      const data = await UserModel.getBy({ resetPasswordToken: req.params.resetToken });
      if (data) {
        res.status(status.OK).json({
          message: 'Token exist.',
        });
      } else {
        const err = new ApiError('Reset Token expired', status.BAD_REQUEST);
        next(err);
      }
    } catch (e) {
      next(e);
    }
  },
  async adminLogin(req, res, next) {
    try {
      const adminData = req.body;
      authService.adminLogin(adminData, async (adminDetail, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Login Successfully.',
            token: adminDetail.token,
          });
        }
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * if given email is valid then the forget password email will send.
   * @params {string} req.body.email - user email
   * @returns Email will send
   */
  async forgotPassword(req, res, next) {
    const UserModel = await authService.getDynamicModel(req);
    const user = await UserModel.findOne({
      where: {
        [Op.and]: [
          {
            isDeleted: false,
            [Op.and]: Sequelize.and(
              Sequelize.where(
                Sequelize.fn('lower', Sequelize.col('email')),
                Sequelize.fn('lower', req.body.email),
              ),
            ),
          },
        ],
      },
    });
    if (user) {
      try {
        if (
          user.userType === 'user' ||
          user.userType === 'account admin' ||
          user.userType === 'super admin'
        ) {
          authService.forgotPassword(req, user, (userData, error) => {
            if (error) {
              next(error);
            } else {
              res.status(status.OK).json({ message: 'Reset password email sent successfully' });
            }
          });
        } else {
          const error = new ApiError("Email doesn't exist", status.NOT_FOUND);
          next(error);
        }
      } catch (error) {
        next(error);
      }
    } else {
      const error = new ApiError("Email doesn't exist", status.NOT_FOUND);
      next(error);
    }
  },

  /** Reset password with otpCode
   * @params {string} req.body.email
   * @params {string} req.body.otpCode
   * @returns {Success}
   */
  async resetPasswordByEmail(req, res, next) {
    const UserModel = await authService.getDynamicModel(req);
    const user = await UserModel.getBy({
      resetPasswordToken: req.params.reset_password_token,
    });
    if (user) {
      const params = {
        password: req.body.password,
      };
      authService.resetPassword(req, user, params, (success, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({ message: 'Password updated Successfully.' });
        }
      });
    } else {
      const error = new ApiError('Invalid Token', status.BAD_REQUEST);
      next(error);
    }
  },
  async removeFromInvitedProject(req, res) {
    const user = await User.findOne({
      where: {
        [Op.and]: [
          {
            isDeleted: false,
            email: { [Sequelize.Op.iLike]: `${req.body.email}` },
          },
        ],
      },
    });
    if (user) {
      const memberData = await Member.findOne({
        where: { UserId: user.id, isDeleted: false },
      });
      await Member.update({ isDeleted: true }, { where: { id: memberData.id, isDeleted: false } });
      await User.update({ isDeleted: true }, { where: { id: user.id } });
      res
        .status(status.OK)
        .json({ status: 200, message: 'Successfully removed from the already invited project.!' });
    } else {
      res.status(status.OK).json({ status: 404, message: 'Email Id not exist' });
    }
  },
  async getResendInviteLink(req, res, next) {
    const user = await User.findOne({
      where: {
        [Op.and]: [
          {
            isDeleted: false,
            email: { [Sequelize.Op.iLike]: `${req.body.email}` },
          },
        ],
      },
    });
    const data = {
      id: '',
      ParentCompanyId: '',
      email: req.body.email,
      type: '',
    };
    if (user) {
      const memberData = await Member.findOne({
        where: { UserId: user.id, isDeleted: false },
        include: [
          {
            association: 'Role',
          },
        ],
      });
      data.ParentCompanyId = memberData.ParentCompanyId;
      data.type = memberData.Role.roleName;
      data.id = memberData.id;
      await MAILER.sendMail(
        data,
        'invite_member',
        'You were added as a member',
        'Member Onboarded',
        (info, err) => {
          if (err) {
            const newError = new ApiError(err.message, status.BAD_REQUEST);
            return next(null, newError);
          }
          res.status(status.OK).json({
            status: 200,
            message: 'Invite Link sent successfully.!',
          });
        },
      );
    } else {
      res.status(status.OK).json({ status: 404, message: 'Email Id not exist' });
    }
  },
};

module.exports = AuthController;
