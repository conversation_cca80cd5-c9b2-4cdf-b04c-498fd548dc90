const { ExportToCsv } = require('export-to-csv');
const moment = require('moment');
const awsConfig = require('../middlewares/awsConfig');

const csvConcreteReportService = {
  async exportConcreteReportInCsvFormat(
    data,
    selectedHeaders,
    timezoneoffset,
    fileName,
    exportType,
    done,
  ) {
    /* Column headers */
    const rowValues = [];
    const columns = [];
    let isIdSelected = false;
    let isDescriptionSelected = false;
    let isDateSelected = false;
    let isStatusSelected = false;
    let isApprovedBySelected = false;
    let isCompanySelected = false;
    let isOrderNumberSelected = false;
    let isSlumpSelected = false;
    let isTruckSpacingSelected = false;
    let isPrimerOrderedSelected = false;
    let isPersonSelected = false;
    let isQuantityOrderedSelected = false;
    let isMixDesignSelected = false;
    selectedHeaders.map((object) => {
      if (object.isActive === true) {
        rowValues.push(object.title);
        if (object.key === 'id') {
          columns.push({ key: object.key, width: 5 });
        } else {
          columns.push({ key: object.key, width: 32 });
        }
        if (object.key === 'id') isIdSelected = true;
        if (object.key === 'description') isDescriptionSelected = true;
        if (object.key === 'date') isDateSelected = true;
        if (object.key === 'status') isStatusSelected = true;
        if (object.key === 'approvedby') isApprovedBySelected = true;
        if (object.key === 'company') isCompanySelected = true;
        if (object.key === 'orderNumber') isOrderNumberSelected = true;
        if (object.key === 'slump') isSlumpSelected = true;
        if (object.key === 'truckSpacing') isTruckSpacingSelected = true;
        if (object.key === 'primer') isPrimerOrderedSelected = true;
        if (object.key === 'name') isPersonSelected = true;
        if (object.key === 'quantity') isQuantityOrderedSelected = true;
        if (object.key === 'mixDesign') isMixDesignSelected = true;
      }
      return object;
    });
    const values = [];
    for (let index = 0; index < data.length; index += 1) {
      const object = {};
      if (isIdSelected) {
        object.Id = data[index].ConcreteRequestId;
      }
      if (isDescriptionSelected) {
        object.Description = data[index].description;
      }
      if (isDateSelected) {
        object['Date & Time'] = moment(
          new Date(
            `${moment(data[index].concretePlacementStart).format('MM/DD/YYYY')} ${moment(
              data[index].concretePlacementStart,
            ).format('hh:mm a')} `,
          ),
        )
          .add(Number(timezoneoffset), 'm')
          .format('MMM-DD-YYYY hh:mm a');
      }
      if (isStatusSelected) {
        object.Status = data[index].status;
      }
      if (isApprovedBySelected) {
        object['Approved By'] =
          data[index].approverDetails && data[index].approverDetails.User.firstName
            ? `${data[index].approverDetails.User.firstName} ${data[index].approverDetails.User.lastName} `
            : '-';
      }
      if (isCompanySelected) {
        let company = null;
        if (data[index].concreteSupplierDetails && data[index].concreteSupplierDetails.length > 0) {
          const companyValues = [];
          for (let m = 0; m < data[index].concreteSupplierDetails.length; m += 1) {
            if (data[index].concreteSupplierDetails && data[index].concreteSupplierDetails[m]) {
              companyValues.push(data[index].concreteSupplierDetails[m].Company.companyName);
            }
          }
          company = companyValues.join(', ');
        }
        object['Concrete Supplier'] = company || '-';
      }
      if (isOrderNumberSelected) {
        object['Order Number'] = data[index].concreteOrderNumber
          ? data[index].concreteOrderNumber
          : '-';
      }
      if (isSlumpSelected) {
        object.Slump = data[index].slump ? data[index].slump : '-';
      }
      if (isTruckSpacingSelected) {
        object['Truck Spacing'] = data[index].truckSpacingHours
          ? data[index].truckSpacingHours
          : '-';
      }
      if (isPrimerOrderedSelected) {
        object['Primer Ordered'] = data[index].primerForPump ? data[index].primerForPump : '-';
      }

      if (isPersonSelected) {
        let member;
        if (data[index].memberDetails && data[index].memberDetails.length > 0) {
          const memberValues = [];
          for (let m = 0; m < data[index].memberDetails.length; m += 1) {
            if (
              data[index].memberDetails &&
              data[index].memberDetails[m] &&
              data[index].memberDetails[m].Member &&
              data[index].memberDetails[m].Member.User
            ) {
              memberValues.push(
                `${data[index].memberDetails[m].Member.User.firstName} ${data[index].memberDetails[m].Member.User.lastName} `,
              );
            }
          }
          member = memberValues.join(', ');
        }
        object['Responsible Person'] = member || '-';
      }
      if (isQuantityOrderedSelected) {
        object['Quantity Ordered'] = data[index].concreteQuantityOrdered
          ? data[index].concreteQuantityOrdered
          : '-';
      }
      if (isMixDesignSelected) {
        let mixDesign = null;
        if (data[index].mixDesignDetails && data[index].mixDesignDetails.length > 0) {
          const mixDesignValues = [];
          for (let m = 0; m < data[index].mixDesignDetails.length; m += 1) {
            if (data[index].mixDesignDetails && data[index].mixDesignDetails[m]) {
              mixDesignValues.push(data[index].mixDesignDetails[m].ConcreteMixDesign.mixDesign);
            }
          }
          mixDesign = mixDesignValues.join(', ');
        }
        object['Mix Design'] = mixDesign || '-';
      }
      values.push(object);
    }
    const options = {
      showLabels: true,
      showTitle: false,
      useTextFile: false,
      useBom: false,
      useKeysAsHeaders: true,
    };

    const csvExporter = new ExportToCsv(options);
    const csvFile = await csvExporter.generateCsv(values, true);
    if (csvFile) {
      const buffer = Buffer.from(csvFile, 'utf-8');
      awsConfig.reportUpload(buffer, fileName, exportType, async (result, error1) => {
        if (!error1) {
          return done(result, false);
        }
        return done(null, { message: 'cannot export document' });
      });
    }
  },
};
module.exports = csvConcreteReportService;
