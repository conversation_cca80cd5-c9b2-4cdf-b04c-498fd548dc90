const Joi = require('joi');

const craneRequestValidation = {
  craneRequest: {
    body: Joi.object({
      description: Joi.string().min(3).required(),
      companies: Joi.array().min(1).required(),
      isEscortNeeded: Joi.boolean().required(),
      ProjectId: Joi.number().required(),
      additionalNotes: Joi.optional().allow(''),
      EquipmentId: Joi.array().min(1).required(),
      LocationId: Joi.number().required(),
      definableFeatureOfWorks: Joi.array().required(),
      craneDeliveryStart: Joi.date().required(),
      craneDeliveryEnd: Joi.date().required(),
      ParentCompanyId: Joi.any(),
      responsiblePersons: Joi.array().min(1).required(),
      isAssociatedWithDeliveryRequest: Joi.boolean().required(),
      pickUpLocation: Joi.string().required(),
      dropOffLocation: Joi.string().required(),
      recurrence: Joi.string().valid('Does Not Repeat', 'Daily', 'Weekly', 'Monthly', 'Yearly'),
      repeatEveryCount: Joi.when('recurrence', {
        is: Joi.string().valid('Daily', 'Weekly', 'Monthly'),
        then: Joi.string().min(1).required(),
        otherwise: Joi.string().allow('', null),
      }),
      repeatEveryType: Joi.when('recurrence', {
        is: Joi.string().valid('Daily', 'Weekly', 'Monthly'),
        then: Joi.string().min(1).required(),
        otherwise: Joi.string().allow('', null),
      }),
      days: Joi.when('recurrence', {
        is: Joi.string().valid('Daily', 'Weekly'),
        then: Joi.array().min(1).required(),
        otherwise: Joi.array().allow('', null),
      }),
      chosenDateOfMonth: Joi.when('recurrence', {
        is: Joi.string().valid('Monthly', 'Yearly'),
        then: Joi.boolean().required(),
        otherwise: Joi.boolean().allow('', null),
      }),
      dateOfMonth: Joi.when('recurrence', {
        is: Joi.string().valid('Monthly', 'Yearly'),
        then: Joi.when('chosenDateOfMonth', {
          is: Joi.boolean().valid(true),
          then: Joi.string().min(1).required(),
          otherwise: Joi.string().allow('', null),
        }),
        otherwise: Joi.string().allow('', null),
      }),
      monthlyRepeatType: Joi.when('recurrence', {
        is: Joi.string().valid('Monthly', 'Yearly'),
        then: Joi.when('chosenDateOfMonth', {
          is: Joi.string().valid(false),
          then: Joi.string().min(1).required(),
          otherwise: Joi.string().allow('', null),
        }),
        otherwise: Joi.string().allow('', null),
      }),
      TimeZoneId: Joi.number().required(),
      endPicker: Joi.string().allow('', null),
      startPicker: Joi.string().allow('', null),
    }),
  },
  editCraneRequest: {
    body: Joi.object({
      id: Joi.number().required(),
      description: Joi.string().min(3).required(),
      CraneRequestId: Joi.number(),
      companies: Joi.array().min(1).required(),
      isEscortNeeded: Joi.boolean().required(),
      ProjectId: Joi.number().required(),
      additionalNotes: Joi.optional().allow(''),
      EquipmentId: Joi.array().min(1).required(),
      LocationId: Joi.number().required(),
      definableFeatureOfWorks: Joi.array().required(),
      craneDeliveryStart: Joi.date().required(),
      craneDeliveryEnd: Joi.date().required(),
      responsiblePersons: Joi.array().min(1).required(),
      ParentCompanyId: Joi.any(),
      isAssociatedWithDeliveryRequest: Joi.boolean().required(),
      pickUpLocation: Joi.string().required(),
      dropOffLocation: Joi.string().required(),
      recurrenceId: Joi.number().optional().allow('', null),
      recurrenceEndDate: Joi.date().optional().allow('', null),
      previousSeriesRecurrenceEndDate: Joi.date().required().allow('', null),
      nextSeriesRecurrenceStartDate: Joi.date().required().allow('', null),
      recurrenceSeriesStartDate: Joi.date().required().allow('', null),
      recurrenceSeriesEndDate: Joi.date().required().allow('', null),
      seriesOption: Joi.number().required(),
      deliveryStartTime: Joi.string().required(),
      deliveryEndTime: Joi.string().required(),
      timezone: Joi.string().required(),
    }),
  },
  getSingleCraneRequest: {
    params: Joi.object({
      CraneRequestId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
      ProjectId: Joi.any(),
    }),
  },
  updateNDRStatus: {
    body: Joi.object({
      id: Joi.number().required(),
      status: Joi.string().required(),
      ParentCompanyId: Joi.any(),
      statuschange: Joi.string(),
    }),
  },
  upcomingRequestList: {
    query: Joi.object({
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
    }),
  },
};
module.exports = craneRequestValidation;
