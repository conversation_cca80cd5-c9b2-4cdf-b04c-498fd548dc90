const Joi = require('joi');

const voidValidation = {
  createVoidList: {
    body: Joi.object({
      ProjectId: Joi.number().required(),
      DeliveryRequestId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  createCraneRequestVoidList: {
    body: Joi.object({
      ProjectId: Joi.number().required(),
      CraneRequestId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  createConcreteRequestVoidList: {
    body: Joi.object({
      ProjectId: Joi.number().required(),
      ConcreteRequestId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  removeVoidList: {
    body: Joi.object({
      id: Joi.array(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
      isSelectAll: Joi.boolean().required(),
    }),
  },
};
module.exports = voidValidation;
