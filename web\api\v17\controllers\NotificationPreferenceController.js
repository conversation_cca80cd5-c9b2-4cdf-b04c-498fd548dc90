/* eslint-disable no-await-in-loop */
const status = require('http-status');
const { notificationPreferenceService } = require('../services');
const {
  NotificationPreferenceItem,
  NotificationPreference,
  Member,
  Project,
} = require('../models');

const NotificationPreferenceController = {
  async setNotificationPreference(req, res, next) {
    notificationPreferenceService.setNotificationPreference(req, async (notification, error) => {
      if (error) {
        return next(error);
      }
      res.status(status.OK).json({
        message: 'Notification Preference Updated Successfully.',
        data: notification,
      });
    });
  },
  async listNotificationPreference(req, res, next) {
    notificationPreferenceService.listNotificationPreference(req, async (notification, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.OK).json({
          message: 'Notification Preference Listed Successfully',
          data: notification,
        });
      }
    });
  },
  async notificationPreferenceItems(req, res, next) {
    notificationPreferenceService.notificationPreferenceItems(req, async (notification, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.OK).json({
          message: 'Notification Preference Items Listed Successfully',
          data: notification,
        });
      }
    });
  },
  async addNotificationPreferenceToAllMembers(req, res) {
    const getNotificationPreferenceItemsList = await NotificationPreferenceItem.findAll({
      where: { isDeleted: false },
    });
    const projectList = await Project.findAll({
      where: { isDeleted: false },
    });
    for (let index = 0; index < projectList.length; index += 1) {
      const project = projectList[index];
      const membersList = await Member.findAll({
        where: { isDeleted: false, ProjectId: +project.id, status: 'completed' },
      });
      for (let index1 = 0; index1 < membersList.length; index1 += 1) {
        const member = membersList[index1];
        const getProject = await Project.findOne({
          where: {
            id: +member.ProjectId,
            isDeleted: false,
          },
          include: [
            {
              where: { isDeleted: false },
              association: 'TimeZone',
              required: false,
              attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
            },
          ],
        });
        const attr = {
          time: '05:00',
          timeFormat: 'AM',
        };
        let projectObject;
        if (getProject) {
          projectObject = getProject.toJSON();
        }
        if (projectObject && projectObject.TimeZone) {
          attr.TimeZoneId = projectObject.TimeZone.id;
        } else {
          attr.TimeZoneId = 3;
        }
        await Member.update(attr, { where: { id: member.id } });
        const memberData = member;
        for (let index2 = 0; index2 < getNotificationPreferenceItemsList.length; index2 += 1) {
          const item = getNotificationPreferenceItemsList[index2];
          if (
            (item.id === 7 &&
              item.description === 'When a comment is added to a delivery/crane/concrete request' &&
              item.itemId === 4 &&
              item.emailNotification === true &&
              item.inappNotification === false &&
              item.isDeleted === false) ||
            item.inappNotification === true
          ) {
            const object = {
              MemberId: memberData.id,
              ProjectId: memberData.ProjectId,
              ParentCompanyId: memberData.ParentCompanyId,
              NotificationPreferenceItemId: item.id,
              instant: true,
              dailyDigest: false,
              isDeleted: false,
            };
            await NotificationPreference.createInstance(object);
          } else {
            const object = {
              MemberId: memberData.id,
              ProjectId: memberData.ProjectId,
              ParentCompanyId: memberData.ParentCompanyId,
              NotificationPreferenceItemId: item.id,
              instant: false,
              dailyDigest: true,
              isDeleted: false,
            };
            await NotificationPreference.createInstance(object);
          }
        }
      }
      if (+projectList.length === index + 1) {
        res.status(status.OK).json({
          message: 'Success',
        });
      }
    }
  },
  async addNotificationPreferenceToAMember(req, res) {
    const getNotificationPreferenceItemsList = await NotificationPreferenceItem.findAll({
      where: { isDeleted: false },
    });
    const memberData = await Member.findOne({
      where: {
        isDeleted: false,
        id: +req.body.MemberId,
        ProjectId: +req.body.ProjectId,
        ParentCompanyId: +req.body.ParentCompanyId,
      },
    });
    const getProject = await Project.findOne({
      where: {
        isDeleted: false,
        id: +memberData.ProjectId,
      },
      include: [
        {
          where: { isDeleted: false },
          association: 'TimeZone',
          required: false,
          attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
        },
      ],
    });
    const attr = {
      time: '05:00',
      timeFormat: 'AM',
    };
    let projectObject;
    if (getProject) {
      projectObject = getProject.toJSON();
    }
    if (projectObject && projectObject.TimeZone) {
      attr.TimeZoneId = projectObject.TimeZone.id;
    } else {
      attr.TimeZoneId = 3;
    }
    await Member.update(attr, { where: { id: memberData.id } });
    getNotificationPreferenceItemsList.map(async (item) => {
      if (
        (item.id === 7 &&
          item.description === 'When a comment is added to a delivery/crane/concrete request' &&
          item.itemId === 4 &&
          item.emailNotification === true &&
          item.inappNotification === false &&
          item.isDeleted === false) ||
        item.inappNotification === true
      ) {
        const object = {
          MemberId: memberData.id,
          ProjectId: memberData.ProjectId,
          ParentCompanyId: memberData.ParentCompanyId,
          NotificationPreferenceItemId: item.id,
          instant: true,
          dailyDigest: false,
          isDeleted: false,
        };
        await NotificationPreference.createInstance(object);
      } else {
        const object = {
          MemberId: memberData.id,
          ProjectId: memberData.ProjectId,
          ParentCompanyId: memberData.ParentCompanyId,
          NotificationPreferenceItemId: item.id,
          instant: false,
          dailyDigest: true,
          isDeleted: false,
        };
        await NotificationPreference.createInstance(object);
      }
    });
    res.status(status.OK).json({
      message: 'Success',
    });
  },
  async removeNotificationPreferenceForDeactivatedMembers(req, res) {
    const deactivatedMembersList = await Member.findAll({
      where: {
        isActive: false,
        isDeleted: false,
      },
    });
    deactivatedMembersList.forEach(async (element) => {
      await NotificationPreference.update(
        { instant: false, dailyDigest: false },
        {
          where: {
            MemberId: +element.id,
            ProjectId: +element.ProjectId,
            ParentCompanyId: +element.ParentCompanyId,
          },
        },
      );
    });
    res.status(status.OK).json({
      message: 'Success',
    });
  },
  async addOneNotificationPreferenceToMembers(req, res) {
    const getANotificationPreferenceItem = await NotificationPreferenceItem.findOne({
      where: { isDeleted: false, id: 12 },
    });
    const projectList = await Project.findAll({
      where: { isDeleted: false },
    });
    for (let index = 0; index < projectList.length; index += 1) {
      const project = projectList[index];
      const membersList = await Member.findAll({
        where: { isDeleted: false, ProjectId: +project.id, status: 'completed' },
      });
      for (let index1 = 0; index1 < membersList.length; index1 += 1) {
        const memberData = membersList[index1];
        const object = {
          MemberId: memberData.id,
          ProjectId: memberData.ProjectId,
          ParentCompanyId: memberData.ParentCompanyId,
          NotificationPreferenceItemId: getANotificationPreferenceItem.id,
          instant: true,
          dailyDigest: false,
          isDeleted: false,
        };
        await NotificationPreference.createInstance(object);
      }
      if (+projectList.length === index + 1) {
        res.status(status.OK).json({
          message: 'Success',
        });
      }
    }
  }
};
module.exports = NotificationPreferenceController;
