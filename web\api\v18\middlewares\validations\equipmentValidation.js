const Joi = require('joi');

const equipmentValidation = {
  addEquipment: {
    body: Joi.object({
      equipmentName: Joi.string().min(3).required(),
      createdBy: Joi.number(),
      ProjectId: Joi.number().required(),
      controlledBy: Joi.number().required(),
      PresetEquipmentTypeId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
  equipmentDetail: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
    body: Joi.object({
      idFilter: Joi.optional().allow(''),
      memberFilter: Joi.number(),
      typeFilter: Joi.string().allow('', null),
      nameFilter: Joi.optional().allow(''),
      companyNameFilter: Joi.optional().allow(''),
      search: Joi.optional().allow(''),
      ParentCompanyId: Joi.any(),
      sort: Joi.any().optional().allow('', null),
      sortByField: Joi.any().optional().allow('', null),
      isFilter: Joi.boolean().required(),
      showActivatedAlone: Joi.boolean().required(),
    }),
  },
  craneEquipmentDetail: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
      pageSize: Joi.number().required(),
      pageNo: Joi.number().required(),
      ParentCompanyId: Joi.any(),
    }),
    body: Joi.object({
      idFilter: Joi.optional().allow(''),
      memberFilter: Joi.number(),
      typeFilter: Joi.string().allow('', null),
      nameFilter: Joi.optional().allow(''),
      companyNameFilter: Joi.optional().allow(''),
      search: Joi.optional().allow(''),
      ParentCompanyId: Joi.any(),
      sort: Joi.any().optional().allow('', null),
      sortByField: Joi.any().optional().allow('', null),
      showActivatedAlone: Joi.boolean().required(),
    }),
  },
  listEquipmentType: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
    }),
    body: Joi.object({
      ParentCompanyId: Joi.any(),
    }),
  },
  updateEquipment: {
    body: Joi.object({
      id: Joi.number().required(),
      ProjectId: Joi.number().required(),
      equipmentName: Joi.string().required(),
      PresetEquipmentTypeId: Joi.number().optional(),
      controlledBy: Joi.number().optional(),
      ParentCompanyId: Joi.any(),
      isActive: Joi.boolean().optional(),
    }),
  },
  deleteEquipment: {
    body: Joi.object({
      id: Joi.array(),
      ProjectId: Joi.number().required(),
      isSelectAll: Joi.boolean().required(),
      ParentCompanyId: Joi.any(),
    }),
  },
};
module.exports = equipmentValidation;
