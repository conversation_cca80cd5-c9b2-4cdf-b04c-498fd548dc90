const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');

const cloudFrontURl = process.env.CLOUD_FRONT_URL;

const s3Client = new AWS.S3({
  accessKeyId: process.env.ACCESS_KEY_ID,
  secretAccessKey: process.env.SECRET_ACCESS_KEY,
  region: process.env.REGION,
});

const awsConfig = {
  async upload(fileList, done) {
    const uploadParams = {
      Bucket: process.env.BUCKET,
      Key: '',
      Body: null,
    };
    const responseData = [];
    fileList.forEach((element) => {
      let fileExtension;
      let name;
      if (element.originalname !== undefined) {
        fileExtension = element.originalname.replace(/^.*\./, '');
        name = element.originalname;
      } else {
        fileExtension = element.name.replace(/^.*\./, '');
        name = element.name;
      }
      const uniquename = uuidv4();
      const filename = `${uniquename}.${fileExtension}`;
      uploadParams.Key = filename;
      uploadParams.Body = element.buffer;
      uploadParams.ContentDisposition = `attachment; filename=${name}`;
      s3Client.upload(uploadParams, (err, data) => {
        if (err) {
          done(null, err);
        }
        const newData = data;
        newData.Location = cloudFrontURl + filename;
        responseData.push(newData);
        if (responseData.length === fileList.length) {
          done(responseData, false);
        }
      });
    });
  },
  async singleUpload(fileList, done) {
    const uploadParams = {
      Bucket: process.env.BUCKET,
      Key: '',
      Body: null,
    };
    let file;
    if (fileList.file !== undefined) {
      file = fileList.file;
    } else {
      file = fileList.files;
    }
    const responseData = [];
    const element = file;
    let fileExtension;
    let name;
    if (element.originalname !== undefined) {
      fileExtension = element.originalname.replace(/^.*\./, '');
      name = element.originalname;
    } else {
      fileExtension = element.name.replace(/^.*\./, '');
      name = element.name;
    }
    const uniquename = uuidv4();
    const filename = `${uniquename}.${fileExtension}`;
    uploadParams.Key = filename;
    uploadParams.Body = element.buffer;
    uploadParams.ContentDisposition = `attachment; filename=${name}`;

    s3Client.upload(uploadParams, (err, data) => {
      if (err) {
        return done(null, err);
      }
      const newData = data;
      newData.Location = cloudFrontURl + filename;
      responseData.push(newData);
      done(responseData, false);
    });
  },
  async reportUpload(buffer, name, extension, done) {
    const uploadParams = {
      Bucket: process.env.BUCKET,
      Key: '',
      Body: null,
    };
    const fileExtension = extension.toLowerCase();
    const uniquename = uuidv4();
    const filename = `${uniquename}.${fileExtension}`;
    uploadParams.Key = filename;
    uploadParams.Body = buffer;
    uploadParams.ContentDisposition = `attachment; filename=${name}.${fileExtension}`;
    s3Client.upload(uploadParams, (err, data) => {
      if (err) {
        return done(null, err);
      }
      if (data) {
        const response = cloudFrontURl + filename;
        done(response, false);
      }
    });
  },
  async uploadToS3(buffer, name, extension) {
    const uploadParams = {
      Bucket: process.env.BUCKET,
      Key: '',
      Body: null,
    };
    return new Promise((res, rej) => {
      try {
        const fileExtension = extension.toLowerCase();
        const fileName = `schedule_reports/${name}.${fileExtension}`;
        uploadParams.Key = fileName;
        uploadParams.Body = buffer;
        s3Client.upload(uploadParams, (err, data) => {
          if (err) {
            rej(e);
          }
          if (data) {
            const response = cloudFrontURl + fileName;
            console.log('********response********', response);
            res(response);
          }
        });
      } catch (e) {
        rej(e);
      }
    });
  },
  async logisticPlanUpload(fileList, done) {
    const uploadParams = {
      Bucket: process.env.BUCKET,
      Key: '',
      Body: null,
    };
    let file;
    if (fileList.file !== undefined) {
      file = fileList.file;
    } else {
      file = fileList.files;
    }
    const responseData = [];
    const element = file;
    let fileExtension;
    let name;
    if (element.originalname !== undefined) {
      fileExtension = element.originalname.replace(/^.*\./, '');
      name = element.originalname;
    } else {
      fileExtension = element.name.replace(/^.*\./, '');
      name = element.name;
    }
    const uniquename = uuidv4();
    const filename = `${uniquename}.${fileExtension}`;
    uploadParams.Key = filename;
    uploadParams.Body = element.buffer;
    uploadParams.ContentDisposition = `inline; filename=${name}`;
    let contentType;

    switch (fileExtension.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
        contentType = 'image/jpeg';
        break;
      case 'png':
        contentType = 'image/png';
        break;
      case 'pdf':
        contentType = 'application/pdf';
        break;
      default:
        break;
    }

    uploadParams.ContentType = contentType;
    s3Client.upload(uploadParams, (err, data) => {
      if (err) {
        return done(null, err);
      }
      const newData = data;
      newData.fileLink = cloudFrontURl + filename;
      newData.fileName = element.originalname;
      responseData.push(newData);
      done(responseData, false);
    });
  },
  async sitePlanUpload(file, done) {
    const uploadParams = {
      Bucket: process.env.BUCKET,
      Key: '',
      Body: null,
    };
    const responseData = [];
    const element = file;
    let fileExtension;
    let name;
    if (element.originalname !== undefined) {
      fileExtension = element.originalname.replace(/^.*\./, '');
      name = element.originalname;
    } else {
      fileExtension = element.name.replace(/^.*\./, '');
      name = element.name;
    }
    const uniquename = uuidv4();
    const filename = `${uniquename}.${fileExtension}`;
    uploadParams.Key = filename;
    uploadParams.Body = element.buffer;
    uploadParams.ContentDisposition = `inline; filename=${name}`;
    let contentType;

    switch (fileExtension.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
        contentType = 'image/jpeg';
        break;
      case 'png':
        contentType = 'image/png';
        break;
      case 'pdf':
        contentType = 'application/pdf';
        break;
      default:
        break;
    }

    uploadParams.ContentType = contentType;
    s3Client.upload(uploadParams, (err, data) => {
      if (err) {
        return done(null, err);
      }
      const newData = data;
      newData.fileLink = cloudFrontURl + filename;
      newData.fileName = element.originalname;
      responseData.push(newData);
      done(responseData, false);
    });
  },
};

module.exports = awsConfig;
