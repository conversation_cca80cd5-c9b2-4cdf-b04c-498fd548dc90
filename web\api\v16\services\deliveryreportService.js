const moment = require('moment');
const _ = require('lodash');
const httpStatus = require('http-status');
const cron = require('node-cron');
const pdfHeatMapService = require('./pdfHeatMapService');
const { Sequelize, sequelize, Enterprise } = require('../models');
const {
  DeliveryPerson,
  VoidList,
  SchedulerReport,
  CraneRequest,
  ConcreteRequest,
} = require('../models');
let { DeliveryRequest, Member, DeliverGate, DeliverCompany, User } = require('../models');
const helper = require('../helpers/domainHelper');
const {
  queryBuilderExternal,
  replacementsBuilderExternal,
  defaultTimeSlots,
} = require('../helpers/queryBuilderExternal');
const exportService = require('./exportService');
const pdfDeliveryReportService = require('./pdfDeliveryReportService');
const csvDeliveryReportService = require('./csvDeliveryReportService');
const calendarSettingsService = require('./calendarSettingsService');
const excelDeliveryReportService = require('./excelDeliveryReportService');
const excelWeeklyCalendarService = require('./excelWeeklyCalendarService');
const ApiError = require('../helpers/apiError');
const awsConfig = require('../middlewares/awsConfig');
const puppeteerService = require('./puppeteerService');

let publicUser;
let publicMember;
const { Op } = Sequelize;
const deliveryReportService = {
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const incomeData = inputData;
    let enterpriseValue;
    let ProjectId;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    DeliveryRequest = modelObj.DeliveryRequest;
    Member = modelObj.Member;
    DeliverGate = modelObj.DeliverGate;
    DeliverCompany = modelObj.DeliverCompany;
    User = modelObj.User;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return ProjectId;
  },
  async listDeliveryRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      let { sort } = inputData.body;
      let { sortByField } = inputData.body;
      let order;
      let searchCondition = {};
      if (params.void >= 1 && params.void <= 0) {
        done(null, { message: 'Please enter void as 1 or 0' });
      } else {
        const memberDetails = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: params.ProjectId,
            isDeleted: false,
            isActive: true,
          }),
        });
        if (memberDetails) {
          const voidDelivery = [];
          const voidList = await VoidList.findAll({
            where: {
              ProjectId: params.ProjectId,
              isDeliveryRequest: true,
              DeliveryRequestId: { [Op.ne]: null },
            },
          });
          voidList.forEach(async (element) => {
            voidDelivery.push(element.DeliveryRequestId);
          });
          const offset = (+params.pageNo - 1) * +params.pageSize;
          const condition = {
            ProjectId: +params.ProjectId,
            isQueued: false,
          };
          if (params.void === '0' || params.void === 0) {
            condition['$DeliveryRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidDelivery }],
            };
          } else {
            condition['$DeliveryRequest.id$'] = {
              [Op.and]: [{ [Op.in]: voidDelivery }],
            };
          }
          if (incomeData.descriptionFilter) {
            condition.description = {
              [Sequelize.Op.iLike]: `%${incomeData.descriptionFilter}%`,
            };
          }
          if (incomeData.pickFrom) {
            condition.cranePickUpLocation = {
              [Sequelize.Op.iLike]: `%${incomeData.pickFrom}%`,
            };
          }
          if (incomeData.pickTo) {
            condition.craneDropOffLocation = {
              [Sequelize.Op.iLike]: `%${incomeData.pickTo}%`,
            };
          }
          if (incomeData.equipmentFilter) {
            condition['$equipmentDetails.Equipment.id$'] = incomeData.equipmentFilter;
          }
          if (incomeData.gateFilter) {
            condition['$gateDetails.Gate.id$'] = incomeData.gateFilter;
          }
          if (incomeData.defineFilter) {
            condition['$defineWorkDetails.DeliverDefineWork.id$'] = incomeData.defineFilter;
          }
          if (incomeData.companyFilter) {
            condition['$companyDetails.Company.id$'] = incomeData.companyFilter;
          }
          if (incomeData.idFilter) {
            condition.DeliveryId = incomeData.idFilter;
          }
          if (incomeData.statusFilter) {
            condition.status = incomeData.statusFilter;
          }
          if (incomeData.startdate) {
            const startDateTime = moment(incomeData.startdate, 'YYYY-MM-DD')
              .startOf('day')
              .utcOffset(Number(inputData.headers.timezoneoffset), true);
            const endDateTime = moment(incomeData.enddate, 'YYYY-MM-DD')
              .endOf('day')
              .utcOffset(Number(inputData.headers.timezoneoffset), true);
            condition.deliveryStart = {
              [Op.between]: [moment(startDateTime), moment(endDateTime)],
            };
          }
          if (incomeData.enddateFilter) {
            const startDateTime = moment(incomeData.enddateFilter, 'YYYY-MM-DD')
              .startOf('day')
              .utcOffset(Number(inputData.headers.timezoneoffset), true);
            const endDateTime = moment(incomeData.enddateFilter, 'YYYY-MM-DD')
              .endOf('day')
              .utcOffset(Number(inputData.headers.timezoneoffset), true);
            condition.deliveryStart = {
              [Op.between]: [moment(startDateTime), moment(endDateTime)],
            };
          }
          if (incomeData.upcoming) {
            condition.deliveryStart = {
              [Op.gt]: new Date(),
            };
            order = 'ASC';
            sort = 'ASC';
            sortByField = 'deliveryStart';
          }
          if (incomeData.search) {
            const searchDefault = [
              {
                '$approverDetails.User.firstName$': {
                  [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                },
              },
              {
                '$equipmentDetails.Equipment.equipmentName$': {
                  [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                },
              },
              {
                description: {
                  [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                },
              },
              {
                cranePickUpLocation: {
                  [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                },
              },
              {
                craneDropOffLocation: {
                  [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                },
              },
            ];
            if (!Number.isNaN(+incomeData.search)) {
              searchCondition = {
                [Op.and]: [
                  {
                    [Op.or]: [
                      searchDefault,
                      {
                        [Op.and]: [
                          {
                            DeliveryId: +incomeData.search,
                            isDeleted: false,
                            ProjectId: +params.ProjectId,
                          },
                        ],
                      },
                    ],
                  },
                ],
              };
            } else {
              searchCondition = {
                [Op.and]: [
                  {
                    [Op.or]: searchDefault,
                  },
                ],
              };
            }
          }
          const roleId = memberDetails.RoleId;
          const memberId = memberDetails.id;
          if (
            incomeData.companyFilter ||
            incomeData.gateFilter ||
            incomeData.memberFilter ||
            incomeData.assignedFilter ||
            (memberDetails.RoleId === 4 &&
              (params.void === '0' || params.void === 0) &&
              !incomeData.upcoming) ||
            (memberDetails.RoleId === 3 &&
              (params.void === '0' || params.void === 0) &&
              !incomeData.upcoming)
          ) {
            const result = { count: 0, rows: [] };
            const deliveryList = await DeliveryRequest.getCalendarData(
              condition,
              roleId,
              memberId,
              searchCondition,
              order,
              sort,
              sortByField,
            );
            this.getSearchData(
              incomeData,
              deliveryList.rows,
              [],
              +params.pageSize,
              0,
              0,
              memberDetails,
              async (checkResponse, checkError) => {
                if (!checkError) {
                  this.getLimitData(
                    checkResponse,
                    0,
                    +params.pageSize,
                    [],
                    incomeData,
                    inputData.headers.timezoneoffset,
                    async (newResponse, newError) => {
                      if (!newError) {
                        if (sort === 'ASC') {
                          newResponse.sort(function (a, b) {
                            return a[sortByField] > b[sortByField]
                              ? 1
                              : b[sortByField] > a[sortByField]
                                ? -1
                                : 0;
                          });
                        } else {
                          newResponse.sort(function (a, b) {
                            return b[sortByField] > a[sortByField]
                              ? 1
                              : a[sortByField] > b[sortByField]
                                ? -1
                                : 0;
                          });
                        }
                        if (inputData.body.exportType) {
                          result.rows = newResponse;
                        } else {
                          result.rows = newResponse.slice(offset, offset + +params.pageSize);
                        }
                        result.count = checkResponse.length;
                        done(result, false);
                      } else {
                        done(null, { message: 'Something went wrong' });
                      }
                    },
                  );
                } else {
                  done(null, { message: 'Something went wrong' });
                }
              },
            );
          } else {
            const newResult = { count: 0, rows: [] };
            const deliveryList = await DeliveryRequest.getAll(
              condition,
              roleId,
              memberId,
              +params.pageSize,
              offset,
              searchCondition,
              order,
              sort,
              sortByField,
            );
            this.getLimitData(
              deliveryList,
              0,
              +params.pageSize,
              [],
              incomeData,
              inputData.headers.timezoneoffset,
              async (newResponse, newError) => {
                if (!newError) {
                  if (sort === 'ASC') {
                    newResponse.sort(function (a, b) {
                      return a[sortByField] > b[sortByField]
                        ? 1
                        : b[sortByField] > a[sortByField]
                          ? -1
                          : 0;
                    });
                  } else {
                    newResponse.sort(function (a, b) {
                      return b[sortByField] > a[sortByField]
                        ? 1
                        : a[sortByField] > b[sortByField]
                          ? -1
                          : 0;
                    });
                  }
                  if (inputData.body.exportType) {
                    newResult.rows = newResponse;
                  } else {
                    newResult.rows = newResponse.slice(offset, offset + +params.pageSize);
                  }
                  newResult.count = deliveryList.length;
                  done(newResult, false);
                } else {
                  done(null, { message: 'Something went wrong' });
                }
              },
            );
          }
        } else {
          done(null, { message: 'Project Id/Member does not exist' });
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  emptyHeatMapResult() {
    const finalResult = {};
    finalResult.count = 0;
    finalResult.result = {};
    return finalResult;
  },
  async heatMapQueryOut(inputData, incomeData, params) {
    const result = {};
    if (incomeData.templateType.length === 1) {
      if (incomeData.templateType.find((ele) => ele.id === 1) && incomeData.gateFilter != 0) {
        return this.emptyHeatMapResult();
      }
      if (
        incomeData.templateType.find((ele) => ele.id === 2) &&
        (incomeData.gateFilter != 0 ||
          incomeData.equipmentFilter != 0 ||
          incomeData.defineFilter != 0)
      ) {
        return this.emptyHeatMapResult();
      }
    } else if (incomeData.templateType.length === 2) {
      const IDs = incomeData.templateType.map((d) => parseInt(d.id));
      const isCrane_Concrete_Combained = IDs.includes(1) && IDs.includes(2);
      if (isCrane_Concrete_Combained && incomeData.gateFilter != 0) {
        return this.emptyHeatMapResult();
      }
    }
    const queryBuilderOut = (await queryBuilderExternal(inputData, incomeData, params)).toString();
    const replacementsObj = await replacementsBuilderExternal(inputData, incomeData, params);
    const deliveryList = await sequelize.query(queryBuilderOut, {
      replacements: { ...replacementsObj },
      type: Sequelize.QueryTypes.SELECT,
    });
    // sorting
    let deliveryListSort = _.sortBy(deliveryList, (o) => moment(o.Date, 'MMMM DD,YYYY').toDate());
    if (params.sortOrder == 'desc') {
      deliveryListSort = _.sortBy(deliveryList, (o) =>
        moment(o.Date, 'MMMM DD,YYYY').toDate(),
      ).reverse();
    }

    _.forEach(deliveryListSort, (d) => {
      if (!result.hasOwnProperty(d.Date)) {
        result[d.Date] = {
          timeslots: defaultTimeSlots(),
          totalCount: 0 + parseInt(d.Count),
        };
        result[d.Date].timeslots[d.Time] = parseInt(d.Count);
      } else {
        result[d.Date].timeslots[d.Time] = parseInt(d.Count);
        result[d.Date].totalCount = result[d.Date].totalCount + parseInt(d.Count);
      }
    });

    // pagination
    const finalData = _.map(result, function (value, key) {
      return { date: key, data: value };
    });

    let pageData;

    if (incomeData.hasOwnProperty('exportType')) {
      pageData = finalData;
    } else {
      const pagesize = parseInt(params.pageSize);
      const pagenumber = parseInt(params.pageNo.trim(), 10);
      const startIndex = (pagenumber - 1) * pagesize;
      const endIndex = pagenumber * pagesize;
      pageData = _.slice(finalData, startIndex, endIndex);
    }

    const convertedObject = _.reduce(
      pageData,
      function (result, value) {
        result[value.date] = value.data;
        return result;
      },
      {},
    );

    const finalResult = {};
    finalResult.count = finalData.length;
    finalResult.result = convertedObject;
    return finalResult;
  },
  async heatMapListDeliveryRequest(inputData, done) {
    try {
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      if (params.void >= 1 && params.void <= 0) {
        done(null, { message: 'Please enter void as 1 or 0' });
      } else {
        const memberDetails = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: params.ProjectId,
            isDeleted: false,
            isActive: true,
          }),
        });
        if (memberDetails) {
          const result = await this.heatMapQueryOut(inputData, incomeData, params);
          done(result, false);
        } else {
          done(null, { message: 'Project Id/Member does not exist' });
        }
      }
    } catch (e) {
      // console.log("********Error*********", e)
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async getLimitData(result, index, limit, finalResult, incomeData, timezoneoffset, done) {
    if (index < limit) {
      finalResult.push(result);
      this.getLimitData(
        result,
        index + 1,
        limit,
        finalResult,
        incomeData,
        timezoneoffset,
        (response, err) => {
          if (!err) {
            done(response, false);
          } else {
            done(null, err);
          }
        },
      );
    } else {
      done(result, false);
    }
  },
  async getSearchData(incomeData, deliveryList, result, limit, index, count, memberDetails, done) {
    const elementValue = deliveryList[index];
    if (elementValue) {
      const element = JSON.parse(JSON.stringify(elementValue));
      const status = { companyCondition: true, gateCondition: true, memberCondition: true };
      if (incomeData.companyFilter) {
        const data = await DeliverCompany.findOne({
          where: {
            DeliveryId: element.id,
            CompanyId: incomeData.companyFilter,
            isDeleted: false,
          },
        });
        if (!data) {
          status.companyCondition = false;
        }
      }
      if (incomeData.gateFilter) {
        const data = await DeliverGate.findOne({
          where: {
            DeliveryId: element.id,
            GateId: incomeData.gateFilter,
            isDeleted: false,
          },
        });
        if (!data) {
          status.gateCondition = false;
        }
      }
      if (incomeData.memberFilter) {
        const data = await DeliveryPerson.findOne({
          where: {
            DeliveryId: element.id,
            MemberId: incomeData.memberFilter,
            isDeleted: false,
          },
        });
        if (!data) {
          status.memberCondition = false;
        }
      }
      // if (memberDetails.RoleId === 4 || memberDetails.RoleId === 3) {
      //   const data = await DeliveryPerson.findOne({
      //     where: {
      //       DeliveryId: element.id,
      //       MemberId: memberDetails.id,
      //       isDeleted: false,
      //       isActive: true,
      //     },
      //   });
      //   if (!data) {
      //     status.memberCondition = false;
      //   }
      // }

      if (status.companyCondition && status.gateCondition && status.memberCondition) {
        result.push(element);
      }
      if (index < deliveryList.length - 1) {
        this.getSearchData(
          incomeData,
          deliveryList,
          result,
          limit,
          index + 1,
          count + 1,
          memberDetails,
          (response, err) => {
            if (!err) {
              done(response, false);
            } else {
              done(null, err);
            }
          },
        );
      } else {
        done(result, false);
      }
    } else {
      done(result, false);
    }
  },
  async exportReportForScheduler(req) {
    return new Promise(async (res, rej) => {
      try {
        await this.listDeliveryRequest(req, async (response, error) => {
          if (!error) {
            if (response.count == 0) {
              return res('No Data Found');
            }
            if (req.body.exportType === 'PDF') {
              const loginUser = req.user;
              await pdfDeliveryReportService.pdfFormatOfDeliveryRequest(
                req.params,
                loginUser,
                response.rows,
                req,
                async (pdfFile, err) => {
                  if (!err) {
                    res(pdfFile);
                  } else {
                    rej(err);
                  }
                },
              );
            }
            if (req.body.exportType === 'EXCEL') {
              const workbook = await exportService.createWorkbook();
              let reportWorkbook = await excelDeliveryReportService.deliveryReport(
                workbook,
                response.rows,
                req.body.selectedHeaders,
                req.headers.timezoneoffset,
              );
              if (reportWorkbook) {
                reportWorkbook = await reportWorkbook.xlsx.writeBuffer();
                // console.log("******Buffer.isBuffer(reportWorkbook)******", Buffer.isBuffer(reportWorkbook));
                if (Buffer.isBuffer(reportWorkbook)) {
                  await awsConfig.reportUpload(
                    reportWorkbook,
                    req.body.reportName,
                    req.body.exportType === 'EXCEL' ? 'xlsx' : req.body.exportType,
                    async (result, error1) => {
                      if (!error1) {
                        res(result);
                      } else {
                        rej(error1);
                      }
                    },
                  );
                } else {
                  res('No data found');
                }
              } else {
                res('No data found');
              }
            }
            if (req.body.exportType === 'CSV') {
              await csvDeliveryReportService.exportDeliveryReportInCsvFormat(
                response.rows,
                req.body.selectedHeaders,
                req.headers.timezoneoffset,
                req.body.reportName,
                req.body.exportType,
                async (csvFile, err) => {
                  if (!err) {
                    res(csvFile);
                  } else {
                    rej(err);
                  }
                },
              );
            }
          } else {
            rej(error);
          }
        });
      } catch (e) {
        rej(e);
      }
    });
  },
  async exportReport(req, done) {
    await this.listDeliveryRequest(req, async (response, error) => {
      if (!error) {
        if (req.body.exportType === 'PDF') {
          const loginUser = req.user;
          await pdfDeliveryReportService.pdfFormatOfDeliveryRequest(
            req.params,
            loginUser,
            response.rows,
            req,
            async (pdfFile, err) => {
              if (!err) {
                if (req.body.saved) {
                  req.body.reportType = 'Delivery';
                  const savedData = await this.createSavedReports(req, pdfFile);
                  if (savedData) {
                    return done(pdfFile, false);
                  }
                  done(null, { message: 'cannot create reports' });
                } else {
                  return done(pdfFile, false);
                }
              }
            },
          );
        }
        if (req.body.exportType === 'EXCEL') {
          const workbook = await exportService.createWorkbook();
          let reportWorkbook = await excelDeliveryReportService.deliveryReport(
            workbook,
            response.rows,
            req.body.selectedHeaders,
            req.headers.timezoneoffset,
          );
          if (reportWorkbook) {
            if (req.body.saved) {
              reportWorkbook = await reportWorkbook.xlsx.writeBuffer();
              const excelFile = await this.saveExcelReport(
                reportWorkbook,
                req.body.reportName,
                req.body.exportType,
              );
              if (excelFile) {
                req.body.reportType = 'Delivery';
                const savedData = await this.createSavedReports(req, excelFile);
                return done(excelFile, false);
              }
              done(null, { message: 'cannot create reports' });
            } else {
              return done(reportWorkbook, false);
            }
          }
          done(null, { message: 'cannot export document' });
        }
        if (req.body.exportType === 'CSV') {
          await csvDeliveryReportService.exportDeliveryReportInCsvFormat(
            response.rows,
            req.body.selectedHeaders,
            req.headers.timezoneoffset,
            req.body.reportName,
            req.body.exportType,
            async (csvFile, err) => {
              if (!err) {
                if (req.body.saved) {
                  req.body.reportType = 'Delivery';
                  const savedData = await this.createSavedReports(req, csvFile);
                  if (savedData) {
                    return done(csvFile, false);
                  }
                  done(null, { message: 'cannot create reports' });
                } else {
                  return done(csvFile, false);
                }
              }
              return done(null, { message: 'cannot export document' });
            },
          );
        }
      }
    });
  },
  async getTotalRequestList(req, done) {
    const { timezoneoffset } = req.headers;
    await this.getDynamicModel(req);
    const incomeData = req.body;
    const { params } = req;
    const loginUser = req.user;
    const memberDetails = await Member.findOne({
      where: Sequelize.and({
        UserId: loginUser.id,
        ProjectId: params.ProjectId,
        isDeleted: false,
        isActive: true,
      }),
    });
    const condition = {
      ProjectId: +params.ProjectId,
      isQueued: false,
      isDeleted: false,
    };
    const craneDeliveryRequestCondition = {
      ProjectId: +params.ProjectId,
      isDeleted: false,
    };
    const concreteCondition = {
      ProjectId: +params.ProjectId,
      isDeleted: false,
    };

    if (memberDetails) {
      const roleId = memberDetails.RoleId;
      const memberId = memberDetails.id;
      const finalArray = incomeData.templateType.map((obj) => {
        return obj.name;
      });
      if (finalArray && finalArray.length > 0) {
        const result = { count: 0, rows: [] };
        const eventArray = [];
        if (finalArray.includes('Delivery')) {
          if (finalArray.includes('Crane')) {
            condition.requestType = 'deliveryRequest';
          }
          const deliveryList = await this.getWeeklyDeliveryRequest(
            condition,
            incomeData,
            params,
            timezoneoffset,
            roleId,
            req,
          );
          if (deliveryList && deliveryList.rows && deliveryList.rows.length > 0) {
            eventArray.push(...deliveryList.rows);
          }
        }
        if (finalArray.includes('Crane')) {
          if (incomeData.statusFilter) {
            craneDeliveryRequestCondition.status = incomeData.statusFilter;
          }
          if (req.body.exportType === 'EXCEL') {
            req.body.start = moment(moment(req.body.currentStart, 'YYYY-MM-DD HH:mm:ss')).add(
              -Number(timezoneoffset),
              'm',
            );
            req.body.end = moment(moment(req.body.currentEnd, 'YYYY-MM-DD HH:mm:ss')).add(
              -Number(timezoneoffset),
              'm',
            );
          }
          let startDate;
          let endDate;
          if (incomeData.startDate) {
            startDate = moment(incomeData.startDate, 'YYYY-MM-DD').format('YYYY-MM-DD 00:00:00');
            endDate = moment(incomeData.endDate, 'YYYY-MM-DD')
              .add(1, 'days')
              .format('YYYY-MM-DD 00:00:00');
          }
          // if (incomeData.startDate) {
          //   const startDate = moment(incomeData.startDate, 'YYYY-MM-DD')
          //     .startOf('day')
          //     .utcOffset(Number(timezoneoffset), true);
          //   const endDate = moment(incomeData.endDate, 'YYYY-MM-DD')
          //     .endOf('day')
          //     .utcOffset(Number(timezoneoffset), true);
          //   craneDeliveryRequestCondition.craneDeliveryStart = {
          //     [Op.between]: [moment(startDate), moment(endDate)],
          //   };
          // }
          if (incomeData.defineFilter) {
            craneDeliveryRequestCondition['$defineWorkDetails.DeliverDefineWork.id$'] =
              incomeData.defineFilter;
          }
          if (incomeData.companyFilter) {
            craneDeliveryRequestCondition['$companyDetails.Company.id$'] = incomeData.companyFilter;
          }
          if (incomeData.memberFilter > 0) {
            craneDeliveryRequestCondition['$memberDetails.Member.id$'] = +incomeData.memberFilter;
          }
          if (incomeData.equipmentFilter) {
            craneDeliveryRequestCondition['$equipmentDetails.Equipment.id$'] =
              incomeData.equipmentFilter;
          }
          const voidCraneDelivery = [];
          const voidCraneRequestList = await VoidList.findAll({
            where: {
              ProjectId: +params.ProjectId,
              isDeliveryRequest: false,
              CraneRequestId: { [Op.ne]: null },
            },
          });
          voidCraneRequestList.forEach(async (element) => {
            voidCraneDelivery.push(element.CraneRequestId);
          });
          if (params.void === '0' || params.void === 0) {
            craneDeliveryRequestCondition['$CraneRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidCraneDelivery }],
            };
          }
          let craneRequestList;
          if (incomeData.gateFilter) {
            craneRequestList = [];
          } else {
            craneRequestList = await CraneRequest.getWeeklyCalendarList(
              req,
              roleId,
              craneDeliveryRequestCondition,
              incomeData.start,
              incomeData.end,
              incomeData.startTime,
              incomeData.endTime,
              startDate,
              endDate,
              incomeData.typeFormat,
              incomeData.timezone,
              incomeData.eventStartTime,
              incomeData.eventEndTime,
            );
          }
          condition.requestType = 'deliveryRequestWithCrane';
          const deliveryListWithCraneEquipment = await this.getWeeklyDeliveryRequest(
            condition,
            incomeData,
            params,
            timezoneoffset,
            roleId,
            req,
          );
          if (
            deliveryListWithCraneEquipment &&
            deliveryListWithCraneEquipment.rows &&
            deliveryListWithCraneEquipment.rows.length > 0
          ) {
            eventArray.push(...deliveryListWithCraneEquipment.rows);
          }
          if (craneRequestList && craneRequestList.length > 0) {
            eventArray.push(...craneRequestList);
          }
        }
        if (finalArray.includes('Concrete')) {
          if (req.body.exportType === 'EXCEL') {
            req.body.start = moment(moment(req.body.currentStart, 'YYYY-MM-DD HH:mm:ss')).add(
              -Number(timezoneoffset),
              'm',
            );
            req.body.end = moment(moment(req.body.currentEnd, 'YYYY-MM-DD HH:mm:ss')).add(
              -Number(timezoneoffset),
              'm',
            );
          }
          let startDate;
          let endDate;
          if (incomeData.startDate) {
            startDate = moment(incomeData.startDate, 'YYYY-MM-DD').format('YYYY-MM-DD 00:00:00');
            endDate = moment(incomeData.endDate, 'YYYY-MM-DD')
              .add(1, 'days')
              .format('YYYY-MM-DD 00:00:00');
          }
          // if (incomeData.startDate) {
          //   const startDate = moment(incomeData.startDate, 'YYYY-MM-DD')
          //     .startOf('day')
          //     .utcOffset(Number(timezoneoffset), true);
          //   const endDate = moment(incomeData.endDate, 'YYYY-MM-DD')
          //     .endOf('day')
          //     .utcOffset(Number(timezoneoffset), true);
          //   concreteCondition.concretePlacementStart = {
          //     [Op.between]: [moment(startDate), moment(endDate)],
          //   };
          // }
          const voidConcreteDelivery = [];
          const voidConcreteRequestList = await VoidList.findAll({
            where: {
              ProjectId: params.ProjectId,
              isDeliveryRequest: false,
              ConcreteRequestId: { [Op.ne]: null },
            },
          });
          voidConcreteRequestList.forEach(async (element) => {
            voidConcreteDelivery.push(element.ConcreteRequestId);
          });
          if (params.void === '0' || params.void === 0) {
            concreteCondition['$ConcreteRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidConcreteDelivery }],
            };
          }
          let getConcreteRequest;
          if (incomeData.equipmentFilter || incomeData.gateFilter) {
            getConcreteRequest = [];
          } else {
            getConcreteRequest = await ConcreteRequest.getWeeklyCalendarList(
              req,
              concreteCondition,
              roleId,
              memberId,
              incomeData.companyFilter,
              incomeData.statusFilter,
              startDate,
              endDate,
              incomeData.memberFilter,
              incomeData.start,
              incomeData.end,
              incomeData.startTime,
              incomeData.endTime,
              incomeData.typeFormat,
              incomeData.timezone,
              incomeData.eventStartTime,
              incomeData.eventEndTime,
            );
          }
          if (getConcreteRequest && getConcreteRequest.length > 0) {
            eventArray.push(...getConcreteRequest);
          }
        }
        if (finalArray.includes('Calendar Events')) {
          let getAllEvents = [];
          req.query.start = req.body.start;
          req.query.end = req.body.end;
          req.query.ProjectId = req.params.ProjectId;
          req.query.ParentCompanyId = req.body.ParentCompanyId;
          req.query.weeklyReportTest = 'weeklyReport';
          req.query.calendarView = req.body.calendarView;
          req.query.timezone = req.body.timezone;
          req.query.isDST = req.body.isDST;
          req.body.eventStartTime = moment(req.body.eventStartTime, 'HH:mm:00').format('HH:mm');
          req.body.eventEndTime = moment(req.body.eventEndTime, 'HH:mm:00').format('HH:mm');
          if (req.body.exportType === 'EXCEL') {
            req.body.start = moment(moment(req.body.currentStart, 'YYYY-MM-DD HH:mm:ss')).add(
              -Number(timezoneoffset),
              'm',
            );
            req.body.end = moment(moment(req.body.currentEnd, 'YYYY-MM-DD HH:mm:ss')).add(
              -Number(timezoneoffset),
              'm',
            );
          }
          if (incomeData.startDate) {
            req.body.startDate = moment(incomeData.startDate, 'YYYY-MM-DD').format(
              'YYYY-MM-DD 00:00:00',
            );
            req.body.endDate = moment(incomeData.endDate, 'YYYY-MM-DD').format(
              'YYYY-MM-DD 00:00:00',
            );
          }
          if (
            (req.body.equipmentFilter && req.body.equipmentFilter > 0) ||
            (req.body.defineFilter && req.body.defineFilter > 0) ||
            req.body.statusFilter ||
            (req.body.memberFilter && req.body.memberFilter > 0) ||
            (req.body.companyFilter && req.body.companyFilter > 0) ||
            (req.body.gateFilter && req.body.gateFilter > 0)
          ) {
            getAllEvents = [];
          } else {
            getAllEvents = await calendarSettingsService.getAll(req, done);
          }
          if (getAllEvents && getAllEvents.length > 0) {
            eventArray.push(...getAllEvents);
          }
        }
        if (eventArray && eventArray.length > 0) {
          result.rows = eventArray;
          result.count = eventArray.length;
          if (req.body.exportType) {
            return done(result, false);
          }
          return result;
        }
        if (req.body.exportType) {
          return done(eventArray, false);
        }
        return eventArray;
      }
      throw new ApiError('Please choose a Template type', httpStatus.BAD_REQUEST);
    }
  },
  async exportWeeklyCalendarReportForScheduler(req) {
    return new Promise(async (res, rej) => {
      try {
        if (req.body.exportType === 'EXCEL') {
          const finalArray = req.body.templateType.map((obj) => {
            return obj.name;
          });
          await this.getTotalRequestList(req, async (response, error) => {
            if (!error) {
              if (response.count == 0) {
                res('No Data Found');
              }
              if (req.body.exportType === 'EXCEL') {
                const workbook = await exportService.createWorkbook();
                let startDate = req.body.currentStart;
                let endDate = req.body.currentEnd;
                let chosenDateRangeFilter = false;
                if (req.body.startDate && req.body.endDate) {
                  chosenDateRangeFilter = true;
                  startDate = req.body.startDate;
                  endDate = moment(req.body.endDate, 'YYYY-MM-DD')
                    .add(1, 'days')
                    .format('YYYY-MM-DD 00:00:00');
                }
                let reportWorkbook = await excelWeeklyCalendarService.weeklyCalendarReport(
                  finalArray,
                  startDate,
                  endDate,
                  chosenDateRangeFilter,
                  workbook,
                  response.rows,
                  req.headers.timezoneoffset,
                );
                if (reportWorkbook) {
                  reportWorkbook = await reportWorkbook.xlsx.writeBuffer();
                  // console.log("******Buffer.isBuffer(reportWorkbook)******", Buffer.isBuffer(reportWorkbook));
                  if (Buffer.isBuffer(reportWorkbook)) {
                    await awsConfig.reportUpload(
                      reportWorkbook,
                      req.body.reportName,
                      req.body.exportType === 'EXCEL' ? 'xlsx' : req.body.exportType,
                      async (result, error1) => {
                        if (!error1) {
                          res(result);
                        } else {
                          rej(error1);
                        }
                      },
                    );
                  } else {
                    res('No data found');
                  }
                } else {
                  res('No data found');
                }
              }
            } else {
              rej(error);
            }
          });
        } else {
          const incomeData = req.body;
          const { params } = req;
          const loginUser = req.user;
          const startTime = moment(incomeData.eventStartTime, 'HH:mm:ss');
          const endTime = moment(incomeData.eventEndTime, 'HH:mm:ss');
          const duration = moment.duration(endTime.diff(startTime));
          const hourDifference = duration.asHours();
          if (hourDifference > 12) {
            res(
              'Currently, we are not supporting more than 12 hours time difference for PDF export. please change the start and end time in the filter section.',
            );
          }
          const template = JSON.parse(JSON.stringify(incomeData.templateType))
            .map((d) => d.id)
            .join(',');
          const url = `${process.env.BASE_URL}/fc/${params.ProjectId}?user=${loginUser.id}&start=${incomeData.startDate !== '' ? incomeData.startDate : incomeData.start
            }&end=${incomeData.endDate !== '' ? incomeData.endDate : incomeData.end}&parent=${incomeData.ParentCompanyId
            }&company=${incomeData.companyFilter}&start_time=${incomeData.startTime}&end_time=${incomeData.endTime
            }&event_start=${incomeData.eventStartTime}&event_end=${incomeData.eventEndTime}&equip=${incomeData.equipmentFilter
            }&gate=${incomeData.gateFilter}&member=${incomeData.memberFilter}&define=${incomeData.memberFilter
            }${incomeData.statusFilter == '' ? '&status=' : `&status=${incomeData.statusFilter}`
            }&timezone=${incomeData.timezone}&dst=${incomeData.isDST}&template=${template}`;
          const { timezone } = incomeData;
          const result = await puppeteerService.generatePdfByURL(url, timezone);
          if (Buffer.isBuffer(result)) {
            awsConfig.reportUpload(
              result,
              req.body.reportName,
              req.body.exportType,
              async (result, error1) => {
                if (!error1) {
                  res(result);
                } else {
                  rej(error1);
                }
              },
            );
          } else {
            res('No data found');
          }
        }
      } catch (e) {
        console.log('#######Error#######', e);
        rej(e);
      }
    });
  },
  async exportWeeklyCalendarReport(req, done) {
    const finalArray = req.body.templateType.map((obj) => {
      return obj.name;
    });
    await this.getTotalRequestList(req, async (response, error) => {
      if (!error) {
        if (req.body.exportType === 'EXCEL') {
          const workbook = await exportService.createWorkbook();
          let startDate = req.body.currentStart;
          let endDate = req.body.currentEnd;
          let chosenDateRangeFilter = false;
          if (req.body.startDate && req.body.endDate) {
            chosenDateRangeFilter = true;
            startDate = req.body.startDate;
            endDate = moment(req.body.endDate, 'YYYY-MM-DD')
              .add(1, 'days')
              .format('YYYY-MM-DD 00:00:00');
          }
          let reportWorkbook = await excelWeeklyCalendarService.weeklyCalendarReport(
            finalArray,
            startDate,
            endDate,
            chosenDateRangeFilter,
            workbook,
            response.rows,
            req.headers.timezoneoffset,
          );
          if (reportWorkbook) {
            if (req.body.saved) {
              reportWorkbook = await reportWorkbook.xlsx.writeBuffer();
              const excelFile = await this.saveExcelReport(
                reportWorkbook,
                req.body.reportName,
                req.body.exportType,
              );
              if (excelFile) {
                req.body.reportType = 'Weekly Calender';
                const savedData = await this.createSavedReports(req, excelFile);
                return done(excelFile, false);
              }
              done(null, { message: 'cannot create reports' });
            } else {
              return done(reportWorkbook, false);
            }
          }
          done(null, { message: 'cannot export document' });
        }
      }
      return done(null, { message: 'cannot export document' });
    });
  },
  async getWeeklyDeliveryRequest(condition1, incomeData, params, timezoneoffset, roleId, req) {
    const condition = condition1;
    if (req.body.exportType === 'EXCEL') {
      req.body.start = moment(moment(req.body.currentStart, 'YYYY-MM-DD HH:mm:ss')).add(
        -Number(timezoneoffset),
        'm',
      );
      req.body.end = moment(moment(req.body.currentEnd, 'YYYY-MM-DD HH:mm:ss')).add(
        -Number(timezoneoffset),
        'm',
      );
    }
    let startDate;
    let endDate;
    if (incomeData.startDate) {
      startDate = moment(incomeData.startDate, 'YYYY-MM-DD').format('YYYY-MM-DD 00:00:00');
      endDate = moment(incomeData.endDate, 'YYYY-MM-DD')
        .add(1, 'days')
        .format('YYYY-MM-DD 00:00:00');
    }
    if (incomeData.equipmentFilter) {
      condition['$equipmentDetails.Equipment.id$'] = incomeData.equipmentFilter;
    }
    if (incomeData.statusFilter) {
      condition.status = incomeData.statusFilter;
    }
    if (incomeData.gateFilter) {
      condition['$gateDetails.Gate.id$'] = incomeData.gateFilter;
    }
    if (incomeData.defineFilter) {
      condition['$defineWorkDetails.DeliverDefineWork.id$'] = incomeData.defineFilter;
    }
    if (incomeData.companyFilter) {
      condition['$companyDetails.Company.id$'] = incomeData.companyFilter;
    }
    if (incomeData.memberFilter > 0) {
      condition['$memberDetails.Member.id$'] = +incomeData.memberFilter;
    }
    const voidDelivery = [];
    const voidList = await VoidList.findAll({
      where: {
        ProjectId: +params.ProjectId,
        isDeliveryRequest: true,
        DeliveryRequestId: { [Op.ne]: null },
      },
    });
    voidList.forEach(async (element) => {
      voidDelivery.push(element.DeliveryRequestId);
    });
    if (params.void === '0' || params.void === 0) {
      condition['$DeliveryRequest.id$'] = {
        [Op.and]: [{ [Op.notIn]: voidDelivery }],
      };
    }
    const deliveryList = await DeliveryRequest.getWeeklyCalendarData(
      req,
      condition,
      roleId,
      req.body.start,
      req.body.end,
      incomeData.startTime,
      incomeData.endTime,
      startDate,
      endDate,
      incomeData.typeFormat,
      incomeData.timezone,
      incomeData.eventStartTime,
      incomeData.eventEndTime,
    );
    return deliveryList;
  },
  async exportHeatMapReportForSceduler(exportData) {
    return new Promise(async (res, rej) => {
      try {
        await this.heatMapListDeliveryRequest(exportData, async (response, error) => {
          if (!error) {
            if (response.count == 0) {
              return res('No Data Found');
            }
            if (exportData.body.exportType === 'PDF') {
              const loginUser = exportData.user;
              await pdfHeatMapService.pdfFormatOfDeliveryRequest(
                exportData.params,
                loginUser,
                response.result,
                exportData,
                async (pdfFile, err) => {
                  if (!err) {
                    res(pdfFile);
                  }
                },
              );
            }
          } else {
            rej(error);
          }
        });
      } catch (e) {
        // console.log("*********exportHeatMapReportForSceduler*************", e)
        rej(e);
      }
    });
  },
  async exportHeatMapReport(req, done) {
    await this.heatMapListDeliveryRequest(req, async (response, error) => {
      if (!error) {
        if (response.count == 0) {
          return done({ no_data: true }, false);
        }
        if (req.body.exportType === 'PDF') {
          const loginUser = req.user;
          await pdfHeatMapService.pdfFormatOfDeliveryRequest(
            req.params,
            loginUser,
            response.result,
            req,
            async (pdfFile, err) => {
              if (!err) {
                if (req.body.saved) {
                  req.body.reportType = 'Heat Map';
                  const savedData = await this.createSavedReports(req, pdfFile);
                  if (savedData) {
                    return done(pdfFile, false);
                  }
                  done(null, { message: 'cannot create reports' });
                } else {
                  return done(pdfFile, false);
                }
              }
            },
          );
        }
        if (req.body.exportType === 'EXCEL') {
          const workbook = await exportService.createWorkbook();
          const reportWorkbook = await excelDeliveryReportService.deliveryReport(
            workbook,
            response.result,
            req.body.selectedHeaders,
            req.headers.timezoneoffset,
          );
          if (reportWorkbook) {
            return done(reportWorkbook, false);
          }
          done(null, { message: 'cannot export document' });
        }
        if (req.body.exportType === 'CSV') {
          await csvDeliveryReportService.exportDeliveryReportInCsvFormat(
            response.result,
            req.body.selectedHeaders,
            req.headers.timezoneoffset,
            req.body.reportName,
            req.body.exportType,
            async (csvFile, err) => {
              if (!err) {
                return done(csvFile, false);
              }
              return done(null, { message: 'cannot export document' });
            },
          );
        }
      }
    });
  },
  async deleteSchedulerReport(data) {
    // Te get all scheduled tasks and deleteby key
    cron.getTasks().forEach((task, key) => {
      if (key == data.id) {
        task.stop();
      }
    });
    return SchedulerReport.updateInstance(data.id, { isDeleted: true });
  },

  async saveExcelReport(reportWorkbook, reportName, exportType) {
    return new Promise(async (res, rej) => {
      if (Buffer.isBuffer(reportWorkbook)) {
        await awsConfig.reportUpload(
          reportWorkbook,
          reportName,
          exportType === 'EXCEL' ? 'xlsx' : exportType,
          async (result, error1) => {
            if (!error1) {
              res(result);
            }
            rej(null);
          },
        );
      } else {
        rej(null);
      }
    });
  },

  async createSavedReports(inputData, pdfFile) {
    try {
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      incomeData.startTime = incomeData.startTime ? incomeData.startTime : '00:00';
      incomeData.endTime = incomeData.endTime ? incomeData.endTime : '00:00';
      incomeData.startDate = incomeData.startDate
        ? incomeData.startDate
        : moment().format('YYYY-MM-DD');
      incomeData.endDate = incomeData.endDate ? incomeData.endDate : moment().format('YYYY-MM-DD');

      const createDataFormation = {
        isSaved: true,
        reportName: incomeData.reportName,
        reportType: incomeData.reportType,
        ProjectId: parseInt(params.ProjectId),
        outputFormat: incomeData.exportType.trim(),
        recurrence: 'Does Not Repeat',
        repeatEvery: {},
        sendTo: [],
        subject: 'Report',
        message: 'Report',
        status: incomeData.status || null,
        isDeleted: false,
        createdBy: loginUser.id,
        lastRun: moment(),
        timezone: incomeData.timezone,
        startDate: `${incomeData.startDate}/${incomeData.startTime}`,
        endDate: `${incomeData.endDate}/${incomeData.endTime}`,
        memberFilterId: incomeData.memberFilter,
        parentFilterCompanyId: incomeData.ParentCompanyId,
        gateId: incomeData.gateFilter,
        defineId: incomeData.defineFilter,
        companyId: typeof incomeData.companyFilter === 'number' ? incomeData.companyFilter : 0,
        equipmentId:
          typeof incomeData.equipmentFilter === 'number' ? incomeData.equipmentFilter : 0,
        companyFilter: typeof incomeData.companyFilter === 'number' ? '' : incomeData.companyFilter,
        equipmentFilter:
          typeof incomeData.equipmentFilter === 'number' ? '' : incomeData.equipmentFilter,
        templateFilterType: incomeData.templateType ? incomeData.templateType : '[]',
        selectedHeaders: incomeData.selectedHeaders ? incomeData.selectedHeaders : '[]',
        s3_url: pdfFile,
        isEndDateMeet: false,
        idFilter: incomeData.idFilter ? incomeData.idFilter : 0,
        sort: incomeData.sort ? incomeData.sort : 'DESC',
        sortByField: incomeData.sortByField ? incomeData.sortByField : 'id',
        queuedNdr: incomeData.queuedNdr ? incomeData.queuedNdr : false,
        pickFrom: incomeData.pickFrom ? incomeData.pickFrom : '',
        pickTo: incomeData.pickTo ? incomeData.pickTo : '',
        locationFilter: incomeData.locationFilter ? incomeData.locationFilter : '',
        descriptionFilter: incomeData.descriptionFilter ? incomeData.descriptionFilter : '',
        orderNumberFilter: incomeData.orderNumberFilter ? incomeData.orderNumberFilter : '',
        mixDesignFilter: incomeData.mixDesignFilter ? incomeData.mixDesignFilter : '',
        truckspacingFilter: incomeData.truckspacingFilter ? incomeData.truckspacingFilter : '',
        slumpFilter: incomeData.slumpFilter ? incomeData.slumpFilter : '',
        primerFilter: incomeData.primerFilter ? incomeData.primerFilter : '',
        quantityFilter: incomeData.quantityFilter ? incomeData.quantityFilter : '',
      };
      createDataFormation.cronExpression = '1 1 1 1 1';
      const newScheduler = await SchedulerReport.createInstance(createDataFormation);
      return newScheduler;
    } catch (e) {
      return null;
    }
  },
};
module.exports = deliveryReportService;
