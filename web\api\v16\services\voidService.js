const { Sequelize, Enterprise } = require('../models');
let { <PERSON><PERSON><PERSON><PERSON>, Member, User, DeliveryRequest } = require('../models');
const {
  CraneRequest,
  ConcreteRequest,
  DeliverHistory,
  CraneRequestHistory,
  ConcreteRequestHistory,
} = require('../models');
const helper = require('../helpers/domainHelper');
// const mixpanelService = require('./mixpanelService');
const craneRequestService = require('./craneRequestService');
const concreteRequestService = require('./concreteRequestService');
const deliveryService = require('./deliveryService');
// const { deliveryService } = require("./");

const { Op } = Sequelize;
let publicUser;
let publicMember;
const voidService = {
  async createVoidList(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const voidData = inputData.body;
      const loginUser = inputData.user;
      const memberData = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: voidData.ProjectId,
          isDeleted: false,
        }),
      });
      if (memberData) {
        const newDeliveryRequest = await DeliveryRequest.findOne({
          where: { id: voidData.DeliveryRequestId },
        });
        if (newDeliveryRequest) {
          voidData.MemberId = memberData.id;
          const existVoid = await VoidList.findOne({
            where: Sequelize.and({
              DeliveryRequestId: voidData.DeliveryRequestId,
            }),
          });
          if (!existVoid) {
            const newVoidData = await VoidList.createInstance(voidData);
            if (newVoidData) {
              const object = {
                ProjectId: voidData.ProjectId,
                MemberId: memberData.id,
                DeliveryRequestId: newDeliveryRequest.id,
                isDeleted: false,
                type: 'void',
                description: `${loginUser.firstName} ${loginUser.lastName} Voided the Delivery Booking, ${newDeliveryRequest.description}`,
              };
              await DeliverHistory.createInstance(object);
            }
            done(newVoidData, false);
          } else {
            done(null, { message: 'Delivery Booking already is in void list.' });
          }
        } else {
          done(null, { message: 'Delivery Booking Id Does not exist.' });
        }
      } else {
        done(null, { message: 'Project Id/Member Does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    let enterpriseValue;
    let ProjectId;
    const incomeData = inputData;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    VoidList = modelObj.VoidList;
    Member = modelObj.Member;
    DeliveryRequest = modelObj.DeliveryRequest;
    User = modelObj.User;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return ProjectId;
  },
  async removeVoidList(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const reqData = inputData.body;
      const loginUser = inputData.user;
      const memberData = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: reqData.ProjectId,
          isDeleted: false,
        }),
      });
      if (memberData) {
        if (reqData.isSelectAll) {
          const voidedList = await VoidList.findAll({
            where: {
              ProjectId: reqData.ProjectId,
              isDeleted: false,
            },
          });
          if (voidedList && voidedList.length > 0) {
            voidedList.forEach(async (element) => {
              if (
                element.DeliveryRequestId &&
                !element.CraneRequestId &&
                !element.ConcreteRequestId
              ) {
                await this.deliveryRequestVoidHistory(element, memberData, loginUser);
              }
              if (
                !element.DeliveryRequestId &&
                element.CraneRequestId &&
                !element.ConcreteRequestId
              ) {
                await this.craneRequestVoidHistory(element, memberData, loginUser);
              }
              if (
                !element.DeliveryRequestId &&
                !element.CraneRequestId &&
                element.ConcreteRequestId
              ) {
                await this.concreteRequestVoidHistory(element, memberData, loginUser);
              }
            });
          }
          const removeData = VoidList.destroy({
            where: {
              ProjectId: reqData.ProjectId,
            },
          });
          done(removeData, false);
        } else {
          const { id } = reqData;
          const existVoid = await VoidList.findOne({
            where: { id: { [Op.in]: id } },
          });
          if (existVoid) {
            if (existVoid && existVoid.CraneRequestId) {
              await this.craneRequestVoidHistory(existVoid, memberData, loginUser);
            }
            if (existVoid && existVoid.ConcreteRequestId) {
              await this.concreteRequestVoidHistory(existVoid, memberData, loginUser);
            }
            if (existVoid && existVoid.DeliveryRequestId) {
              await this.deliveryRequestVoidHistory(existVoid, memberData, loginUser);
            }
            const removeData = await await VoidList.destroy({
              where: { id: { [Op.in]: id } },
            });
            done(removeData, false);
          } else {
            done(null, { message: 'Some Void not available' });
          }
        }
      } else {
        done(null, { message: 'Void not available' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createCraneRequestVoid(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const voidData = inputData.body;
      voidData.isDeliveryRequest = false;
      const loginUser = inputData.user;
      const memberData = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: voidData.ProjectId,
          isDeleted: false,
        }),
      });
      if (memberData) {
        const newCraneRequest = await CraneRequest.findOne({
          where: { id: voidData.CraneRequestId },
        });
        if (newCraneRequest) {
          voidData.MemberId = memberData.id;
          const existVoid = await VoidList.findOne({
            where: Sequelize.and({
              CraneRequestId: voidData.CraneRequestId,
            }),
          });
          if (!existVoid) {
            const newVoidData = await VoidList.createInstance(voidData);
            if (newVoidData) {
              const object = {
                ProjectId: voidData.ProjectId,
                MemberId: memberData.id,
                CraneRequestId: newCraneRequest.id,
                isDeleted: false,
                type: 'void',
                description: `${loginUser.firstName} ${loginUser.lastName} Voided the Crane Booking, ${newCraneRequest.description}`,
              };
              await CraneRequestHistory.createInstance(object);
            }
            done(newVoidData, false);
          } else {
            done(null, { message: 'Crane Booking already is in void list.' });
          }
        } else {
          done(null, { message: 'Crane Booking Id Does not exist.' });
        }
      } else {
        done(null, { message: 'Project Id/Member Does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createConcreteRequestVoid(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const voidData = inputData.body;
      voidData.isDeliveryRequest = false;
      const loginUser = inputData.user;
      const memberData = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: voidData.ProjectId,
          isDeleted: false,
        }),
      });
      if (memberData) {
        const newConcreteRequest = await ConcreteRequest.findOne({
          where: { id: voidData.ConcreteRequestId, isDeleted: false },
        });
        if (newConcreteRequest) {
          voidData.MemberId = memberData.id;
          const existVoid = await VoidList.findOne({
            where: Sequelize.and({
              ConcreteRequestId: voidData.ConcreteRequestId,
            }),
          });
          if (!existVoid) {
            const newVoidData = await VoidList.createInstance(voidData);
            if (newVoidData) {
              const object = {
                ProjectId: voidData.ProjectId,
                MemberId: memberData.id,
                ConcreteRequestId: newConcreteRequest.id,
                isDeleted: false,
                type: 'void',
                description: `${loginUser.firstName} ${loginUser.lastName} Voided the Concrete Booking, ${newConcreteRequest.description}`,
              };
              await ConcreteRequestHistory.createInstance(object);
            }
            done(newVoidData, false);
          } else {
            done(null, { message: 'Concrete Booking already is in void list.' });
          }
        } else {
          done(null, { message: 'Concrete Booking Id Does not exist.' });
        }
      } else {
        done(null, { message: 'Project Id/Member Does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getVoidList(req, done) {
    try {
      const { params } = req;
      await this.getDynamicModel(req);
      const offset = (+params.pageNo - 1) * +params.pageSize;
      if (
        req.body.statusFilter === 'Tentative' ||
        req.body.statusFilter === 'Pump Confirmed' ||
        req.body.locationFilter ||
        req.body.mixDesignFilter
      ) {
        req.body.concreteSupplierFilter = req.body.companyFilter;
        await concreteRequestService.listConcreteRequest(req, async (response1, error2) => {
          if (!error2) {
            let newResult = { count: 0, rows: [] };
            const voidlist = [];
            voidlist.push(...response1.rows);
            newResult = { count: 0, rows: [] };
            newResult.rows = voidlist.slice(offset, offset + +params.pageSize);
            newResult.count = voidlist.length;
            done(newResult, false);
          }
        });
      } else {
        // await concreteRequestService.listNDR(req, async (response2, error2) => {
        //   if (!error2) {
        //     const newResult = { count: 0, rows: [] };
        //     let voidlist = [];
        //     voidlist = response2;
        //     newResult.rows = voidlist.slice(offset, offset + +params.pageSize);
        //     newResult.count = voidlist.length;
        //     done(newResult, false);
        //   }
        // });
        await craneRequestService.getVoidRequest(req, async (response, error1) => {
          if (!error1) {
            let newResult = { count: 0, rows: [] };
            const voidlist = [];
            voidlist.push(...response);
            if (
              +req.body.filterCount > 0 &&
              (req.body.gateFilter ||
                req.body.equipmentFilter ||
                req.body.statusFilter === 'Declined' ||
                req.body.status === 'Delivered' ||
                req.body.status === 'Pending')
            ) {
              newResult.rows = voidlist.slice(offset, offset + +params.pageSize);
              newResult.count = voidlist.length;
              done(newResult, false);
            } else {
              req.body.concreteSupplierFilter = req.body.companyFilter;
              await concreteRequestService.listConcreteRequest(req, async (response1, error2) => {
                if (!error2) {
                  voidlist.push(...response1.rows);
                  if (req.body.sort === 'ASC') {
                    voidlist.sort(function (a, b) {
                      // eslint-disable-next-line no-nested-ternary
                      return a[req.body.sortByField] > b[req.body.sortByField]
                        ? 1
                        : b[req.body.sortByField] > a[req.body.sortByField]
                        ? -1
                        : 0;
                    });
                  } else {
                    voidlist.sort(function (a, b) {
                      // eslint-disable-next-line no-nested-ternary
                      return b[req.body.sortByField] > a[req.body.sortByField]
                        ? 1
                        : a[req.body.sortByField] > b[req.body.sortByField]
                        ? -1
                        : 0;
                    });
                  }

                  newResult = { count: 0, rows: [] };
                  newResult.rows = voidlist.slice(offset, offset + +params.pageSize);
                  newResult.count = voidlist.length;
                  done(newResult, false);
                }
              });
            }
          }
        });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createMultipleVoidList(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const voidData = inputData.body;
      const loginUser = inputData.user;
      const memberData = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: voidData.ProjectId,
          isDeleted: false,
        }),
      });
      if (memberData) {
        for (let mainIndex = 0; mainIndex < voidData.deliveryRequestIds.length; mainIndex += 1) {
          const deliveryRequestId = voidData.deliveryRequestIds[mainIndex];
          if (deliveryRequestId) {
            const newDeliveryRequest = await DeliveryRequest.findOne({
              where: { id: deliveryRequestId },
            });
            if (newDeliveryRequest) {
              voidData.MemberId = memberData.id;
              const existVoid = await VoidList.findOne({
                where: Sequelize.and({
                  DeliveryRequestId: deliveryRequestId,
                }),
              });
              if (!existVoid) {
                const voidcreate = {
                  DeliveryRequestId: deliveryRequestId,
                  ProjectId: voidData.ProjectId,
                  ParentCompanyId: voidData.ParentCompanyId,
                };
                const newVoidData = await VoidList.createInstance(voidcreate);
                return newVoidData;
              }
              done(null, { message: 'Delivery Booking already is in void list.' });
            } else {
              done(null, { message: 'Delivery Booking Id Does not exist.' });
            }
          }
        }
      } else {
        done(null, { message: 'Project Id/Member Does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async deliveryRequestVoidHistory(existVoid, memberData, loginUser) {
    const deliveryRequest = await DeliveryRequest.findOne({
      where: {
        id: existVoid.DeliveryRequestId,
      },
    });
    const object = {
      ProjectId: existVoid.ProjectId,
      MemberId: memberData.id,
      DeliveryRequestId: existVoid.DeliveryRequestId,
      isDeleted: false,
      type: 'restore',
      description: `${loginUser.firstName} ${loginUser.lastName} Restored the Delivery Booking, ${deliveryRequest.description}`,
    };
    await DeliverHistory.createInstance(object);
  },
  async craneRequestVoidHistory(existVoid, memberData, loginUser) {
    const craneRequest = await CraneRequest.findOne({
      where: {
        id: existVoid.CraneRequestId,
      },
    });
    const object = {
      ProjectId: existVoid.ProjectId,
      MemberId: memberData.id,
      CraneRequestId: existVoid.CraneRequestId,
      isDeleted: false,
      type: 'restore',
      description: `${loginUser.firstName} ${loginUser.lastName} Restored the Crane Booking, ${craneRequest.description}`,
    };
    await CraneRequestHistory.createInstance(object);
  },
  async concreteRequestVoidHistory(existVoid, memberData, loginUser) {
    const concreteRequest = await ConcreteRequest.findOne({
      where: {
        id: existVoid.ConcreteRequestId,
      },
    });
    const object = {
      ProjectId: existVoid.ProjectId,
      MemberId: memberData.id,
      ConcreteRequestId: existVoid.ConcreteRequestId,
      isDeleted: false,
      type: 'restore',
      description: `${loginUser.firstName} ${loginUser.lastName} Restored the Concrete Booking, ${concreteRequest.description}`,
    };
    await ConcreteRequestHistory.createInstance(object);
  },
};
module.exports = voidService;
