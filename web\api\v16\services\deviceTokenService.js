const { Sequelize, Enterprise } = require('../models');
let { DeviceToken } = require('../models');
const helper = require('../helpers/domainHelper');

const { Op } = Sequelize;
let publicUser;
let publicMember;
const deviceTokenService = {
  async setDeviceToken(inputData, done) {
    try {
      const loginUser = inputData.user;
      const { deviceToken } = inputData.body;
      await this.getDynamicModel(inputData);
      const devRes = await DeviceToken.findOne({ where: { UserId: loginUser.id } });
      if (devRes) {
        const updateDevice = await DeviceToken.update(
          { deviceToken },
          { where: { id: devRes.id } },
        );
        done(updateDevice, false);
      } else {
        const data = {
          UserId: loginUser.id,
          deviceToken,
        };
        const newDevice = await DeviceToken.create(data);
        done(newDevice, false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async clearDeviceToken(inputData, done) {
    try {
      const newToken = await DeviceToken.findOne({ where: { UserId: inputData.user.id } });
      if (newToken) {
        const clearToken = await newToken.destroy();
        done(clearToken, false);
      } else {
        done('Device Token Not Found.!', false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    // publicProject = modelData.Project;
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    let enterpriseValue;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    DeviceToken = modelObj.DeviceToken;
  },
};
module.exports = deviceTokenService;
