const status = require('http-status');
const { commentService } = require('../services');

const DefineController = {
  async createComment(req, res, next) {
    try {
      await commentService.createComment(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Comment added successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getComment(req, res, next) {
    try {
      await commentService.getComment(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Comment Viewed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = DefineController;
