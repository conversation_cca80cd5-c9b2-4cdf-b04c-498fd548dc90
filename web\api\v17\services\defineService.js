const fs = require('fs');
const status = require('http-status');
const xlstojson = require('xls-to-json-lc');
const csv = require('csv-parser');
const ExcelJS = require('exceljs');
const {
  Sequelize,
  Enterprise,
  DeliverDefine,
  CraneRequestDefinableFeatureOfWork,
  CompanyDefine,
  Company,
  DeliveryRequest,
  CraneRequest,
  DeliverHistory,
  CraneRequestHistory,
} = require('../models');
let { DeliverDefineWork, Member } = require('../models');
const helper = require('../helpers/domainHelper');
// const mixpanelService = require('./mixpanelService');
const ApiError = require('../helpers/apiError');

let publicUser;
let publicMember;

const { Op } = Sequelize;

const defineService = {
  async getDefinable(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const { ProjectId } = params;
      const reqData = inputData.body;
      const pageNumber = +params.pageNo;
      const limit = +params.pageSize;
      let sortByColumnType;
      if (params.sortByField) {
        sortByColumnType = params.sortByField;
      } else {
        sortByColumnType = 'DFOW';
      }
      const offset = (pageNumber - 1) * limit;
      if (+params.export !== 1) {
        let condition;
        if (reqData.search !== '' && reqData.search !== undefined && reqData.search !== null) {
          condition = Sequelize.and({
            isDeleted: false,
            ProjectId,
            DFOW: {
              [Sequelize.Op.iLike]: `%${reqData.search}%`,
            },
          });
        } else {
          condition = Sequelize.and({ isDeleted: false, ProjectId });
        }
        const defineWork = await DeliverDefineWork.findAndCountAll({
          where: condition,
          order: [[sortByColumnType, params.sort]],
          limit,
          offset,
        });
        if (sortByColumnType === 'DFOW') {
          if (defineWork && defineWork.rows && defineWork.rows.length > 0) {
            defineWork.rows.sort((a, b) => (a.DFOW.toLowerCase() > b.DFOW.toLowerCase() ? 1 : -1));
          }
        }

        done(defineWork, false);
      } else {
        const defineWork = await DeliverDefineWork.findAll({
          where: Sequelize.and({ isDeleted: false, ProjectId }),
          order: [['DFOW', params.sort]],
          attributes: ['autoId', 'DFOW', 'Specification'],
        });
        const book = await this.exportFile(defineWork);
        done(book, false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    // publicProject = modelData.Project;
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    let enterpriseValue;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    DeliverDefineWork = modelObj.DeliverDefineWork;
    Member = modelObj.Member;
    return true;
  },
  async exportFile(defineWork) {
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'User';
    workbook.lastModifiedBy = 'User';
    workbook.views = [
      {
        x: 0,
        y: 0,
        width: 10000,
        height: 20000,
        firstSheet: 0,
        activeTab: 1,
        visibility: 'visible',
      },
    ];

    const worksheet = workbook.addWorksheet('DFOW');
    worksheet.columns = [
      { header: 'id', key: 'id', width: 5 },
      { header: 'DFOW', key: 'DFOW', width: 32 },
      { header: 'Specification', key: 'Specification', width: 32 },
    ];
    defineWork.forEach((data) => {
      worksheet.addRow({
        id: data.autoId,
        DFOW: data.DFOW,
        Specification: data.Specification,
      });
    });

    return workbook;
  },
  async updateDefinable(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { editData } = inputData.body;
      const { params } = inputData;
      const existData = [];
      const addData = [];
      editData.forEach(async (element, i) => {
        let exist;
        const condition = element.id !== undefined && element.id !== null;
        if (condition) {
          exist = await DeliverDefineWork.findOne({
            where: Sequelize.and({ id: element.id }),
          });
        }
        if (exist) {
          const existDFOW = editData.filter(
            (e) =>
              e.id !== element.id &&
              e.DFOW === element.DFOW &&
              e.Specification === element.Specification,
          );

          if (existDFOW.length === 0) {
            const newExist = await DeliverDefineWork.findAll({
              where: Sequelize.and(
                { id: { [Op.not]: element.id } },
                { DFOW: element.DFOW },
                { Specification: element.Specification },
                { ProjectId: params.ProjectId },
              ),
            });
            if (newExist.length === 0) {
              await DeliverDefineWork.update(
                { DFOW: element.DFOW, Specification: element.Specification },
                { where: { id: element.id } },
              );
            } else {
              existData.push(exist);
              newExist.forEach(async (newElement) => {
                existData.push(newElement);
              });
            }
          } else {
            existData.push(exist);
            existDFOW.forEach(async (newElement) => {
              existData.push(newElement);
            });
          }
        } else {
          const newElement = {
            dfow: element.DFOW,
            specification: element.Specification ? element.Specification : '',
          };
          addData.push(newElement);
        }
        if (editData.length - 1 === i) {
          if (addData.length > 0) {
            this.insertDefine(addData, inputData, (response, error) => {
              if (error) {
                done(null, error);
              } else if (existData.length > 0) {
                done(null, { message: 'Duplication not allowed', data: existData });
              } else {
                done({ message: 'Updated Successfully.' }, false);
              }
            });
          } else if (existData.length > 0) {
            done(null, { message: 'Duplication not allowed', data: existData });
          } else {
            done({ message: 'Updated Successfully.' }, false);
          }
        }
      });
    } catch (e) {
      done(null, e);
    }
  },
  async addDefinable(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const dfowData = inputData.body;
      const newInput = inputData;
      const { addData } = dfowData;
      const existDFOW = await DeliverDefineWork.findOne({
        where: Sequelize.and({ ProjectId: dfowData.ProjectId }, { DFOW: addData[0].DFOW }),
      });
      if (!existDFOW) {
        newInput.params.ProjectId = dfowData.ProjectId;
        addData[0].dfow = addData[0].DFOW;
        this.insertDefine(addData, newInput, (response, error) => {
          if (error) {
            done(null, error);
          } else {
            done({ message: 'Created Successfully.' }, false);
          }
        });
      } else {
        done(null, { message: 'DFOW Already exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async deleteDefinable(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { deleteData } = inputData.body;
      const reqData = inputData.body;
      let getDfow;
      if (reqData.isSelectAll) {
        getDfow = await DeliverDefineWork.findAll({
          where: { ProjectId: reqData.ProjectId, isDeleted: false },
        });
      } else {
        getDfow = await DeliverDefineWork.findAll({
          where: { ProjectId: reqData.ProjectId, isDeleted: false, id: { [Op.in]: deleteData } },
        });
      }
      if (getDfow && getDfow.length > 0) {
        getDfow.map(async (item, index) => {
          const isDfowMappedToDeliveryRequest = await DeliverDefine.findOne({
            where: {
              DeliverDefineWorkId: +item.id,
              isDeleted: false,
              ProjectId: reqData.ProjectId,
            },
          });
          const isDfowMappedToCraneRequest = await CraneRequestDefinableFeatureOfWork.findOne({
            where: {
              DeliverDefineWorkId: +item.id,
              isDeleted: false,
              ProjectId: reqData.ProjectId,
            },
          });
          const isDfowMappedToCompany = await CompanyDefine.findOne({
            where: {
              DeliverDefineWorkId: +item.id,
              isDeleted: false,
              ProjectId: reqData.ProjectId,
            },
          });
          if (isDfowMappedToDeliveryRequest) {
            return done(null, {
              message: `Definable Feature of Work ID ${item.autoId} cannot be deleted. The Definable Feature of Work is mapped to a delivery booking`,
            });
          }
          if (isDfowMappedToCraneRequest) {
            return done(null, {
              message: `Definable Feature of Work ID ${item.autoId} cannot be deleted. The Definable Feature of Work is mapped to a crane booking`,
            });
          }
          if (isDfowMappedToCompany) {
            return done(null, {
              message: `Definable Feature of Work ID ${item.autoId} cannot be deleted. The Definable Feature of Work is mapped to a company`,
            });
          }
          await DeliverDefineWork.update(
            { isDeleted: true },
            {
              where: { id: +item.id, ProjectId: reqData.ProjectId, isDeleted: false },
            },
          );
          if (index === getDfow.length - 1) {
            return done('success', false);
          }
        });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createDefinable(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { file } = inputData;
      const ProjectId = +inputData.params.ProjectId;
      const memberDetail = await Member.findOne({
        where: [
          Sequelize.and(
            {
              UserId: inputData.user.id,
              ProjectId,
              isDeleted: false,
            },
            Sequelize.or({ RoleId: [1, 2, 3] }),
          ),
        ],
      });
      if (memberDetail) {
        if (file && file.originalname) {
          const splitValue = file.originalname.split('.');
          const extension = splitValue[splitValue.length - 1];
          if (extension === 'xlsx') {
            const newWorkbook = new ExcelJS.Workbook();
            await newWorkbook.xlsx.readFile(file.path);
            const worksheet = newWorkbook.getWorksheet('data');
            const dfowArray = [];
            worksheet.eachRow(async (rowData, rowNumber) => {
              const singleRowData = rowData.values;
              if (rowNumber > 1) {
                const obj = {
                  dfow: singleRowData[3],
                  id: singleRowData[1].toString(), // Convert to string to match the desired format
                  specification: singleRowData[2],
                };
                dfowArray.push(obj);
              }
            });
            this.insertDefine(dfowArray, inputData, (resValue, error) => {
              if (!error) {
                done(resValue, false);
              } else {
                done(null, error);
              }
            });
          } else if (extension === 'csv') {
            const result = [];
            const stream = fs
              .createReadStream(file.path)
              .pipe(csv())
              .on('data', async (row) => {
                try {
                  stream.pause();
                  const data = {
                    id: row.id,
                    dfow: row.DFOW,
                    specification: row.Specification,
                  };
                  result.push(data);
                } finally {
                  stream.resume();
                }
              })
              .on('end', () => {
                this.insertDefine(result, inputData, (resValue, error) => {
                  if (!error) {
                    done(resValue, false);
                  } else {
                    done(null, error);
                  }
                });
              });
          } else if (extension === 'xls') {
            xlstojson(
              {
                input: file.path,
                output: null, // since we don't need output.json
                lowerCaseHeaders: true,
              },
              (err, result) => {
                if (err) {
                  done(null, err);
                }
                this.insertDefine(result, inputData, (resValue, error) => {
                  if (!error) {
                    done(resValue, false);
                  } else {
                    done(null, error);
                  }
                });
              },
            );
          } else {
            done(null, { message: 'Please choose valid file' });
          }
        } else {
          done(null, { message: 'Please select a file.' });
        }
      } else {
        done(null, { message: 'Project Does not exist or you are not a valid member.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async insertDefine(result, inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const existProjectId = inputData.params.ProjectId;
      const ProjectId = +existProjectId;
      let fileFormat = true;

      if (result !== undefined && result.length === 0) {
        done(null, { message: 'Please upload proper document.' });
      } else {
        const updateData = { isDeleted: false };
        if (inputData.file) {
          fs.unlinkSync(inputData.file.path);
          const keys = ['id', 'dfow', 'specification'];
          const inputKeys = Object.keys(result[0]);
          const keyArray = inputKeys.filter((e) => {
            return keys.indexOf(e) !== -1;
          });
          if (
            inputKeys.length === 2 ||
            keyArray.length === 2 ||
            inputKeys.length === 3 ||
            keyArray.length === 3
          ) {
            fileFormat = true;
          } else {
            fileFormat = false;
          }
        }
        if (fileFormat) {
          const lastIdValue = await DeliverDefineWork.findOne({
            where: { ProjectId, isDeleted: false },
            order: [['autoId', 'DESC']],
          });
          let id = 0;
          const newValue = JSON.parse(JSON.stringify(lastIdValue));
          if (newValue && newValue.autoId !== null && newValue.autoId !== undefined) {
            id = +newValue.autoId;
          }
          /* eslint-disable no-restricted-syntax */
          /* eslint-disable no-await-in-loop */
          for (const [i, element] of result.entries()) {
            if (element.dfow) {
              const findDFOWByAttributes = [];
              findDFOWByAttributes.push({ ProjectId });
              let Specification;
              const DFOW = element.dfow;
              findDFOWByAttributes.push({ DFOW });
              if (element.specification) {
                Specification = element.specification;
                findDFOWByAttributes.push({ Specification });
              }
              const existValue = await DeliverDefineWork.findOne({
                where: {
                  [Op.and]: findDFOWByAttributes,
                },
              });
              if (!existValue) {
                id += 1;
                const data = {
                  DFOW,
                  Specification,
                  autoId: id,
                  ProjectId,
                  isDeleted: false,
                };
                delete data.id;
                await DeliverDefineWork.createInstance(data);
              } else if (existValue.isDeleted === true) {
                id += 1;
                updateData.autoId = id;
                await DeliverDefineWork.update(updateData, {
                  where: { id: existValue.id },
                });
              } else if (existValue && result.length === 1) {
                const duplicationRecords = [];
                duplicationRecords.push(result);
                duplicationRecords.push(existValue);
                done(null, { message: 'Duplication not allowed', data: duplicationRecords });
              }
            }
            if (result.length - 1 === i) {
              done({ message: 'created' }, false);
            }
          }
        } else {
          done(null, { message: 'Invalid File Format' });
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async lastDefineId(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      let data;
      const lastData = await DeliverDefineWork.findOne({
        where: { ProjectId: inputData.params.ProjectId, isDeleted: false },
        order: [['autoId', 'DESC']],
      });
      if (lastData) {
        data = lastData.autoId + 1;
      } else {
        data = 1;
      }
      done({ id: data }, false);
    } catch (e) {
      done(null, e);
    }
  },
};
module.exports = defineService;
