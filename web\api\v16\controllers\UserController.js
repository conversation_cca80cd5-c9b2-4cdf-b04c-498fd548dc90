const status = require('http-status');
const bcrypt = require('bcrypt');
const ApiError = require('../helpers/apiError');
const { UserSerializer } = require('../serializers');
let { User } = require('../models');
const { memberService } = require('../services');
const { bcryptPassword } = require('../services/password');
const enterpriseCheck = require('../helpers/enterpriseCheckHelper');
const helper = require('../helpers/domainHelper');
const { Sequelize, Member, Role } = require('../models');

const { Op } = Sequelize;
const UserController = {
  /**
   * Returns user if given user id is valid
   * @params {header: JWT 'token'}
   * @returns { User }
   */
  async show(req, res) {
    const user = await User.getBy({});
    res.status(status.OK).json(UserSerializer.serialize(user));
  },
  /**
   * @params {header: JWT 'token'}
   * @returns { User }
   */
  async userLists(req, res) {
    const commonSearch = {
      [Op.and]: {
        isDeleted: false,
        userType: 'user',
      },
    };
    const users = await User.findAll({ where: commonSearch, attributes: ['id', 'email'] });
    res.status(status.OK).json({ data: users });
  },
  /**
   * Returns user if given user email is exists
   * @params {header: JWT 'token'}
   * @returns { User }
   */
  async checkUser(req, res) {
    // await memberService.getDynamicModel(req);
    let isMemberExists = false;
    const user = await User.getBy({ email: req.body.email });
    const existUser = await User.findOne({
      where: {
        [Op.and]: [
          {
            isDeleted: false,
            [Op.and]: Sequelize.and(
              Sequelize.where(
                Sequelize.fn('lower', Sequelize.col('email')),
                Sequelize.fn('lower', req.body.email),
              ),
            ),
          },
        ],
      },
    });
    if (existUser) {
      const existMember = await Member.findOne({
        where: Sequelize.and({
          UserId: existUser.id,
          isDeleted: false,
          ProjectId: req.body.ProjectId,
        }),
      });
      if (existMember) {
        isMemberExists = true;
      }
    }
    const response = user || {};
    res.status(status.OK).json({ response, isMemberExists });
  },
  /**
   * update existing user
   * @params {header: JWT 'token'}
   * @params {string} req.body.name user name
   * @params {string} req.body.phone phone number
   * @returns {User}
   */
  async update(req, res, next) {
    let user = await User.getBy({ id: req.user.id });

    try {
      user = await user.update(req.body);
      const serializedUser = (await UserSerializer.serialize(user)) || {};
      res.status(status.OK).json({ user: serializedUser });
    } catch (err) {
      next(err);
    }
  },
  async uploadProfile(req, res, next) {
    try {
      await memberService.uploadProfile(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Uploaded Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async changePassword(req, res, next) {
    const domainValue = await enterpriseCheck.checkEnterPrise(req);
    const modelObj = await helper.getDynamicModel(domainValue);
    User = modelObj.User;
    let user = await User.getBy({ id: req.user.id });
    const encryptNewPassword = await bcrypt.compare(req.body.newPassword, user.password);
    let password;
    try {
      const verifyPassword = await bcrypt.compare(req.body.oldPassword, user.password);
      if (!verifyPassword) {
        const error = new ApiError('Please enter old password', status.BAD_REQUEST);
        next(error);
      } else {
        if (user.userType === 'super admin' && encryptNewPassword) {
          res.status(status.BAD_REQUEST).json({
            message: 'Current & New password should not be same',
          });
        }
        await bcryptPassword(req.body.newPassword, (encPassword) => {
          password = encPassword;
        });
        user = await user.update({ password });
        const serializedUser = (await UserSerializer.serialize(user)) || {};
        res
          .status(status.OK)
          .json({ message: 'Password Updated Successfully.', user: serializedUser });
      }
    } catch (err) {
      next(err);
    }
  },
  async isAuthenticatedUser(req, res, next) {
    if (req.user) {
      const UserData = await Member.findOne({
        where: { UserId: req.user.id, isDeleted: false },
      });
      const user = UserSerializer.serialize(req.user);
      if (user && user.roleId) {
        user.roleId = UserData.RoleId;
      }
      res.status(status.OK).json(user);
    } else {
      const error = new ApiError('Please Log in', status.BAD_REQUEST);
      next(error);
    }
  },
  async superAdminDetails(req, res, next) {
    if (req.user) {
      const { user } = req;
      const role = await Role.findOne({ where: { roleName: 'Super Admin' } });
      await res.status(status.OK).json({ user, role });
    } else {
      const error = new ApiError('Please Log in', status.BAD_REQUEST);
      next(error);
    }
  },
  async updateAdminProfile(req, res, next) {
    const { body } = req;
    const { user } = req;
    if (req.user) {
      await User.update(
        {
          firstName: body.firstName,
          phoneNumber: body.phoneNumber,
          phoneCode: body.phoneCode,
          lastName: body.lastName,
        },
        {
          where: {
            id: user.id,
          },
        },
      );
      await res.status(200).json({ message: 'Profile updated successfully.' });
    } else {
      const error = new ApiError('Please Log in', status.BAD_REQUEST);
      next(error);
    }
  },
};

module.exports = UserController;
