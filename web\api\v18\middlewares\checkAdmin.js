const status = require('http-status');
const Sequelize = require('sequelize');
const ApiError = require('../helpers/apiError');
const helper = require('../helpers/domainHelper');
const enterpriseHelper = require('../helpers/enterpriseCheckHelper');
const { sequelize } = require('../models');
let { User, Member } = require('../models');

async function getDynamicModel(inputData) {
  const incomeData = inputData;
  const domainName = await enterpriseHelper.checkEnterPrise(inputData);
  const modelObj = await helper.getDynamicModel(domainName);
  Member = modelObj.Member;
  User = modelObj.User;
  const newUser = await User.findOne({ where: { email: inputData.user.email, isDeleted: false } });
  incomeData.user = newUser;
}

const isAdmin = async (req, res, next) => {
  await getDynamicModel(req);
  const checkadmin = await User.findOne({
    where: Sequelize.and(
      {
        id: req.user.id,
      },
      Sequelize.or({
        userType: ['super admin', 'folloit admin'],
      }),
    ),
  });
  if (checkadmin) {
    next();
  } else {
    const error = new ApiError('Access Forbidden', status.FORBIDDEN);
    next(error);
  }
};

async function checkProjectAdmin(ProjectId, UserId, inputData, callback) {
  await getDynamicModel(inputData);
  const existMember = await Member.findOne({
    where: sequelize.and({ ProjectId: +ProjectId, UserId, isDeleted: false }),
  });
  if (existMember) {
    if (existMember.RoleId === 2 || existMember.RoleId === 1 || existMember.RoleId === 3) {
      callback({ status: true }, false);
    } else {
      const error = new ApiError('Access Forbidden', status.FORBIDDEN);
      callback(null, error);
    }
  } else {
    const error = new ApiError('Access Forbidden', status.FORBIDDEN);
    callback(null, error);
  }
}

const isProjectAdminOnly = async (req, res, next) => {
  await getDynamicModel(req);
  const { query } = req;
  const loginUser = req.user;
  if (query !== undefined) {
    if (query.ProjectId !== undefined && query.ProjectId !== null) {
      checkProjectAdmin(query.ProjectId, loginUser.id, req, (result, err) => {
        if (result) {
          next();
        } else {
          next(err);
        }
      });
    }
  }
};

const isAccountAdmin = async (req, res, next) => {
  await getDynamicModel(req);
  // const data = await User.findOne({ where: { id: req.user.id, isAccount: true } });
  const data = await User.findOne({ where: { id: req.user.id, isDeleted: false } });
  if (data) {
    next();
  } else {
    const error = new ApiError('Access Forbidden', status.FORBIDDEN);
    next(error);
  }
};

const isProjectAdmin = async (req, res, next) => {
  await getDynamicModel(req);
  const param = req.params;
  const input = req.body;
  const loginUser = req.user;
  if (param !== undefined) {
    if (param.ProjectId !== undefined && param.ProjectId !== null) {
      checkProjectAdmin(param.ProjectId, loginUser.id, req, (result, err) => {
        if (result) {
          next();
        } else {
          next(err);
        }
      });
    } else {
      checkProjectAdmin(input.ProjectId, loginUser.id, req, (result, err) => {
        if (result) {
          next();
        } else {
          next(err);
        }
      });
    }
  } else {
    checkProjectAdmin(input.ProjectId, loginUser.id, req, (result, err) => {
      if (result) {
        next();
      } else {
        next(err);
      }
    });
  }
};

module.exports = { isAdmin, isProjectAdmin, isAccountAdmin, isProjectAdminOnly };
