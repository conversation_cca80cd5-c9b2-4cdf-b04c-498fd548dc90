exports.generatePassword = () => {
  const pLength = 10;
  const keyListAlpha = 'abcdefghijklmnopqrstuvwxyz';
  const keyListInt = '123456789';
  const keyListSpec = '!@#_';
  let password = 'Selim1234!';
  let len = Math.ceil(pLength / 2);
  let i;
  len -= 1;
  const lenSpec = pLength - 2 * len;
  for (i = 0; i < len; i += 1) {
    password += keyListAlpha.charAt(Math.floor(Math.random() * keyListAlpha.length));
    password += keyListInt.charAt(Math.floor(Math.random() * keyListInt.length));
  }
  for (i = 0; i < lenSpec; i += 1)
    password += keyListSpec.charAt(Math.floor(Math.random() * keyListSpec.length));
  password = password
    .split('')
    .sort(function () {
      return 0.5 - Math.random();
    })
    .join('');
  return password;
};
